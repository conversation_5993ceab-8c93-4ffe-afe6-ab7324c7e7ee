import asyncio
import csv
from typing import List, Dict, Any
import aiohttp
import structlog

logger = structlog.stdlib.get_logger(__name__)

# TODO: Replace with actual API base URL
API_BASE_URL = "https://your-api-base-url.com/api/1.0"
PAGE_SIZE = 100


async def fetch_page(session: aiohttp.ClientSession, page_number: int) -> List[Dict[str, Any]]:
    """Fetch a single page of resource_cd's."""
    params = {
        "limit": PAGE_SIZE,
        "offset": page_number * PAGE_SIZE,
    }

    url = f"{API_BASE_URL}/resources"

    async with session.get(url, params=params) as response:
        response.raise_for_status()
        data = await response.json()
        return data


async def find_total_pages(session: aiohttp.ClientSession) -> int:
    """Use binary search to find the total number of pages."""
    logger.info("Starting binary search to find total pages")

    # Start with page 100 as suggested
    high = 100
    low = 0
    last_non_empty_page = 0

    # First, find an upper bound by doubling until we get an empty page
    while True:
        try:
            data = await fetch_page(session, high)
            if not data:  # Empty page found
                break
            last_non_empty_page = high
            high *= 2
            logger.info(f"Page {last_non_empty_page} has data, trying page {high}")
        except Exception as e:
            logger.error(f"Error fetching page {high}: {e}")
            break

    # Now binary search between last_non_empty_page and high
    low = last_non_empty_page

    while low < high:
        mid = (low + high) // 2
        try:
            data = await fetch_page(session, mid)
            if data:  # Non-empty page
                last_non_empty_page = mid
                low = mid + 1
            else:  # Empty page
                high = mid
        except Exception as e:
            logger.error(f"Error fetching page {mid}: {e}")
            high = mid

    total_pages = last_non_empty_page + 1
    logger.info(f"Found total pages: {total_pages}")
    return total_pages


async def fetch_all_pages(session: aiohttp.ClientSession, total_pages: int) -> List[tuple[str, int]]:
    """Fetch all pages concurrently and return resource_cd's with their page numbers."""
    logger.info(f"Fetching {total_pages} pages concurrently")

    # Create tasks for all pages
    tasks = []
    for page_num in range(total_pages):
        task = asyncio.create_task(fetch_page(session, page_num))
        tasks.append((task, page_num))

    # Execute all tasks concurrently and collect results
    results = []
    completed_tasks = await asyncio.gather(*[task for task, _ in tasks], return_exceptions=True)

    for (task, page_num), result in zip(tasks, completed_tasks):
        if isinstance(result, Exception):
            logger.error(f"Error fetching page {page_num}: {result}")
            continue

        try:
            for item in result:
                resource_cd = item["resource_cd"]
                results.append((resource_cd, page_num))
        except Exception as e:
            logger.error(f"Error processing page {page_num} data: {e}")

    logger.info(f"Fetched {len(results)} resource_cd's total")
    return results


async def main():
    """Main function to orchestrate the fetching and CSV writing."""
    logger.info("Starting resource_cd fetching script")

    timeout = aiohttp.ClientTimeout(total=30)

    async with aiohttp.ClientSession(
        timeout=timeout,
        headers={"Content-Type": "application/json"}
    ) as session:

        # Step 1: Find total number of pages using binary search
        total_pages = await find_total_pages(session)

        if total_pages == 0:
            logger.warning("No pages found")
            return

        # Step 2: Fetch all pages concurrently
        resource_data = await fetch_all_pages(session, total_pages)

        # Step 3: Write to CSV file
        csv_filename = "resource_cds.csv"
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(["resource_cd", "page_number"])

            for resource_cd, page_num in resource_data:
                writer.writerow([resource_cd, page_num])

        logger.info(f"Wrote {len(resource_data)} resource_cd's to {csv_filename}")


if __name__ == "__main__":
    # Configure logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    asyncio.run(main())