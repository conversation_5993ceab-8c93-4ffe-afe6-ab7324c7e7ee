import csv
import itertools
from ssl import create_default_context
from typing import Dict, Any

from httpx import Client, HTTPTransport
from sqlalchemy import Column, Integer, String, create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

JsonDict = Dict[str, Any]


class ResourceProviderClient:
    _DEFAULT_PAGE_SIZE = 100

    def __init__(
        self,
        base_url: str,
        timeout: int = 30,
        retries: int = 3,
        ssl_verify: bool = True,
    ):
        self.base_url = base_url
        transport = HTTPTransport(
            verify=ssl_verify and create_default_context(),
            retries=retries,
        )
        self._http = Client(
            transport=transport,
            base_url=base_url,
            headers={"Content-Type": "application/json"},
            follow_redirects=True,
            timeout=timeout,
        )

    def __enter__(self):
        self._http.__enter__()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self._http.__exit__(exc_type, exc_value, traceback)

    def _fetch_resource_cds_page(self, page_number: int) -> JsonDict:
        params = {
            "limit": self._DEFAULT_PAGE_SIZE,
            "offset": page_number * self._DEFAULT_PAGE_SIZE,
        }
        print(f"Fetching page {page_number} with offset {params['offset']}")
        response = self._http.get(url="/resources", params=params)
        response.raise_for_status()
        return response.json()

    def fetch_all_resource_cds_with_pages(self, provider_name: str):
        """Fetch all resource_cds with their page numbers"""
        results = []

        for current_page in itertools.count():
            payload = self._fetch_resource_cds_page(current_page)
            n_resource_cds = len(payload)
            print(
                f"[{provider_name}] Page {current_page}: fetched {n_resource_cds} resource_cds"
            )

            for item in payload:
                results.append(
                    {
                        "provider_name": provider_name,
                        "resource_cd": item["resource_cd"],
                        "page_number": current_page,
                    }
                )

            if n_resource_cds < self._DEFAULT_PAGE_SIZE:
                print(f"[{provider_name}] Last page reached (page {current_page})")
                break

        return results


def main():
    # TODO: Replace with actual API base URLs and provider names
    base_urls = [
        ("CEH_PROVIDER", "https://develop-ceh.dtpl.corp.dev.vtb/api/0.6/"),
        ("UNI_PROVIDER", "https://develop-uni.dtpl.corp.dev.vtb/api/1.0/"),
        # Add more providers as needed
    ]

    all_resource_cds_data = []

    for provider_name, base_url in base_urls:
        print(f"\nStarting to fetch resource_cds from {provider_name} ({base_url})")

        try:
            with ResourceProviderClient(base_url, ssl_verify=False) as client:
                provider_data = client.fetch_all_resource_cds_with_pages(provider_name)
                all_resource_cds_data.extend(provider_data)
                print(f"[{provider_name}] Fetched {len(provider_data)} resource_cds")
        except Exception as e:
            print(f"[{provider_name}] Error fetching data: {e}")
            continue

    print(
        f"\nTotal resource_cds fetched from all providers: {len(all_resource_cds_data)}"
    )

    # Write to CSV file
    csv_filename = "resource_cds.csv"
    with open(csv_filename, "w", newline="", encoding="utf-8") as csvfile:
        fieldnames = ["provider_name", "page_number", "resource_cd"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for row in all_resource_cds_data:
            writer.writerow(row)

    print(f"Results saved to {csv_filename}")

    # Print some statistics
    if all_resource_cds_data:
        # Statistics by provider
        provider_stats = {}
        for row in all_resource_cds_data:
            provider = row["provider_name"]
            if provider not in provider_stats:
                provider_stats[provider] = {"count": 0, "max_page": -1}
            provider_stats[provider]["count"] += 1
            provider_stats[provider]["max_page"] = max(
                provider_stats[provider]["max_page"], row["page_number"]
            )

        print("\nStatistics by provider:")
        for provider, stats in provider_stats.items():
            print(
                f"  {provider}: {stats['count']} resource_cds, {stats['max_page'] + 1} pages (0-{stats['max_page']})"
            )

        # Show first few and last few entries
        print("\nFirst 5 entries:")
        for i, row in enumerate(all_resource_cds_data[:5]):
            print(
                f"  {i+1}. [{row['provider_name']}] {row['resource_cd']} (page {row['page_number']})"
            )

        if len(all_resource_cds_data) > 5:
            print("\nLast 5 entries:")
            for i, row in enumerate(
                all_resource_cds_data[-5:], len(all_resource_cds_data) - 4
            ):
                print(
                    f"  {i}. [{row['provider_name']}] {row['resource_cd']} (page {row['page_number']})"
                )


if __name__ == "__main__":
    main()
