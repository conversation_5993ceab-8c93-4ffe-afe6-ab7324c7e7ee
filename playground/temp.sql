WITH
    resource_to_dataset AS (
        SELECT
            resource_cd
          , delta.definition ->> 'connections' AS connections_template
          , CASE delta.data_action_type_rk
                WHEN :item_added THEN FALSE
                WHEN :item_modified THEN FALSE
                WHEN :item_deleted THEN TRUE
            END AS marked_as_deleted
          , dataset ->> 'name' AS table_name
          , dataset ->> 'schema_name' AS schema_name
        FROM
            "{stage_table}" AS delta
            CROSS JOIN LATERAL
                json_array_elements(
                    delta.definition -> 'datasets'
                ) AS dataset
    )
  , delta AS (
        SELECT
            resource.resource_rk
          , table_.table_rk
          , marked_as_deleted
          , connections_template
        FROM
            resource_to_dataset AS delta
            INNER JOIN (
                SELECT
                    resource_rk
                  , resource_cd
                FROM metamodel.bridge_resource
                WHERE effective_to_dttm = :effective_to_dttm
            ) AS resource
                ON delta.resource_cd = resource.resource_cd
            INNER JOIN (
                SELECT
                    table_rk
                  , table_name
                  , schema_name
                FROM metamodel.bridge_table
                WHERE effective_to_dttm = :effective_to_dttm
            ) AS table_ ON delta.table_name = table_.table_name
                AND delta.schema_name = table_.schema_name
    )
  , update_deleted AS (
    -- @fmt:off
        UPDATE metamodel.link_resource_table AS link
        SET effective_to_dttm = :effective_from_dttm
        FROM delta
        WHERE
            delta.marked_as_deleted
            AND link.resource_rk = delta.resource_rk
            AND link.table_rk = delta.table_rk
            AND link.effective_to_dttm = :effective_to_dttm
            AND link.deleted_flg IS FALSE
        RETURNING
            link.resource_rk
          , link.table_rk
          , link.connect_string
    -- @fmt:on
    )
INSERT
INTO metamodel.link_resource_table ( resource_rk
                                   , table_rk
                                   , connect_string
                                   , effective_from_dttm
                                   , effective_to_dttm
                                   , deleted_flg)
SELECT
    resource_rk
  , table_rk
  , connect_string
  , :effective_from_dttm
  , :effective_to_dttm
  , TRUE AS deleted_flg
FROM update_deleted

UNION ALL
SELECT
    resource_rk
  , table_rk
  , connections_template
  , :effective_from_dttm
  , :effective_to_dttm
  , FALSE AS deleted_flg
FROM delta
WHERE
    NOT delta.marked_as_deleted AND
    NOT exists (
        SELECT
        FROM metamodel.link_resource_table AS link
        WHERE
            link.resource_rk = delta.resource_rk AND
            link.table_rk = delta.table_rk AND
            link.effective_to_dttm = :effective_to_dttm AND
            link.deleted_flg IS FALSE
    )
