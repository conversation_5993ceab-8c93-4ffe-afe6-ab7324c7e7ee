schema_version: 2.0
metadata:
  author: <PERSON><PERSON><PERSON>. (4072399)
  version: "1.4-u" # version of your sql prototype
  description: Управляющий поток cd_customer_segment_ind
  tags:
    - cf
    - 'team: rb-reporting-12'
    - 'src: ceh'
    - 'sstm: 1480_43'
    - 'sstm_nm: dtpl_cagm'
    - 'strm: ФД-204'
    - 'tgt: dm_pik_fd_cagm_tech.cd_customer_segment_ind'
    - 'tgt-sch: dm_pik_fd_cagm'
    - 'tgt-obj: cd_customer_segment_ind'
    - 'wf: wf_dm_pik_fd_cagm_tech_cd_customer_segment_ind'
  group: rbreporting
flows:
  - id: cf_dm_pik_fd_cagm_cd_customer_segment_ind
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Управляющий поток для дага wf_dm_pik_fd_cagm_tech_cd_customer_segment_ind
    metadata:
      - name: trigger_dag_id
        datatype: str
        type: in
        default: wf_dm_pik_fd_cagm_tech_cd_customer_segment_ind #name of your workflow dag
      - name: target_table_name
        datatype: str
        type: in
        default: dm_pik_fd_cagm_tech.cd_customer_segment_ind
      - name: target_resource_name
        datatype: str
        type: in
        default: ceh.dm_pik_fd_cagm_tech.cd_customer_segment_ind  #было ceh.dm_pik_fd_cagm.cd_customer_segment_ind
      - name: source_resource_names
        datatype: list
        type: in
        default:         
          - #dr+ resources
            - ceh.idl.bbridge_counterparty  #было ceh.bdm.counterparty
            - ceh.idl.bbridge_counterparty_service_status
            - ceh.idl.blink_counterparty_x_department    #было ceh.idl.counterparty_x_department
            - ceh.idl.bbridge_department   #было ceh.bdm.department
            - ceh.dm_pik_fd_cagm_tech.tech_cust_activity_ind  #было ceh.dm_pik_fd_cagm.tech_cust_activity_ind 
            - ceh.idl.blink_counterparty_x_uniq_counterparty  #было ceh.bdm.counterparty_x_uniq_counterparty
          - #dev resources
            - ceh.bdm.counterparty
            - ceh.idl.bbridge_counterparty_service_status
            - ceh.idl.department           
            - ceh.dm_pik_fd_cagm_tech.tech_cust_activity_ind         
      - name: report_dt_from
        datatype: str
        type: in
        default: "null" # provide date in 'yyyy-mm-dd' format to compute nearest report range in the past
      - name: report_dt_to
        datatype: str
        type: in
        default: "null" # provide date in 'yyyy-mm-dd' format to compute nearest report range in the past
      - name: interval_value
        datatype: str
        type: in
        default: month # day | month , period of mart calculation
      - name: algorithm_uid
        datatype: str
        type: in
        default: '01.RBREP.wf_dm_pik_fd_cagm_tech_cd_customer_segment_ind.0.0' # check out that in wf code there is same name
      - name: snapshot_flg
        datatype: bool
        type: in
        default: true # false if you are loading with increment
      - name: force_flg
        datatype: bool
        type: in
        default: true # false to skip calculated report_dt in report_dt_from..report_dt_to interval
      - name: get_max_loaded_version_condition
        datatype: str
        type: in
        default: any # any | all | overlook
      - name: timeout_duration
        type: in
        datatype: int
        default: 72000
        #add your custom parameters here       
      #- name: scheme 
      #  datatype: str
      #  type: in
      #  default: dm_pik_fd_cagm
      - name: dev_run
        type: in
        datatype: int
        default: 0  # 0 - if run on DR-RR+ env, 1 on DEV
      #- name: psi_suffix  
      #  datatype: str
      #  type: in
      #  default: ""
      - name: source_table_list
        type: in 
        datatype: dict

    tasks:
      - id: check_dag_running
        type: ceh_core_idl.app.operators.core.check_dag_running
        description: Проверка на то, что этот cf даг не запущен.

      - id: check_available_resources_in_ceh_provider
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        description: Проверить доступность ресурсов
        properties:
          target_resource_name: ${target_resource_name}
          source_resource_names: ${source_resource_names[dev_run]}
        sequencer:
          dependencies:
            - ref: check_dag_running

      - id: get_common_version
        type: ceh_core_idl.app.operators.services.common_version_operator
        description: Получить стабильную версию (минимальную загруженную из последних на источниках)
        metadata:
          - name: common_version
            datatype: int
            type: out
        properties:
          resources: ${source_resource_names[dev_run]}
        sequencer:
          dependencies:
            - ref: check_available_resources_in_ceh_provider

      - id: get_max_loaded_version
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        description: Рассчитать максимальную загруженную версию на приёмнике
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_cd: ${target_resource_name}
          source_resource_cds: ${source_resource_names[dev_run]}
          common_version_id: ${get_common_version.common_version}
          condition: ${get_max_loaded_version_condition}
          algorithm_uid: ${algorithm_uid}
        sequencer:
          dependencies:
            - ref: get_common_version

      - id: get_workflow_parameters
        type: ceh_core_idl.app.operators.basic.workflow_parameters_operator
        description: Сформировать параметры для рабочего потока
        metadata:
          - name: instance_id
            datatype: int
            type: out
          - name: src_version_id
            datatype: int
            type: out
          - name: stable_version_id
            datatype: int
            type: out
        properties:
          version_parameters:
            - ${get_max_loaded_version}
          stable_version: ${get_common_version.common_version}
        sequencer:
          dependencies:
            - ref: get_max_loaded_version
          on_false_condition: skip  

      - id: get_date
        type: ceh_core_idl.app.operators.core.sql_operator
        description: Получить даты с шагом ${interval_value} для расчета, которые еще
          не считались; вычисляется последняя отчётная дата (календарно) в каждом
          диапазоне; по умолчанию берётся последний прошедшй расчётный период
        metadata:
          - name: result
            datatype: dict
            type: out
        properties:
          query: |-
            select '''' || t1.report_dt || '''' as report_dt
            from ( 
              select (date_trunc('${interval_value}', t.report_dt::date) + interval '1 ${interval_value}' - interval '1 day')::date as report_dt 
              from generate_series(
              date_trunc('${interval_value}',coalesce(${report_dt_from},(date_trunc('${interval_value}', current_date)-interval '1 day'))::date)::timestamp, 
              date_trunc('${interval_value}',coalesce(${report_dt_to},(date_trunc('${interval_value}', current_date)-interval '1 day'))::date)::timestamp,
              interval  '1 ${interval_value}') AS t(report_dt) 
            ) t1 
            left join ( 
              select report_dt 
              from ${target_table_name}
              where deleted_flg=false
              group by report_dt 
            ) as t2 
            on t1.report_dt=t2.report_dt
            where 1=1
              and (t2.report_dt is null or ${force_flg}=true) 
              and t1.report_dt between coalesce(${report_dt_from},(date_trunc('${interval_value}', current_date)-interval '1 day'))::date and coalesce(${report_dt_to},(date_trunc('${interval_value}', current_date)-interval '1 day'))::date 
            order by t1.report_dt
          result_flg: true
          result_limit: 1000
        sequencer:
          dependencies:
            - ref: get_workflow_parameters

      - id: transform_report_dt_list
        type: ceh_core_idl.app.operators.core.json_query_operator
        description: Преобразовать sql в json с нужной структурой для итерирования по списку
        properties:
          input_data: ${get_date.result.response}
          jq_expression: 'map(. |= {"report_dt": .report_dt})[]'
          fetch_type: all
        sequencer:
          dependencies:
            - ref: get_date
            
      - id: get_target_table_name_split
        description: Разделяем target_table_name таблицу+корневую схему (без _tech, _work итд)
        type: ceh_core_idl.app.operators.core.sql_operator
        metadata:
          - name: load_id
            datatype: str
            type: out
        properties:
            query: |-
              select 
                regexp_replace(
                  split_part('${target_table_name}','.',1),
                  '(_tech\M|_work\M|_in\M|_out\M|_dlt\M)',
                  '',
                  'gi'
                ) as schema_name
                ,split_part('${target_table_name}','.',2) as table_name              
            result_flg: true
        sequencer:
            dependencies:
              - ref: transform_report_dt_list      

      - id: launch_loop_dags
        type: ceh_core_idl.app.operators.core.sequential_trigger_dag_operator
        description: Запуск нескольких дагов и проверка их состояния
        properties:
          trigger_dag_id: ${trigger_dag_id}
          number_dags: ${transform_report_dt_list.jq_response | length}
          conf: ${transform_report_dt_list.jq_response}
          common_conf:
            #target_table_name: ${target_table_name}
            target_table_name: ${get_target_table_name_split.result.response[0].table_name}
            target_full_table_name: ${get_target_table_name_split.result.response[0].schema_name}_tech.${get_target_table_name_split.result.response[0].table_name}
            target_root_schema_name: ${get_target_table_name_split.result.response[0].schema_name}
            target_resource_name: ${target_resource_name}
            instance_id: ${get_workflow_parameters.instance_id}
            snapshot_flg: ${snapshot_flg}
            algorithm_uid: ${algorithm_uid}
            src_version_id: ${get_workflow_parameters.src_version_id}
            stable_version_id: ${get_workflow_parameters.stable_version_id}
            timeout_duration: ${timeout_duration}            
            #scheme: ${scheme}
            #psi_suffix: ${psi_suffix}
            dev_run: ${dev_run}
            
          wait_for_completion: true
        sequencer:
          dependencies:
            #- ref: transform_report_dt_list
            - ref: get_target_table_name_split
            - ref: get_workflow_parameters
