{{- if and ((.Values.url).sq) (eq .Values.mode "main") }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: srv-sq-generator-back
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ default .Values.replicaCount (($.Values.services.sqBack).replica) }}
  selector:
    matchLabels:
      app: sq-app-back
  strategy:
    type: Recreate
    # recreateParams: 
    #   pre: {} 
    #   mid: {}
    #   post: {}
  template:
    metadata:
      labels:
        app: sq-app-back
    spec:
      containers:
      - image: "{{ .Values.repo.server }}/{{ .Values.repo.suffix }}/srv-provider:{{ .Values.services.version }}"
        name: sq-generator-back
        command: ["bash", "-c", "/logs/run.sh sequence_generator_back"]
        ports:
        - containerPort: 6067
        envFrom:
        - configMapRef:
            name: dtpl-cm-srv-common
        - configMapRef:
            name: dtpl-cm-srv-kf-common
        - configMapRef:
            name: dtpl-cm-srv-adb
        env:
        {{- if hasKey .Values.services "sqBack" }}
        {{- if hasKey .Values.services.sqBack "addEnv" }}
        {{- range $key, $value := .Values.services.sqBack.addEnv }}
        - name: {{ $key | upper }}
          value: {{ $value | quote }}
        {{- end }}
        {{- end }}
        {{- end }}
        readinessProbe:
          exec:
            command:
            - ls
            - /logs/
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
          - name: fluent-config
            mountPath: /logs/run.sh
            subPath: run.sh
          {{- if hasKey $.Values.kafka "tls" }}
          - name: kf-tls
            mountPath: /kf-tls/
          {{- end }}
          - name: logs
            mountPath: /logs/
        resources:
          {{- $LIMITS := default $.Values.resources ($.Values.services.sqBack) }}
          {{- with $LIMITS }}
          limits:
            cpu: {{ .limits.cpu | quote }}
            memory: {{ .limits.memory | quote }}
          requests:
            cpu: {{ .requests.cpu | quote }}
            memory: {{ .requests.memory | quote }}
          {{- end }}  
      - image: "{{ .Values.repo.server }}/{{ .Values.repo.suffix }}/srv-fluentbit:{{ .Values.services.version }}"
        command: ["sh", "-c", "/logs/run_fluent.sh"]
        name: sq-fluent
        env:
        - name: ES_PASS
          valueFrom:
            secretKeyRef:
              name: dtpl-secret-srv-common
              key: EFK_ES_PASSWORD
        readinessProbe:
          exec:
            command:
            - ls
            - /logs/
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
          {{- if hasKey $.Values.kafkaLogs "tls" }}
          - name: kf-tls
            mountPath: /kf-tls/
          {{- end }}
          - name: fluent-config
            mountPath: /logs/run_fluent.sh
            subPath: run_fluent.sh
          - name: fluent-config
            mountPath: /fluent-bit/etc/fluent-bit.conf
            subPath: fluent-bit.conf
          - name: logs
            mountPath: /logs/
        resources:
          {{- $LIMITS := default $.Values.resources ($.Values.services.fluent) }}
          {{- with $LIMITS }}
          limits:
            cpu: {{ .limits.cpu | quote }}
            memory: {{ .limits.memory | quote }}
          requests:
            cpu: {{ .requests.cpu | quote }}
            memory: {{ .requests.memory | quote }}
          {{- end }}   
      volumes:
      {{- if hasKey $.Values.kafka "tls" }}
      - name: kf-tls
        secret:
          secretName: dtpl-secret-srv-cert-kf
          defaultMode: 0400
      {{- end }}
      - name: logs
        emptyDir: {}
      - name: fluent-config
        configMap:
          name: dtpl-cm-srv-fluent-files
          items:
          - key: run.sh
            path: run.sh
            mode: 0755
          - key: run_fluent.sh
            path: run_fluent.sh
            mode: 0755
          - key: fluent-bit.conf
            path: fluent-bit.conf
      imagePullSecrets:
      - name: dtpl-secret-srv-repo
---
apiVersion: v1
kind: Service
metadata:
  name: sq-service-back
  namespace: {{ .Values.namespace }}
spec:
  selector:
    app: sq-app-back
  ports:
    - protocol: TCP
      port: 80
      targetPort: 6067
...
{{- end }}