{{- if .Values.disasterRecovery }}
{{- $namespace := .Values.namespace }}
{{- if eq .Values.mode "dr" }}
  {{- $namespace = .Values.disasterRecovery.namespace }}
{{- end }}
{{- if hasKey .Values.adb "tls" }}
{{/*- if or (not .Values.kafka.tls.clcrt) (not .Values.kafka.tls.clkey) */}}
apiVersion: acm.clearwayintegration.com/v1beta1
kind: AutoCertificate
metadata:
  name: dtpl-ac-srv-adb
  namespace: {{ $namespace }}
spec:
  apdCode: "0029"
  risCode: dtpl
  risId: "1480"
  appName: adb
  certificateSecret: dtpl-secret-srv-cert-adb
  renew: true
  duration: 240h
  dn: CN=APD29-1480-adb-{{ $.Values.settings.instance | lower }}-client-srv
  extKeyUsages:
  - ClientAuth
{{/*- end */}}
{{- end }}
{{- end }}