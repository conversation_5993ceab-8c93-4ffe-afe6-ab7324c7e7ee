{{- if and (eq .Values.mode "main") (or (.Values.resolver) ((.Values.url).datafix)) }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dtpl-cm-srv-nginx-common
  namespace: {{ .Values.namespace }}
{{- $RESOLVER_LIST := $.Values.resolver | deepCopy }}
{{- if (($.Values.url).datafix) }}
  {{- $_ := set $RESOLVER_LIST $.Values.url.datafix (dict "host" "datafix-service" "port" "80") }}
{{- end }}
{{- $DATAFIX_HOST := 0 }}
data:
  dtpl-resolver.conf: |
    {{- range $key, $value := index $RESOLVER_LIST }}
      {{- if and (($.Values.url).datafix) (eq (($.Values.url).datafix) $key) }}
      {{- $DATAFIX_HOST = 1 }}
      {{- end }}    
    server {
        listen      8000;
        server_name {{ $key }};
        access_log /var/opt/rh/rh-nginx116/log/nginx/access.log main;
        client_max_body_size 64m;
        {{- if and $DATAFIX_HOST (or (not (hasKey $.Values.services "auth_enable")) (eq $.Values.services.auth_enable false)) }}
        auth_basic "Restricted Content";
        auth_basic_user_file /etc/nginx/.htpasswd;
        {{- $DATAFIX_HOST = 0 }}
        {{- end }}
        location / {
            {{- if not $value.disableHeaderHost }}
            proxy_set_header Host $host;
            {{- end }}
            proxy_pass      http{{ if $value.ssl }}s{{ end }}://{{ $value.host }}:{{ $value.port }}/;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
    {{ end }}
...
{{- end }}