{{- if and ((.Values.url).kfkods) (eq .Values.mode "main") }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: srv-kfkods-provider-front
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ default .Values.replicaCount (($.Values.services.kfkodsFront).replica) }}
  selector:
    matchLabels:
      app: kfkods-app-front
  strategy:
    type: Recreate  
  template:
    metadata:
      labels:
        app: kfkods-app-front
    spec:
      containers:
      - image: "{{ .Values.repo.server }}/{{ .Values.repo.suffix }}/srv-provider:{{ .Values.services.version }}"
        name: kfkods-provider-front
        command: ["bash", "-c", "/logs/run.sh kafka_provider_front kfkods-service-back"]
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: dtpl-cm-srv-common
        - configMapRef:
            name: dtpl-cm-srv-kf-common
        env:
        {{- if hasKey .Values.services "kfkodsFront" }}
        {{- if hasKey .Values.services.kfkodsFront "addEnv" }}
        {{- range $key, $value := .Values.services.kfkodsFront.addEnv }}
        - name: {{ $key | upper }}
          value: {{ $value | quote }}
        {{- end }}
        {{- end }}
        {{- end }}
        volumeMounts:
          - name: fluent-config
            mountPath: /logs/run.sh
            subPath: run.sh
          {{- if hasKey $.Values.kafka "tls" }}
          - name: kf-tls
            mountPath: /kf-tls/
          {{- end }}
          - name: logs
            mountPath: /logs/
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8000
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 20
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          failureThreshold: 20
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - ls
            - /logs/
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          {{- $LIMITS := default $.Values.resources ($.Values.services.kfkodsFront) }}
          {{- with $LIMITS }}
          limits:
            cpu: {{ .limits.cpu | quote }}
            memory: {{ .limits.memory | quote }}
          requests:
            cpu: {{ .requests.cpu | quote }}
            memory: {{ .requests.memory | quote }}
          {{- end }}   
      - image: "{{ .Values.repo.server }}/{{ .Values.repo.suffix }}/srv-fluentbit:{{ .Values.services.version }}"
        command: ["sh", "-c", "/logs/run_fluent.sh"]
        name: kfkods-fluent
        env:
        - name: ES_PASS
          valueFrom:
            secretKeyRef:
              name: dtpl-secret-srv-common
              key: EFK_ES_PASSWORD
        readinessProbe:
          exec:
            command:
            - ls
            - /logs/
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
          {{- if hasKey $.Values.kafkaLogs "tls" }}
          - name: kf-tls
            mountPath: /kf-tls/
          {{- end }}
          - name: fluent-config
            mountPath: /logs/run_fluent.sh
            subPath: run_fluent.sh
          - name: fluent-config
            mountPath: /fluent-bit/etc/fluent-bit.conf
            subPath: fluent-bit.conf
          - name: logs
            mountPath: /logs/
        resources:
          {{- $LIMITS := default $.Values.resources ($.Values.services.fluent) }}
          {{- with $LIMITS }}
          limits:
            cpu: {{ .limits.cpu | quote }}
            memory: {{ .limits.memory | quote }}
          requests:
            cpu: {{ .requests.cpu | quote }}
            memory: {{ .requests.memory | quote }}
          {{- end }}   
      volumes:
      {{- if hasKey $.Values.kafka "tls" }}
      - name: kf-tls
        secret:
          secretName: dtpl-secret-srv-cert-kf
          defaultMode: 0400
      {{- end }}
      - name: logs
        emptyDir: {}
      - name: fluent-config
        configMap:
          name: dtpl-cm-srv-fluent-files
          items:
          - key: run.sh
            path: run.sh
            mode: 0755
          - key: run_fluent.sh
            path: run_fluent.sh
            mode: 0755
          - key: fluent-bit.conf
            path: fluent-bit.conf
      imagePullSecrets:
      - name: dtpl-secret-srv-repo
---
apiVersion: v1
kind: Service
metadata:
  name: kfkods-service
  namespace: {{ .Values.namespace }}
spec:
  clusterIP: None
  selector:
    app: kfkods-app-front
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
...
{{- end }}