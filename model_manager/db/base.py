from model_manager.config import conf
from gino.ext.starlette import Gino  # pylint: disable=import-error
from loguru import logger


pg_service_pool = Gino(
    dsn=conf.DB_SERVICE_CONN_STR,
    pool_min_size=conf.DB_SERVICE_POOL_MIN_SIZE,
    pool_max_size=conf.DB_SERVICE_POOL_MAX_SIZE,
    echo=conf.DB_SERVICE_ECHO,
    use_connection_for_request=conf.DB_SERVICE_USE_CONNECTION_FOR_REQUEST,
    retry_limit=conf.DB_SERVICE_RETRY_LIMIT,
    retry_interval=conf.DB_SERVICE_RETRY_INTERVAL,
)


async def init_service_db():
    logger.log('INFO', 'Инициализация клиента к служебной БД')
    if not pg_service_pool.bind:
        await pg_service_pool.set_bind(
            conf.DB_SERVICE_CONN_STR,
            server_settings={
                'application_name': conf.DB_APP_NAME,
            },
        )
    logger.log('INFO', 'Инициализация клиента к служебной БД - Успешно')
    return pg_service_pool


pg_pool_dwh = Gino(
    dsn=conf.DB_CONN_STR,
    pool_min_size=conf.DB_POOL_MIN_SIZE,
    pool_max_size=conf.DB_POOL_MAX_SIZE,
    echo=conf.DB_ECHO,
    use_connection_for_request=conf.DB_USE_CONNECTION_FOR_REQUEST,
    retry_limit=conf.DB_RETRY_LIMIT,
    retry_interval=conf.DB_RETRY_INTERVAL,
)


async def init_db():
    logger.log('INFO', 'Инициализация клиента postgres к БД dwh')
    if not pg_pool_dwh.bind:
        await pg_pool_dwh.set_bind(
            conf.DB_CONN_STR,
            server_settings={
                'application_name': conf.DB_APP_NAME,
            },
        )
    logger.log('INFO', 'Инициализация клиента к БД dwh - Успешно')
    return pg_pool_dwh
