from pydantic import PostgresDsn, validator
from typing import Dict, Any, Optional
from lib.config import Settings


class ModelManagerSettings(Settings):
    IDENT: str = 'model-manager'
    DB_DRIVER: str = 'postgresql'
    DB_HOST: str = 'pg_ceh'
    DB_PORT: int = 5432
    DB_USER: str = 'default'
    DB_PASSWORD: str = 'default'
    DB_DATABASE: str = 'ceh'
    DB_CONN_STR: Optional[PostgresDsn] = None
    DB_APP_NAME: Optional[str] = None
    DB_POOL_MIN_SIZE: int = 1
    DB_POOL_MAX_SIZE: int = 1
    DB_POOL_RECYCLE: int = -1
    DB_POOL_CLEAR_CONNECTION: bool = False
    DB_POOL_ACQUIRING_TIMEOUT: int = 270
    DB_ECHO: bool = True
    DB_USE_CONNECTION_FOR_REQUEST: bool = True
    DB_RETRY_LIMIT: int = 1
    DB_RETRY_INTERVAL: int = 1

    # Креды к БД мониторинга
    IS_DB_SERVICE_USE: int = 0
    DB_SERVICE_DRIVER: str = 'postgresql'
    DB_SERVICE_HOST: str = 'pg_ceh'
    DB_SERVICE_PORT: int = 5432
    DB_SERVICE_USER: str = 'default'
    DB_SERVICE_PASSWORD: str = 'default'
    DB_SERVICE_DATABASE: str = 'ceh'
    DB_SERVICE_CONN_STR: Optional[PostgresDsn] = None
    DB_SERVICE_APP_NAME: Optional[str] = None
    DB_SERVICE_POOL_MIN_SIZE: int = 1
    DB_SERVICE_POOL_MAX_SIZE: int = 5
    DB_SERVICE_ECHO: bool = True
    DB_SERVICE_USE_CONNECTION_FOR_REQUEST: bool = True
    DB_SERVICE_RETRY_LIMIT: int = 1
    DB_SERVICE_RETRY_INTERVAL: int = 1

    PASSWORD_PROTECT: int = 1
    SERVICE_PASSWORD: str = 'ModelManagerAdminDefaultPass!'

    SQL_MAX_PARAMS: int = 500
    PATTERN_FILE: str = 'model_manager/template.xlsx'

    ID_SUFFIX: str = f'id-suffix-{1}'
    ID_APP: str = f'id-app-{1}'
    TOPIC_LOCAL_SUFFIX: str = 'default'
    TOPIC_CNT: str = '1'
    DB_QUERY_TIMEOUT: int = 300
    AGENT_CONCURENCY: int = 10

    NODE_ID: str = '1'

    @validator('DB_CONN_STR', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme='postgresql',
            host=values['DB_HOST'],
            port=str(values['DB_PORT']),
            user=values['DB_USER'],
            password=values['DB_PASSWORD'],
            path=f'/{values["DB_DATABASE"]}',
        )

    @validator('DB_SERVICE_CONN_STR', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_service_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme='postgresql',
            host=values['DB_SERVICE_HOST'],
            port=str(values['DB_SERVICE_PORT']),
            user=values['DB_SERVICE_USER'],
            password=values['DB_SERVICE_PASSWORD'],
            path=f'/{values["DB_SERVICE_DATABASE"]}',
        )

    @validator('DB_APP_NAME', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_app_name(cls, _: Optional[str], values: Dict[str, Any]) -> str:
        ident = values['IDENT']
        version = values['SERVICE_VERSION']
        instance = values['INSTANCE']
        return f'SSUM {ident} {version} {instance}'


conf = ModelManagerSettings()
