from fastapi.openapi.docs import get_swagger_ui_html, get_swagger_ui_oauth2_redirect_html
from lib.fastapi_versioning_mod import version
from fastapi import APIRouter

from model_manager.config import conf

router = APIRouter()


@router.get("/docs", include_in_schema=False)
@version(1, 0)
async def custom_swagger_ui():
    return get_swagger_ui_html(
        openapi_url='/api/1.0/openapi.json',
        title='Model manager - Swagger UI',
        oauth2_redirect_url='/api/1.0/docs/oauth2-redirect',
        swagger_js_url='/lib/static/swagger-ui-bundle.js',
        swagger_css_url='/lib/static/swagger-ui.css',
        init_oauth={
            "clientId": conf.OAUTH_CLIENT_ID,
            "clientSecret": conf.OAUTH_CLIENT_SECRET,
        },
    )


@router.get('/api/1.0/docs/oauth2-redirect', include_in_schema=False)
@version(1, 0)
async def swagger_ui_redirect():
    return get_swagger_ui_oauth2_redirect_html()
