from typing import Callable
from fastapi import Request, Response
from fastapi.routing import APIRoute
from lib.auth.config import conf as auth_conf
from lib.custom_api_route import custom_api_route
from kafka_provider.config import conf


class CustomAPIRoute(APIRoute):

    def get_route_handler(self) -> Callable:

        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:

            return await custom_api_route(
                original_route_handler=original_route_handler,
                request=request,
                auth_enabled=conf.AUTH_ENABLE,
                public_roles=auth_conf.PUBLIC_ROLES,
                rs256_public_key=conf.RS256_PUBLIC_KEY,

            )
        return custom_route_handler
