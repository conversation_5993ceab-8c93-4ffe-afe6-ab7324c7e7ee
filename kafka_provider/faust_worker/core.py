import datetime
from typing import Dict, List

from kafka_provider.utils.kafka_actualizer import KafkaMetricActualizer
from lib.kafka.models.kafka_models import ResourceKafkaFull, KafkaMetricFull
from lib.logging.utils import get_logger
from kafka_provider.faust_worker.agents import (
    # health_check_agent,
    metrics_commands_consumer,
    resource_commands_consumer,
    resource_update_commands_consumer,
    single_partition_commands_consumer,
)
from kafka_provider.faust_worker.commands import (
    AppendOrUpdateDictParams,
    AppendParams,
    GetParams,
    GetResourceStateParams,
    GetTableParams,
    SetResourceParams,
    UpdateParams,
    GetValidationErrorsParams,
    AppendOrUpdateParams,
    GetTableItemsParams,
    GetMetricsParams,
    GetKeysParams,
)
from kafka_provider.faust_worker.tables import (
    _kafka_resource_metrics_t,
    _kafka_resources_g_t,
    _kafka_resources_t,
)
from kafka_provider.utils.actualizer import MetricActualizer
from kafka_provider.utils.validator import ResourceValidator
from kafka_provider.utils.utils import set_metric_ids


logger = get_logger()


async def add_command_single(table_name, key, value):
    return await single_partition_commands_consumer.ask(
        key=key,
        value=AppendOrUpdateParams(table_name, key, value)
    )


async def add_resource_command(resource):
    return await resource_update_commands_consumer.ask(
        key=resource.resource_cd,
        value=AppendParams('_kafka_resources_t', resource.resource_cd, resource)
    )


# async def health_check_command(provider_type):
#     try:
#         answer = await health_check_agent.ask(
#             key=provider_type,
#             value='ok'
#         )
#         return answer
#     except Exception as ex:
#         logger.error(f'Kafka is not ok! - {ex}')


async def update_resource_command(resource):
    return await resource_update_commands_consumer.ask(
        key=resource.resource_cd,
        value=UpdateParams('_kafka_resources_t', resource.resource_cd, resource)
    )


async def delete_resource_command(resource_cd, resource):
    return await resource_update_commands_consumer.ask(
        key=resource_cd,
        value=SetResourceParams(resource_cd, resource)
    )


async def get_metrics():

    return await resource_commands_consumer.ask(
        value=GetMetricsParams(),
    )


async def get_resource_command(resource_cd):
    return await resource_commands_consumer.ask(
        key=resource_cd,
        value=GetParams('_kafka_resources_t', resource_cd)
    )


async def get_command(table: str, key: str):
    return await resource_commands_consumer.ask(
        key=key,
        value=GetParams(table, key)
    )


async def get_resources_command(limit, offset):
    # return await single_partition_commands_consumer.ask(
    return await resource_commands_consumer.ask(
        value=GetTableParams('_kafka_resources_g_t', limit, offset)
    )


async def get_resource_state_command(resource_cd):
    return await resource_commands_consumer.ask(
        key=resource_cd,
        value=GetResourceStateParams(resource_cd)
    )


async def get_table_items_command(table_name: str):
    return await resource_commands_consumer.ask(
        value=GetTableItemsParams(table_name)
    )


async def set_resource_command(resource_cd, resource):
    logger.info(f'set_resource_command {resource}')
    return await resource_update_commands_consumer.ask(
        key=resource_cd,
        value=SetResourceParams(resource_cd, resource)
    )


async def get_keys(table):
    return await resource_commands_consumer.ask(
        value=GetKeysParams(
            table=table
        )
    )


async def get_validation_errors_command(resources):
    return await resource_commands_consumer.ask(
        value=GetValidationErrorsParams(resources)
    )


async def set_resource(resource_cd, value):
    logger.info(f'set_resource {value}')
    if isinstance(value, ResourceKafkaFull):
        resource = value
    else:
        resource = ResourceKafkaFull(**value)
    logger.info(f'set_resource {resource}')
    resource = await set_metric_ids(resource)
    try:
        res = _kafka_resources_t[resource_cd]
    except KeyError:
        # logger.debug(f'set_resource {1}')
        _kafka_resources_t[resource_cd] = resource
        _kafka_resources_g_t[resource_cd] = resource
        # logger.debug(f'set_resource {2}')
        await _update_resource_metrics(resource, resource_cd)
    else:
        _kafka_resources_t[resource_cd] = resource
        _kafka_resources_g_t[resource_cd] = resource
        await _update_resource_metrics(resource, resource_cd)
    return resource


async def _update_resource_metrics(resource, resource_cd):
    logger.info(f'_update_metrics {resource.metrics}')
    for m_key, m_value in resource.metrics.items():
        logger.info(f'_update_metrics {m_value}')
        metric = KafkaMetricFull(
            id=m_value.id,
            resource_cd=resource_cd,
            as_of_dttm=datetime.datetime.utcnow(),
        )
        await metrics_commands_consumer.ask(
            key=resource_cd,
            value=AppendOrUpdateDictParams(
                dict_key=resource_cd,
                table='_kafka_resource_metrics_t',
                key=str(m_key),
                value=metric,
                dict_class='KafkaResourceMetric',
            )
        )


async def _update_metrics(metrics, resource_cd):
    logger.info(f'_update_metrics {metrics}')
    for metric in metrics:
        logger.info(f'_update_metrics {metric}')
        await metrics_commands_consumer.ask(
            key=resource_cd,
            value=AppendOrUpdateDictParams(
                dict_key=resource_cd,
                table='_kafka_resource_metrics_t',
                key=str(metric.id.split('.')[-1]),
                value=metric,
                dict_class='KafkaResourceMetric',
            )
        )


async def get_resource_state(resource_cd):
    try:
        resource = _kafka_resources_t[resource_cd]
    except KeyError:
        return None
    else:
        try:
            metrics = _kafka_resource_metrics_t[resource_cd]
            logger.info(metrics)
            metrics = metrics.queue
        except KeyError:
            # ToDo: MetricsNotFoundException
            return None
        else:
            raw_metrics = []
            for metric in metrics.values():
                if isinstance(metric, dict):
                    metric = KafkaMetricFull(**metric)
                if not isinstance(metric.as_of_dttm, datetime.datetime):
                    metric.as_of_dttm = datetime.datetime.fromisoformat(str(metric.as_of_dttm))
                if not isinstance(metric.last_online_dttm, datetime.datetime):
                    metric.last_online_dttm = datetime.datetime.fromisoformat(
                        str(metric.last_online_dttm)
                    ) if metric.last_online_dttm else None
                raw_metrics.append(metric)
            logger.info(f'raw_metrics: {raw_metrics}')
            actual_metrics = await _actualize_metrics(resource, raw_metrics)
            logger.info(f'actual_metrics {actual_metrics}')
            await _update_metrics(actual_metrics, resource_cd)
            return actual_metrics


async def _actualize_metrics(resource, metrics):
    logger.info(f'Актуализация метрик {metrics} для ресурса {resource}')
    actualizer = KafkaMetricActualizer(resource, metrics)
    actual_metrics = await actualizer.actualize()
    logger.info(f'Актуализированные метрики ресурса {resource}, {actual_metrics}')
    return actual_metrics


async def get_validation_errors(resources: List[Dict]):
    """Функция для валидации списка шаблонных ресурсов

    На вход принимаем именно список словарей, так как шаблоны ресурсов могут не попадать под схему
    из-за шаблонизации целых блоков

    """
    logger.info(f'Начало валидации {len(resources)} ресурсов')
    errors = {}

    # делаем быструю проверку шаблонных переменных (существуют в TEMPLATE_VALUES/меташаблоне)
    # Возвращаем ошибки, если они есть, чтобы сократить devops'ам время ожидания (своего рода syntax error).
    # Нет смысла проверять метрики и датасеты, если шаблоны плохие (например, коннекты не определены)
    for resource in resources:
        resource_cd = resource['resource_cd']
        logger.info(f'Начало валидации рендеринга ресурса {resource_cd}.')
        validator = ResourceValidator(resource)
        err = validator.fill_resource()
        if err:
            errors[resource_cd] = {'errors': [err]}
    if errors:
        logger.error(f'Обнаружены ошибки при рендеринге ресурсов: {errors}.')
        return errors

    for resource in resources:
        resource_cd = resource['resource_cd']
        logger.info(f'Начало валидации ресурса {resource_cd}.')
        validator = ResourceValidator(resource)
        validator.fill_resource()
        err, warn = await validator.validate_data()
        if warn:
            errors[resource_cd] = {'warnings': warn}
        if err:
            errors.setdefault(resource_cd, {})
            errors[resource_cd]['errors'] = err

        logger.error(f'Ошибки при валидации ресурса {resource_cd}: {err}.')
        logger.warning(f'Предупреждения при валидации ресурса {resource_cd}: {warn}.')

    logger.info(f'Финальный репорт об ошибках валидации: {errors}')
    return errors
