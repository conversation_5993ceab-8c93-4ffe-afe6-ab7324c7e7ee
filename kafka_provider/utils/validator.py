from kafka_provider.exceptions import CycleRenderException
from jinja2 import UndefinedError, TemplateSyntaxError
from kafka_provider.utils.utils import fill_template
from lib.logging.utils import get_logger
from pydantic import ValidationError
from kafka_provider.config import conf
from async_timeout import timeout
from kafka_provider.db import db
from kafka_provider import utils
from datetime import datetime
import configparser
import asyncio
import aioodbc
import pyodbc
import string
import random


logger = get_logger()


class ResourceValidator:
    """Принимает словарь ресурса, рендерит его и валидирует метрики и датасеты"""

    def __init__(self, resource):
        self.raw_resource = resource
        self.resource = None

    def fill_resource(self):
        """Рендерит полученный при инициализации ресурс. Если во время рендеринга возникли ошибки, возвращает их"""
        errors = self._check_undefined_variables_error()
        if not errors:
            self.resource = fill_template(self.raw_resource)
        else:
            return errors

    async def validate_data(self):
        """Валидация метрик и датасетов"""
        if not self.resource:
            return ['Resource not filled'], []

        errors = []
        warnings = []

        err, warn = await self._check_metrics()
        errors += err
        warnings += warn

        err, warn = await self._check_datasets()
        errors += err
        warnings += warn

        return errors, warnings

    def _check_undefined_variables_error(self):
        """Проверить, что все шаблонные переменные существуют и отрендеренный ресурс соответствует схеме"""
        try:
            fill_template(self.raw_resource, is_strict=True)
        except UndefinedError as e:
            error = f'При заполнении шаблона получена ошибка: {str(e)}. ' \
                    f'Убедитесь, что имена переменных верны и присутствуют ' \
                    f'в переменной среды TEMPLATE_VALUES или меташаблоне'
            return error
        except CycleRenderException as e:
            error = f'При заполнении шаблона получена ошибка: {str(e)}. ' \
                    f'Вероятно имеет место зацикленные шаблонные переменные'
            return error
        except ValidationError as e:
            error = f'Некорректная схема: {str(e)}'
            return error
        except TemplateSyntaxError as e:
            error = f'Ошибка в синтаксисе шаблонной переменной: {str(e)}'
            return error

    async def _check_metrics(self):
        """Проверка, что все метрики ресурса правильно заполнены и могут быть рассчитаны по указанному connections"""
        errors = []
        warnings = []
        coroutines = []
        metric_name2coroutines = []
        for metric_name, metric in self.resource.metrics.items():
            if hasattr(metric, 'alias') and metric.alias:
                if metric.alias not in self.resource.metrics:
                    # TODO когда добавим JQ, усложнить проверку
                    errors.append(f'Не удалось вычислить результат метрики {metric_name}: {metric.alias}')
                continue

            # Проверка формата метки refresh
            format_dt = "PDT%HH%MM"
            try:
                datetime.strptime(metric.refresh, format_dt)
            except ValueError:
                warnings.append(
                    f'Для метрики {metric_name} поле refresh {metric.refresh} указано в некорректном формате. '
                    f'Ожидаемый формат: {format_dt}. Для обновления будет использовано значение по умолчанию.')

            metric_name2coroutines.append(metric_name)
            coroutines.append(self._execute_query(metric_name))

        if conf.PARALLEL_SQL_EXECUTE:
            logger.info(f'Начало валидации метрик {", ".join(metric_name2coroutines)}')
            results = await asyncio.gather(*coroutines, return_exceptions=True)
            for metric_name, metric_value in zip(metric_name2coroutines, results):
                if isinstance(metric_value, Exception):
                    errors.append(f'При вычислении метрики {metric_name} получена ошибка: {str(metric_value)}')
        else:
            for metric_name, coroutine in zip(metric_name2coroutines, coroutines):
                try:
                    logger.info(f'Начало валидации метрики {metric_name}')
                    await coroutine
                except Exception as exc:
                    errors.append(f'При вычислении метрики {metric_name} получена ошибка: {str(exc)}')

        logger.error(f'Ошибки при валидации метрик {self.resource.resource_cd}: {errors}')
        logger.warning(f'Предупреждения при валидации метрик {self.resource.resource_cd}: {warnings}')
        return errors, warnings

    async def _execute_query(self, metric_name):
        """Исполнить sql запрос из метрики. metric_name - название метрики в self.resource.metrics"""
        connection_name = self.resource.metrics[metric_name].connection
        connection = self.resource.connections.get(connection_name)
        if not connection:
            if metric_name not in ('locked_by', 'is_locked'):
                raise Exception(f'Для метрики {metric_name} указан connection {connection_name}, '
                                f'который не описан в ресурсе')
            return

        if connection['type'] != 'odbc':
            raise Exception(f'connection {connection_name} для метрики {metric_name} имеет не ODBC тип подключения')

        dsn = ''
        if connection['dsn']:
            raw_dsn = connection['dsn']
            dsn = f"DSN={raw_dsn}"

            # MS ODBC драйвер не подставляет PWD и UID из odbc.ini, подставляем руками
            driver = pyodbc.dataSources().get(raw_dsn)
            if driver and conf.MS_ODBC_DRIVER in driver:
                config = configparser.ConfigParser()
                config.read(conf.ODBC_INI_PATH)
                if raw_dsn in config.sections():
                    dsn += f";UID={config[raw_dsn]['UID']};PWD={config[raw_dsn]['PWD']}"

        if connection['options']:
            options = ';'.join(['{}={}'.format(k, v) for k, v in connection['options'].items()])
            dsn = f'{dsn};{options}' if dsn else options
        if not dsn:
            raise Exception(f'connection {connection_name} для метрики {metric_name} имеет пустую connection string')

        query = self.resource.metrics[metric_name].query
        query_parameters = []
        if hasattr(self.resource.metrics[metric_name], 'query_parameters') and \
                self.resource.metrics[metric_name].query_parameters:
            for query_parameter in self.resource.metrics[metric_name].query_parameters:
                query_parameters.append(self._convert_type(query_parameter.value, query_parameter.sqltype))

        if dsn not in utils.pool:
            utils.pool[dsn] = await aioodbc.create_pool(dsn=dsn, autocommit=True, minsize=conf.DB_POOL_MIN_SIZE,
                                                        maxsize=conf.DB_POOL_MAX_SIZE,
                                                        pool_recycle=conf.DB_POOL_RECYCLE)

        db_query_timeout = self.resource.metrics[metric_name].query_exec_timeout or conf.DB_QUERY_TIMEOUT
        logger.info(
            f"Получение соединения из пула. resource: {self.resource.resource_cd}, metric: {metric_name}, "
            f"_used: {len(utils.pool[dsn]._used)}, freesize: {utils.pool[dsn].freesize}, "  # noqa
            f"_acquiring: {utils.pool[dsn]._acquiring}, query_timeout: {db_query_timeout}, "  # noqa
            f"closed: {utils.pool[dsn].closed}")
        if utils.pool[dsn]._closing:  # noqa
            await asyncio.sleep(10)
        if utils.pool[dsn].closed:
            utils.pool[dsn] = await aioodbc.create_pool(dsn=dsn, autocommit=True, minsize=conf.DB_POOL_MIN_SIZE,
                                                        maxsize=conf.DB_POOL_MAX_SIZE,
                                                        pool_recycle=conf.DB_POOL_RECYCLE)
        conn = None
        try:
            try:
                async with timeout(conf.DB_POOL_ACQUIRING_TIMEOUT):
                    conn = await utils.pool[dsn].acquire()
            except TimeoutError:
                logger.info(
                    f"Соединение из пула не получено по истечениие {conf.DB_POOL_ACQUIRING_TIMEOUT} секунд. "
                    f"Пул будет закрыт и открыт заново resource: {self.resource.resource_cd}, metric: "
                    f"{metric_name}, _used: {len(utils.pool[dsn]._used)}, freesize: "  # noqa
                    f"{utils.pool[dsn].freesize}, _acquiring: {utils.pool[dsn]._acquiring}, "  # noqa
                    f"query_timeout: {db_query_timeout}")
                bad_pool = utils.pool.pop(dsn)
                await bad_pool.clear()
                logger.info(
                    f"Состояние пула после очистки. resource: {self.resource.resource_cd}, "
                    f"metric: {metric_name}, _used: {len(bad_pool._used)},"  # noqa
                    f"freesize: {bad_pool.freesize}, "  # noqa
                    f"_acquiring: {bad_pool._acquiring}, query_timeout: {db_query_timeout}, "  # noqa
                    f"closed: {bad_pool.closed}")
                bad_pool.close()
                await bad_pool.wait_closed()
                utils.pool[dsn] = await aioodbc.create_pool(dsn=dsn, autocommit=True,
                                                            minsize=conf.DB_POOL_MIN_SIZE,
                                                            maxsize=conf.DB_POOL_MAX_SIZE,
                                                            pool_recycle=conf.DB_POOL_RECYCLE)
                logger.info(
                    f"Получение соединения из пула после закрытия. resource: {self.resource.resource_cd}, "
                    f"metric: {metric_name}, _used: {len(utils.pool[dsn]._used)},"  # noqa
                    f"freesize: {utils.pool[dsn].freesize}, "  # noqa
                    f"_acquiring: {utils.pool[dsn]._acquiring}, query_timeout: {db_query_timeout}, "  # noqa
                    f"closed: {utils.pool[dsn].closed}")
                conn = await utils.pool[dsn].acquire()

            logger.info(
                f"Соединение из пула получено. resource: {self.resource.resource_cd}, metric: {metric_name}, "
                f"_used: {len(utils.pool[dsn]._used)}, freesize: {utils.pool[dsn].freesize}, "  # noqa
                f"_acquiring: {utils.pool[dsn]._acquiring}, query_timeout: {db_query_timeout}")  # noqa
            async with timeout(db_query_timeout):
                async with conn.cursor() as cur:
                    logger.info(f"Выполняется запрос {query} ресурса {self.resource.resource_cd}, метрика "
                                f"{metric_name} c параметрами {query_parameters}")
                    await cur.execute(query, tuple(query_parameters))
            if conn:
                await utils.pool[dsn].release(conn)
        except Exception:
            if conn:
                await utils.pool[dsn].release(conn)
            raise

    def _convert_type(self, value, sqltype):
        """Приведение python переменной к нужному типу данных"""
        from datetime import date, datetime, time
        if sqltype in ('bigint', 'integer', 'smallint'):
            return int(value)
        elif sqltype == 'boolean':
            return bool(value)
        elif sqltype == 'date':
            return date.fromisoformat(value)
        elif sqltype in ('decimal', 'double', 'numeric'):
            return float(value)
        elif sqltype == 'time':
            return time.fromisoformat(value)
        elif sqltype == 'timestamp':
            return datetime.fromisoformat(value)
        elif sqltype in ('char', 'varchar'):
            return str(value)
        else:
            return str(value)

    async def _check_datasets(self):
        """Проверка, что dataset можно найти хотя бы по 1 из описанных в ресурсе pxf профилей

        Немного пояснений, к тому как мы будем проверять наличие таблицы без знания хотя бы 1 поля схемы.
        Алгоритм следующий:
        Если сделать запрос НЕ существующей колонки в существующей таблице, то выдаст что-то вроде
        column does not exist (в зависимости от источника будут разные ошибки)
        Если сделать запрос НЕ существующей колонки в НЕ существующей таблице, то выдаст что-то вроде
        Table not found

        Находил более красивые варианты, но они работали только для oracle, а, например, на hive не срабатывали:
            - Если создать таблицу со схемой ("1" text) для oracle, pxf вернет константу 1
            - Если вместо таблицы в location pxf указать (select 1 from schema.table), то pxf выполнит этот запрос
            - В pxf есть возможность делать select на основании кастомного запроса (гугли "pxf query:<query_name>"),
                но требуется отдельная конфигурация да и динамически таблицу туда не подставишь
        """
        errors = []
        warnings = []
        if not self.resource.datasets:
            return errors, warnings

        pxf_connections = [c for c in self.resource.connections.values() if c['type'] == 'pxf' and c.get('location')]
        if not pxf_connections:
            errors.append('В ресурсе не обнаружен не один pxf профиль с указанным location')
            return errors, warnings

        try:
            async with db.with_bind(conf.DB_CONN_STR):
                for dataset in self.resource.datasets:
                    ext_table = f'{dataset.schema_name}.{dataset.name}' if dataset.schema_name else dataset.name
                    for connection in pxf_connections:
                        pxf_conn_str = f"pxf://{ext_table}?PROFILE={connection['location']['profile']}"

                        options = ''
                        if connection['location'].get('options'):
                            options = ''.join([f'&{key}={val}'
                                               for key, val in connection['location']['options'].items()])
                        if connection['location']['server']:
                            pxf_conn_str += f'&SERVER={connection["location"]["server"]}'
                        if options:
                            pxf_conn_str += options

                        rnd_suffix = ''.join([random.choice(string.hexdigits) for _ in range(20)])
                        checking_table_name = f'"check_exist_{rnd_suffix}"'
                        try:
                            await db.status(db.text(
                                  f"CREATE EXTERNAL TABLE {conf.STG_SCHEMA}.{checking_table_name} "
                                  f"(\"uni_service_check_existing\" TEXT)\n"
                                  f"LOCATION('{pxf_conn_str}')\n"
                                  "FORMAT 'CUSTOM' (formatter='pxfwritable_import')\n"
                                  "ENCODING 'utf-8';\n"
                                )
                            )
                            await db.status(db.text(f'SELECT * FROM {checking_table_name}'))
                        except Exception as exc:
                            # Для получения пояснений обратись к doc line данной функции
                            # invalid identifier - проверка для oracle, остальные источники подходят под 'column in'
                            if 'column' in str(exc).lower() or 'invalid identifier' in str(exc).lower():
                                break
                            # Если не получили not column И не получили Table not found, то что-то особое (bad pxf)
                            elif 'table' not in str(exc).lower() and 'relation' not in str(exc).lower():
                                warn_msg = f'Неожиданная ошибка при обращении к ' \
                                           f'dataset {dataset.schema_name}.{dataset.name} по pxf {pxf_conn_str}: {exc}'
                                warnings.append(warn_msg)
                        finally:
                            try:
                                await db.status(db.text(f'DROP EXTERNAL TABLE {checking_table_name}'))
                            except Exception as e:
                                logger.error(f'Ошибка при удалении временной external таблицы {checking_table_name}: '
                                             f'{str(e)}')
                    else:
                        # Если во время for не было break, то мы попадем сюда (break вызываем, если таблица найдена)
                        error = f'dataset {ext_table} не найден ни по одному PXF подключению'
                        errors.append(error)
        except Exception as e:
            errors.append(f'Неожиданная ошибка при проверке datasets: {str(e)}')

        logger.error(f'Ошибки при поиске датасетов {self.resource.resource_cd}: {errors}')
        logger.warning(f'Предупреждения при поиске датасетов {self.resource.resource_cd}: {warnings}')
        return errors, warnings
