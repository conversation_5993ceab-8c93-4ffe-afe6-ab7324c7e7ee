from functools import partial
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from sqlalchemy import create_engine
from datafix_manager.db.base import pg_pool
from lib.logging.utils import configure_logging, patch_loggers, log_settings
from datafix_manager.config import conf
from datafix_manager.router import api_router


# Настройка логирования сервиса
configure_logging(
    console_enabled=conf.LOG_CONSOLE_ENABLED,
    console_level=conf.LOG_CONSOLE_LEVEL,
    console_json=conf.LOG_CONSOLE_JSON,
    json_enabled=conf.LOG_JSON_ENABLED,
    json_level=conf.LOG_JSON_LEVEL,
    json_filename=conf.LOG_JSON_FILENAME,
    initial_values={
        'service_version': conf.SERVICE_VERSION,
    },
)

app = FastAPI(
    docs_url=None,
    redoc_url=None,
    title='Datafix manager',
    version=conf.SERVICE_VERSION,
    openapi_url='/api/1.0/openapi.json'
)


# Настройка логеров для импортируемых пакетов
patch_loggers()

app.include_router(api_router)
app.mount('/lib/static', StaticFiles(directory='lib/static'), name='static')
app.dag_run_ids = []

engine = create_engine(
    conf.DB_CONN_STR
)
app.engine = engine


@app.on_event('startup')
async def startup():

    # Запись настроек сервиса в лог
    log_settings(conf)

    pg_pool.init_app(app)

    if not pg_pool.bind:
        await pg_pool.set_bind(
            conf.DB_CONN_STR,
            server_settings={
                'application_name': conf.DB_APP_NAME,
            },
        )