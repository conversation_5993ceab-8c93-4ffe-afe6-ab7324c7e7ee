import datetime
import logging
from time import sleep
import psycopg2

from psycopg2 import sql as sql_
from typing import (
    Any,
    Dict,
    List,
    Optional,
)
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.hooks.base import BaseHook
from airflow.models.baseoperator import chain
from ceh_core_libs.utils import Job<PERSON>ogger
from ceh_core_rdv.config import config
from pathlib import Path
from dwh_services_utils.clients.ceh import ceh_provider_v_0_6
from dwh_services_utils.schemas.ceh_provider.v0_6.schemas import (
    ColumnDefinition,
    Resource,
    DatasetDefinition,
    ColumnTypeDefinition,
)
from dwh_services_utils.clients import DeltaManagerClient
from dwh_services_utils.clients.client_factory import (
    System,
    client_factory,
    TxClientFactory,
    DeltaClientFactory,
)
from dwh_services_utils.clients import TxManagerClient
from dwh_services_utils.clients.ceh.client_factory import CehClientFactory
from dwh_services_utils.schemas.delta_manager.schemas import StatusType


class CannotCreateDelta(Exception):
    """
    Raised if delta is not created
    """
    pass


class ValidationError(Exception):
    pass


# Example of dag config
"""
{
    "resource_cd": "example_resource_cd", 
    "query": "select 1", 
    "table_name": "example_table",
    "table_rk": "example_rk",
    "author": "example_author"
}
"""

# DWH services clients
ceh_factory: CehClientFactory = client_factory.get_factory(System.CEH)
# noinspection PyTypeChecker
ceh_provider_client: ceh_provider_v_0_6.CehProviderClient = ceh_factory.get_client(
    base_url=config.ceh_provider.base_url,
    version='0.6'
)
tx_factory: TxClientFactory = client_factory.get_factory(System.TX)
# noinspection PyTypeChecker
tx_manager_client: TxManagerClient = tx_factory.get_client(
    base_url=config.tx_manager.base_url
)
# noinspection PyTypeChecker
delta_factory: DeltaClientFactory = client_factory.get_factory(System.DELTA)
delta_manager_client: DeltaManagerClient = delta_factory.get_client(
    base_url=config.delta_manager.base_url
)

if hasattr(config.gp, 'etl_connection_name') and config.gp.etl_connection_name:
    con = BaseHook.get_connection(config.gp.etl_connection_name)
    gp_db = con.schema
    gp_user = con.login
    gp_host = con.host

    GP_CONNECT = dict(
        dbname=gp_db,
        user=gp_user,
        host=gp_host,
        port=con.port,
        password=con.password
    )
else:
    gp_db = config.gp.database
    gp_user = config.gp.username
    gp_host = config.gp.host

    GP_CONNECT = dict(
        dbname=gp_db,
        user=gp_user,
        host=gp_host,
        port=config.gp.port,
        password=config.gp.password
    )


def _execute_query(
        connection,
        cursor,
        query,
        params=None,
        success_msg='SQL success executed',
        error_msg='SQL throw error',
        is_raise_next=False,
        is_commit=True,
        is_log_rowcount=False,
        is_fetch=False,
        is_log_notices=False
):
    """
    Execute sql query, catch error, log results

    :param connection: psycopg connection
    :param cursor: psycopg cursor
    :param query: sql query for execute
    :param params: params for sql query
    :param success_msg: log message if execution successful
    :param error_msg: log message if execution throw error
    :param is_raise_next: True, then not ignore error and raise exception next (fail task)
    :param is_commit: commit executed query result
    :param is_log_rowcount: log rowcount
    :param is_fetch: fetch rows result
    :param is_log_notices: log result notices
    :return: rowcount, returned_query_data
    """

    len_notices = len(connection.notices)
    try:
        logging.info(f"SQL:\n{cursor.mogrify(query, params if params is not None else []).decode('utf-8')}")
        cursor.execute(query, params)

    except Exception as exc:
        logging.error(f'{error_msg}. {exc}')
        if is_raise_next:
            raise
        else:
            connection.rollback()
            return -1, None
    else:
        if is_log_notices:
            for notice in connection.notices[len_notices:]:
                logging.info(f'NOTICE: {notice}.')
        rowcount = cursor.rowcount
        rows = cursor.fetchall() if is_fetch and cursor.pgresult_ptr is not None else None

        if is_commit:
            connection.commit()

        if is_log_rowcount:
            success_msg += f' Count rows: {rowcount}'
        logging.info(success_msg)

        return rowcount, rows


def change_delta(delta_columns: List[Dict],
                 target_columns: List[ColumnDefinition]) -> List[Dict]:
    for target_column in target_columns:
        for delta_column in delta_columns:
            if target_column.name == delta_column['name']:
                delta_column['primary_key'] = target_column.primary_key
                delta_column['type'] = target_column.type
                continue
    column_type_difinition = [item.value for item in ColumnTypeDefinition]
    for delta_column in delta_columns:
        if delta_column['type'] not in column_type_difinition:
            delta_column['type'] = ColumnTypeDefinition.text.value
    return delta_columns


def open_transaction() -> Dict[str, str]:
    tx = tx_manager_client.create_transaction(commit_timeout=14400)
    logging.info(f'tx_uid: {tx.tx_uid}')
    return {'tx_uid': str(tx.tx_uid), 'tx_token': tx.tx_token}


def build_origin(context: Dict[str, Any]) -> Dict[str, Any]:
    return {
        'author': context['dag_run'].conf.get('author'),
        'algorithm_uid': 'datafix_delta',
        'engine': 'airflow',
        'dag_id': context['dag'].dag_id,
        'run_id': context['dag_run'].run_id,
        'service': 'data_fix_delta',
        'change_request': context['dag_run'].conf.get('change_request'),
        'table_name': context['dag_run'].conf.get('table_name'),
        'json_all': context['dag_run'].conf,
    }


def lock_resource(**context) -> int:
    tx = context['task_instance'].xcom_pull(task_ids='open_transaction')
    resource_code = context['dag_run'].conf.get("resource_cd")
    attempts = context['dag_run'].conf.get('attempts') or 30
    attempt_timeout = context['dag_run'].conf.get('attempt_timeout') or 60
    version_id = ceh_provider_client.lock(resource_codes=[resource_code], tx_uid=tx['tx_uid'], attempts=attempts,
                                          attempt_timeout=attempt_timeout, origin=build_origin(context),)
    return version_id


def create_delta_table(**context) -> tuple:
    # logging.info(f"GP_CONNECT={GP_CONNECT}")
    version_id = context['task_instance'].xcom_pull(task_ids='lock_resource')
    table_name = context['dag_run'].conf.get('table_name').split('.')
    table_rk = context['dag_run'].conf.get('table_rk')
    p_snp_flg = context['dag_run'].conf.get('p_snp_flg')
    p_synth_src_flg = context['dag_run'].conf.get('p_synth_src_flg')
    p_where_wnd_filter = context['dag_run'].conf.get('p_where_wnd_filter')
    p_reload_flg = context['dag_run'].conf.get('p_reload_flg')
    p_wnd_col_list = context['dag_run'].conf.get('p_wnd_col_list')
    query = context['dag_run'].conf.get('query')

    if p_snp_flg and not (p_wnd_col_list or p_where_wnd_filter):
        raise ValidationError(
            'Выполнение в режиме p_snp_flg: true требует задания '
            'окна(p_wnd_col_list или p_where_wnd_filter)'
        )

    p_wnd_col_list = "null" if p_wnd_col_list is None else f"'{p_wnd_col_list}'"
    p_where_wnd_filter = None if p_where_wnd_filter is None else p_where_wnd_filter.replace("'", "''")
    p_where_wnd_filter = "null" if p_where_wnd_filter is None else f"'{p_where_wnd_filter}'"

    if _txi_schema := context['dag_run'].conf.get('p_txi_schema'):
        txi_schema = _txi_schema
    else:
        txi_schema = 'idl_dlt'

    with psycopg2.connect(**GP_CONNECT) as conn:
        with conn.cursor() as cur:
            create_sql_proc = (
                f"""SET var.run_id = {version_id};
                    SET var.task_id = '{context['task'].task_id}';
                    SET var.dag_id ='{context['dag'].dag_id}';
                    SELECT etl.fn_create_sql_proc(
                        p_cr_obj_schema := 'idl_work'::text,
                        p_cr_obj_name := 't_{version_id}_{table_name[1]}'::text,
                        p_sql_text := %s::text,
                        p_mat_flg := true,
                        p_analyze_flg := true,
                        p_distr_cls := '{table_rk}'::text 
                    )"""
            )
            logging.info(f"creating preliminary delta table={create_sql_proc}")
            _, res = _execute_query(
                connection=conn,
                cursor=cur,
                query=create_sql_proc,
                params=(query,),
                success_msg=f'Successful creation of preliminary delta table.',
                error_msg=f'Error in creation of preliminary delta table.',
                is_commit=True,
                is_log_rowcount=False,
                is_fetch=True,
                is_raise_next=True,
                is_log_notices=True,
            )

            if not res[0][0]:
                raise CannotCreateDelta('Error while creating preliminary delta table')

            create_delta_table_sql = (
                f"""SET var.run_id = {version_id};
                    SET var.task_id = '{context['task'].task_id}';
                    SET var.dag_id ='{context['dag'].dag_id}';                 
                    SELECT etl.fn_dlt_lg_load( 
                        p_src_schema := 'idl_work'::text,
                        p_src_table := 't_{version_id}_{table_name[1]}'::text,
                        p_tgt_schema := '{table_name[0]}'::text,
                        p_tgt_table := '{table_name[1]}'::text,
                        p_src_bk_col_list := '{table_rk}'::text,
                        p_wf_load_id := '_{version_id}'::text,
                        p_init_load_flg := false,
                        p_stable_version_id := {version_id}::int8,
                        p_curr_version := {version_id}::int8,
                        p_snp_flg := {p_snp_flg},
                        p_synth_src_flg := {p_synth_src_flg},
                        p_wnd_col_list := {p_wnd_col_list}::text,
                        p_stg_schema := 'idl_work'::text,
                        p_txi_table_name := '{txi_schema}.v{version_id}_{table_name[1]}'::text,
                        p_hash_calc_flg := true,
                        p_use_accessor_flg := false,
                        p_where_wnd_filter := {p_where_wnd_filter}::text,
                        p_reload_flg := {p_reload_flg}::bool,
                        p_dm_del_close_flg := true          
                ) """
            )
            logging.info(f"creating delta table={create_delta_table_sql}")
            _, res = _execute_query(
                connection=conn,
                cursor=cur,
                query=create_delta_table_sql,
                params=(),
                success_msg=f'Successful creation of delta table.',
                error_msg=f'Error in creation of delta table.',
                is_commit=True,
                is_log_rowcount=False,
                is_fetch=True,
                is_raise_next=True,
                is_log_notices=True,
            )
            if not res[0][0]:
                raise CannotCreateDelta()

            get_cols_sql = (
                f"""SELECT json_agg(json_build_object('name', column_name , 'type', data_type, 'primary_key', false, 
                'nullable', CASE is_nullable WHEN 'NO' THEN false ELSE true END)) AS cols
                    FROM information_schema.columns
                    WHERE
                        table_schema = '{txi_schema}'
                        AND table_name = 'v{version_id}_{table_name[1]}'"""
            )
            logging.info(f"Getting columns set from delta table={get_cols_sql}")
            _, res = _execute_query(
                connection=conn,
                cursor=cur,
                query=get_cols_sql,
                params=(),
                success_msg=f'Successful get of columns set from delta table.',
                error_msg=f'Error in getting of columns set from delta table.',
                is_commit=True,
                is_log_rowcount=False,
                is_fetch=True,
                is_raise_next=True,
            )

    return f'{txi_schema}.v{version_id}_{table_name[1]}', res[0][0], p_where_wnd_filter


def validate_row_count(**context) -> None:
    # logging.info(f"GP_CONNECT={GP_CONNECT}")
    delta_table_name, _, _ = context['task_instance'].xcom_pull(task_ids='create_delta_table')
    changed_row_count = context['dag_run'].conf.get('changed_row_count')
    get_row_count_sql = (
        f"""select count(1) from {delta_table_name} 
        where record_mode in ('U', 'D')"""
    )
    with psycopg2.connect(**GP_CONNECT) as conn:
        with conn.cursor() as cur:
            logging.info(f"Getting row count: {get_row_count_sql}")
            _, res = _execute_query(
                connection=conn,
                cursor=cur,
                query=get_row_count_sql,
                params=(),
                success_msg=f'Successful getting row count of delta table.',
                error_msg=f'Error during getting row count of delta table.',
                is_commit=True,
                is_log_rowcount=False,
                is_fetch=True,
                is_raise_next=True,
            )
            if res[0][0] != changed_row_count:
                raise ValidationError(f"Expected changed row count {changed_row_count} does not match actual row count {res[0][0]}")


def update_resources_state(**context) -> None:
    tx = context['task_instance'].xcom_pull(task_ids='open_transaction')
    delta, delta_columns, p_where_wnd_filter = context['task_instance'].xcom_pull(task_ids='create_delta_table')
    version_id = context['task_instance'].xcom_pull(task_ids='lock_resource')
    delta = delta.split('.')
    resource_cd = context['dag_run'].conf.get('resource_cd')
    table_name = context['dag_run'].conf.get('table_name').split('.')
    dag_conf = context['dag_run'].conf

    resource: Resource = ceh_provider_client.get_resource(resource_cd=resource_cd)

    target_dataset: Optional[DatasetDefinition] = None
    if resource.datasets and isinstance(resource.datasets, list) and len(resource.datasets):
        for ds in resource.datasets:
            if table_name[0] == ds.schema_name and table_name[1] == ds.name:
                target_dataset = ds
                break

    # target_dataset: Optional[DatasetDefinition] = resource.datasets[0] \
    #     if resource.datasets and isinstance(resource.datasets, list) and len(resource.datasets) else None
    delta_columns = change_delta(delta_columns, target_dataset.columns) \
        if target_dataset and target_dataset.columns else None

    if delta_columns and target_dataset.columns:
        delta_dataset = {
                "name": delta[1],
                "schema_name": delta[0],
                "columns": delta_columns
            }
        do = {
            "target": target_dataset.dict(),
            "delta": delta_dataset,
            "options": {"extra_update_condition": p_where_wnd_filter}
        }
    else:
        do = None

    logging.info(f"Do: {do}")
    conf = {
        'by_src': {},
        'common': {
            'query_type': 'insert',
            'query': dag_conf.get('query'),
            'datafix_version_id': version_id,
            'changed_version_ids': dag_conf.get('updated_version_ids', []),
        },
    }

    operation = {
        "origin": build_origin(context),
        'source': {
            'conf': conf,
            'results': {
                'by_src': {},
                'common': {},
            },
        },
        "type": "delta",
        # "statement": f"INSERT INTO {table_name[0]}.{table_name[1]} ({columns}) SELECT "
        #              f"{str(columns).replace('version_id,', str(version_id)).replace('version_id', str(version_id))} "
        #              f"FROM {delta[0]}.{delta[1]} WHERE record_mode='A'",
        "delta": {
            "table": delta[1],
            "schema_name": delta[0]
        },
        "target": {
            "resource_cd": resource_cd,
            "table": table_name[1],
            "schema_name": table_name[0]
        },
        "do": do
    }
    state = ceh_provider_client.update_resource_state(
        resource_cd=resource_cd,
        tx_uid=tx['tx_uid'],
        operation=operation
    )
    logging.info(f"Новое состояние ресурса {resource_cd}: {state}")


def commit_transaction(**context) -> None:
    tx = context['task_instance'].xcom_pull(task_ids='open_transaction')
    tx_manager_client.commit(tx_uid=tx['tx_uid'], tx_token=tx['tx_token'])
    logging.info(f'Коммит транзакции {tx["tx_uid"]} прошел успешно!')


def rollback_transaction(**context) -> None:
    if tx := context['task_instance'].xcom_pull(task_ids='open_transaction'):
        tx_manager_client.rollback(tx_uid=tx['tx_uid'], tx_token=tx['tx_token'])
        logging.info(f'Rollback транзакции {tx["tx_uid"]} прошел успешно!')
    else:
        logging.info('Транзакция не была открыта.')


def check_delta(**context) -> None:
    current_attempt = 1
    attempts = context['dag_run'].conf.get('attempts') or 30
    attempt_timeout = context['dag_run'].conf.get('attempt_timeout') or 60
    version_id = context['task_instance'].xcom_pull(task_ids='lock_resource')
    while not get_version_status(version_id=version_id):
        if current_attempt > attempts:
            raise RuntimeError(
                f'Количество попыток ожидания статуса обновления версии '
                f'{version_id} исчерпано'
            )
        current_attempt += 1
        sleep(attempt_timeout)
    logging.info(f"Накат дельты версии {version_id} успешен")


def get_version_status(version_id: int) -> bool:
    response = delta_manager_client.get_version_status(version_id=version_id)
    logging.info(response.message)
    if response.status in (StatusType.progress, StatusType.idle):
        return False
    return True

job_loger_callbacks = {
    'on_success_callback': JobLogger(),
    'on_failure_callback': JobLogger(),
}

with DAG(
        dag_id=Path(__file__).stem,
        start_date=datetime.datetime(2021, 11, 16),
        schedule_interval=None,
        catchup=False,
        **job_loger_callbacks,
) as dag:
    open_transaction = PythonOperator(
        task_id="open_transaction",
        provide_context=True,
        python_callable=open_transaction,
        **job_loger_callbacks,
    )

    lock_resource = PythonOperator(
        task_id="lock_resource",
        provide_context=True,
        python_callable=lock_resource,
        **job_loger_callbacks,
    )

    create_delta_table = PythonOperator(
        task_id="create_delta_table",
        provide_context=True,
        python_callable=create_delta_table,
        **job_loger_callbacks,
    )

    validate_row_count = PythonOperator(
        task_id="validate_row_count",
        provide_context=True,
        python_callable=validate_row_count,
        **job_loger_callbacks,
    )

    update_resources_state = PythonOperator(
        task_id="update_resources_state",
        provide_context=True,
        python_callable=update_resources_state,
        **job_loger_callbacks,
    )

    commit_transaction = PythonOperator(
        task_id="commit_transaction",
        provide_context=True,
        python_callable=commit_transaction,
        **job_loger_callbacks,
    )

    rollback_transaction = PythonOperator(
        task_id='rollback_transaction',
        provide_context=True,
        python_callable=rollback_transaction,
        trigger_rule=TriggerRule.ALL_FAILED,
        **job_loger_callbacks,
    )

    check_delta = PythonOperator(
        task_id="check_delta",
        provide_context=True,
        python_callable=check_delta,
        **job_loger_callbacks,
    )

    chain(
        open_transaction,
        lock_resource,
        create_delta_table,
        validate_row_count,
        update_resources_state,
        [commit_transaction, rollback_transaction],
    )

    commit_transaction >> check_delta
