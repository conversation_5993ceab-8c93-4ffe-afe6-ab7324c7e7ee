import datetime
import hashlib
import logging
import os
from pathlib import Path
from time import sleep
from typing import (
    Any,
    Dict,
)
import psycopg2

import requests
from airflow import DAG
from airflow.models.baseoperator import chain
from airflow.operators.python import PythonOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.hooks.base import BaseHook

from ceh_core_libs.utils import <PERSON><PERSON><PERSON>ger
from ceh_core_rdv.config import config
from dwh_services_utils.clients import TxManagerClient
from dwh_services_utils.clients.ceh import ceh_provider_v_0_6
from dwh_services_utils.clients.ceh.client_factory import CehClientFactory
from dwh_services_utils.clients import DeltaManagerClient
from dwh_services_utils.clients.client_factory import (
    System,
    TxClientFactory,
    client_factory,
    DeltaClientFactory,
)
from dwh_services_utils.schemas.delta_manager.schemas import StatusType

# Example of dag config
"""
{
    "resource_cd": "example_resource_cd", 
    "query": "update example_target_table set example_target_table.t=2 where 1=1", 
    "target": "example_target_table", 
    "author": "example_author"
}
"""

# DWH services clients
ceh_factory: CehClientFactory = client_factory.get_factory(System.CEH)
# noinspection PyTypeChecker
ceh_provider_client: ceh_provider_v_0_6.CehProviderClient = ceh_factory.get_client(
    base_url=config.ceh_provider.base_url,
    version='0.6'
)
tx_factory: TxClientFactory = client_factory.get_factory(System.TX)
# noinspection PyTypeChecker
tx_manager_client: TxManagerClient = tx_factory.get_client(
    base_url=config.tx_manager.base_url
)
# noinspection PyTypeChecker
delta_factory: DeltaClientFactory = client_factory.get_factory(System.DELTA)
delta_manager_client: DeltaManagerClient = delta_factory.get_client(
    base_url=config.delta_manager.base_url
)

permission = 'CHECK_DAG_RUN_PERMISSION'
DATA_FIX_MANAGER_URL = os.environ.get('DATA_FIX_MANAGER_URL')
CHECK_DAG_RUN_PERMISSION = os.environ.get(permission) \
    if os.environ.get(permission) else False


class DagRunPermissionDenied(Exception):
    """
    Raised if DAG ran directly (without using Data fix manager)
    """
    pass


class ValidationError(Exception):
    pass


if hasattr(config.gp, 'etl_connection_name') and config.gp.etl_connection_name:
    con = BaseHook.get_connection(config.gp.etl_connection_name)
    gp_db = con.schema
    gp_user = con.login
    gp_host = con.host

    GP_CONNECT = dict(
        dbname=gp_db,
        user=gp_user,
        host=gp_host,
        port=con.port,
        password=con.password
    )
else:
    gp_db = config.gp.database
    gp_user = config.gp.username
    gp_host = config.gp.host

    GP_CONNECT = dict(
        dbname=gp_db,
        user=gp_user,
        host=gp_host,
        port=config.gp.port,
        password=config.gp.password
    )


def _execute_query(
        connection,
        cursor,
        query,
        params=None,
        success_msg='SQL success executed',
        error_msg='SQL throw error',
        is_raise_next=False,
        is_commit=True,
        is_log_rowcount=False,
        is_fetch=False,
        is_log_notices=False
):
    """
    Execute sql query, catch error, log results

    :param connection: psycopg connection
    :param cursor: psycopg cursor
    :param query: sql query for execute
    :param params: params for sql query
    :param success_msg: log message if execution successful
    :param error_msg: log message if execution throw error
    :param is_raise_next: True, then not ignore error and raise exception next (fail task)
    :param is_commit: commit executed query result
    :param is_log_rowcount: log rowcount
    :param is_fetch: fetch rows result
    :param is_log_notices: log result notices
    :return: rowcount, returned_query_data
    """

    len_notices = len(connection.notices)
    try:
        logging.info(f"SQL:\n{cursor.mogrify(query, params if params is not None else []).decode('utf-8')}")
        cursor.execute(query, params)

    except Exception as exc:
        logging.error(f'{error_msg}. {exc}')
        if is_raise_next:
            raise
        else:
            connection.rollback()
            return -1, None
    else:
        if is_log_notices:
            for notice in connection.notices[len_notices:]:
                logging.info(f'NOTICE: {notice}.')
        rowcount = cursor.rowcount
        rows = cursor.fetchall() if is_fetch and cursor.pgresult_ptr is not None else None

        if is_commit:
            connection.commit()

        if is_log_rowcount:
            success_msg += f' Count rows: {rowcount}'
        logging.info(success_msg)

        return rowcount, rows


def build_origin(context: Dict[str, Any]) -> Dict[str, Any]:
    return {
        'author': context['dag_run'].conf.get('author'),
        'algorithm_uid': 'datafix',
        'engine': 'airflow',
        'dag_id': context['dag'].dag_id,
        'run_id': context['dag_run'].run_id,
        'service': 'data_fix_query',
        'change_request': context['dag_run'].conf.get('change_request'),
        'table_name': context['dag_run'].conf.get('target'),
        'json_all': context['dag_run'].conf,
    }


def build_operation_dict(context: Dict[str, Any]) -> Dict[str, Any]:
    dag_conf = context['dag_run'].conf
    query = dag_conf.get('query')
    conf = {
        'by_src': {},
        'common': {
            'query_type': dag_conf.get('query_type', ''),
            'query': query,
            'datafix_version_id': context['task_instance'].xcom_pull(
                task_ids='lock_resource',
            ),
            'changed_version_ids': dag_conf.get('updated_version_ids', []),
        },
    }
    return {
        'origin': build_origin(context),
        'source': {
            'conf': conf,
            'results': {
                'by_src': {},
                'common': {},
            },
        },
        'type': 'query',
        'statement': query,
    }


def check_run_permission(**context) -> None:
    logging.info('Проверка запуска ДАГа из Data fix manager.')
    if not CHECK_DAG_RUN_PERMISSION:
        logging.info(
            'Проверка запуска ДАГа из Data fix manager отключена. '
            'См. переменную среды CHECK_DAG_RUN_PERMISSION.'
        )
        return
    query = context['dag_run'].conf.get('query')
    md5query = hashlib.md5(query.encode()).hexdigest()

    logging.info(f'{DATA_FIX_MANAGER_URL}/api/1.0/dags/datafix/check_md5query/{md5query}, запрос {query}')
    response = requests.get(f'{DATA_FIX_MANAGER_URL}/api/1.0/dags/datafix/check_md5query/{md5query}')
    logging.info(f'Код ответа {response.status_code}')

    if not response.status_code == 200:
        raise DagRunPermissionDenied()
    else:
        logging.info(f'Проверка запуска ДАГа из Data fix manager прошла успешно')


def open_transaction() -> Dict[str, str]:
    tx = tx_manager_client.create_transaction(commit_timeout=14400)
    logging.info(f'tx_uid: {tx.tx_uid}')
    return {'tx_uid': str(tx.tx_uid), 'tx_token': tx.tx_token}


def lock_resource(**context) -> int:
    tx = context['task_instance'].xcom_pull(task_ids='open_transaction')
    resource_code = context['dag_run'].conf.get("resource_cd")
    attempts = context['dag_run'].conf.get('attempts') or 30
    attempt_timeout = context['dag_run'].conf.get('attempt_timeout') or 60
    version_id = ceh_provider_client.lock(
        resource_codes=[resource_code],
        tx_uid=tx['tx_uid'],
        attempts=attempts,
        attempt_timeout=attempt_timeout,
        origin=build_origin(context),
    )
    return version_id


def validate_row_count(**context) -> None:
    # logging.info(f"GP_CONNECT={GP_CONNECT}")
    changed_row_count = context['dag_run'].conf.get('changed_row_count')
    query: str = context['dag_run'].conf.get('query')
    queryl = query.lower().lstrip()
    if not queryl.startswith('delete'):
        logging.info(f'Query {query} is not delete type. Skipping changed row count validation')
        return

    get_row_count_sql = queryl.replace('delete', 'select count(1)')
    with psycopg2.connect(**GP_CONNECT) as conn:
        with conn.cursor() as cur:
            logging.info(f"Getting row count: {get_row_count_sql}")
            _, res = _execute_query(
                connection=conn,
                cursor=cur,
                query=get_row_count_sql,
                params=(),
                success_msg=f'Successful getting row count of delta table.',
                error_msg=f'Error during getting row count of delta table.',
                is_commit=True,
                is_log_rowcount=False,
                is_fetch=True,
                is_raise_next=True,
            )
            if res[0][0] != changed_row_count:
                raise ValidationError(f"Expected changed row count {changed_row_count} does not match actual row count {res[0][0]}")


def update_resources_state(**context) -> None:
    tx = context['task_instance'].xcom_pull(task_ids='open_transaction')
    resource_cd = context['dag_run'].conf.get('resource_cd')
    state = ceh_provider_client.update_resource_state(
        resource_cd=resource_cd,
        tx_uid=tx['tx_uid'],
        operation=build_operation_dict(context)
    )
    logging.info(f"Новое состояние ресурса {resource_cd}: {state}")


def commit_transaction(**context) -> None:
    tx = context['task_instance'].xcom_pull(task_ids='open_transaction')
    tx_manager_client.commit(tx_uid=tx['tx_uid'], tx_token=tx['tx_token'])
    logging.info(f'Коммит транзакции {tx["tx_uid"]} прошел успешно!')


def rollback_transaction(**context) -> None:
    if tx := context['task_instance'].xcom_pull(task_ids='open_transaction'):
        tx_manager_client.rollback(tx_uid=tx['tx_uid'], tx_token=tx['tx_token'])
        logging.info(f'Rollback транзакции {tx["tx_uid"]} прошел успешно!')
    else:
        logging.info('Транзакция не была открыта.')


def check_delta(**context) -> None:
    current_attempt = 1
    attempts = context['dag_run'].conf.get('attempts') or 30
    attempt_timeout = context['dag_run'].conf.get('attempt_timeout') or 60
    version_id = context['task_instance'].xcom_pull(task_ids='lock_resource')
    while not get_version_status(version_id=version_id):
        if current_attempt > attempts:
            raise RuntimeError(
                f'Количество попыток ожидания статуса обновления версии '
                f'{version_id} исчерпано'
            )
        current_attempt += 1
        sleep(attempt_timeout)
    logging.info(f"Накат дельты версии {version_id} успешен")


def get_version_status(version_id: int) -> bool:
    response = delta_manager_client.get_version_status(version_id=version_id)
    logging.info(response.message)
    if response.status in (StatusType.progress, StatusType.idle):
        return False
    return True

job_loger_callbacks = {
    'on_success_callback': JobLogger(),
    'on_failure_callback': JobLogger(),
}

with DAG(
        dag_id=Path(__file__).stem,
        start_date=datetime.datetime(2021, 11, 16),
        schedule_interval=None,
        catchup=False,
        **job_loger_callbacks,
) as dag:
    check_run_permission = PythonOperator(
        task_id="check_run_permission",
        provide_context=True,
        python_callable=check_run_permission,
        **job_loger_callbacks,
    )

    open_transaction = PythonOperator(
        task_id="open_transaction",
        provide_context=True,
        python_callable=open_transaction,
        **job_loger_callbacks,
    )

    lock_resource = PythonOperator(
        task_id="lock_resource",
        provide_context=True,
        python_callable=lock_resource,
        **job_loger_callbacks,
    )

    validate_row_count = PythonOperator(
        task_id="validate_row_count",
        provide_context=True,
        python_callable=validate_row_count,
        **job_loger_callbacks,
    )

    update_resources_state = PythonOperator(
        task_id="update_resources_state",
        provide_context=True,
        python_callable=update_resources_state,
        **job_loger_callbacks,
    )

    commit_transaction = PythonOperator(
        task_id="commit_transaction",
        provide_context=True,
        python_callable=commit_transaction,
        trigger_rule=TriggerRule.NONE_FAILED_OR_SKIPPED,
        **job_loger_callbacks,
    )

    rollback_transaction = PythonOperator(
        task_id='rollback_transaction',
        provide_context=True,
        python_callable=rollback_transaction,
        trigger_rule=TriggerRule.ALL_FAILED,
        **job_loger_callbacks,
    )

    check_delta = PythonOperator(
        task_id="check_delta",
        provide_context=True,
        python_callable=check_delta,
        **job_loger_callbacks,
    )

    chain(
        check_run_permission,
        open_transaction,
        lock_resource,
        validate_row_count,
        update_resources_state,
        [commit_transaction, rollback_transaction],
    )

    commit_transaction >> check_delta
