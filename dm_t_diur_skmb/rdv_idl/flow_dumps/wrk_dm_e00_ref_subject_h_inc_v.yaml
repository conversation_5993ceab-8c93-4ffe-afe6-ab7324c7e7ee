schema_version: 2.0
metadata:
  author: E<PERSON><PERSON>ev
  version: "1.0"
  description: выгрузка из источника e00_ref_subject_h_inc_v
  tags:
    - "cf: cf_dm_e00_ref_subject_h_inc_v"
    - "wrk: wrk_dm_e00_ref_subject_h_inc_v"
  group: test
flows:
  - id: wrk_dm_e00_ref_subject_h_inc_v
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: поток
    metadata:
      - name: instance_id
        datatype: int
        type: in
        default: 5000
      - name: stable_version_id
        datatype: int
        type: in
        default: 10
      - name: src_version_id
        datatype: int
        type: in
        default: 5
      - name: target_resource_name
        type: in
        default: ceh.dm_t_diur_skmb_tech.e00_ref_subject_h_inc_v_in
      - name: target_table_name
        type: in
        default: dm_t_diur_skmb_tech.e00_ref_subject_h_inc_v_in
      - name: source_resource_names
        type: in
        default:
          - ceh.dm_t_diur_skmb_work.e00_ref_subject_h_inc_v
    tasks:
      - id: check_dag_running
        description: Проверка состояние текущего дага
        type: ceh_core_idl.app.operators.core.check_dag_running
      - id: get_load_id # Шаг выделения уникального load_id. Шаг обязателен.
        description: Генерация уникального ключа потока
        type: ceh_core_idl.app.operators.etl.generate_load_id_operator
        metadata:
          - name: load_id
            datatype: str
            type: out
        properties:
          instance_id: ${instance_id}
          table_name: ${target_table_name}
        sequencer:
          dependencies:
            - ref: check_dag_running
      - id: open_transactions # Шаг открытия транзакции. Шаг обязателен
        description: открытие транзакции
        type: ceh_core_idl.app.operators.services.create_tx_operator
        properties:
          tx_timeout: 15000
        metadata:
          - name: tx_uid
            datatype: str
            type: out
          - name: tx_token
            datatype: str
            type: out
        sequencer:
          dependencies:
            - ref: get_load_id
      - id: block_resources # Шаг блокировки ресурса или группы ресурсов. Шаг обязателен
        description: блокировка ресурса
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          target_resource_names:
            - ${target_resource_name}
        sequencer:
          dependencies:
            - ref: open_transactions
      - id: get_last_report_date
        description: Получам дату последней загрузки
        type: ceh_core_idl.app.operators.core.sql_operator
        properties:
          query:
            select coalesce(max(sbjh_start_date),to_date('1900-01-01', 'yyyy-mm-dd')) as sbjh_start_date from dm_t_diur_skmb_tech.e00_ref_subject_h_inc_v_in
          result_flg: true
          result_limit: 1
        sequencer:
          dependencies:
            - ref: block_resources
      - id: extract_last_report_date
        description: Извлечём из джэйсона, полученного на предыдущем шаге, наш параметр
        type: ceh_core_idl.app.operators.core.json_query_operator
        properties:
          input_data: ${get_last_report_date.result.response}
          jq_expression: 'map(. |= {"sbjh_start_date": .sbjh_start_date})[]'
          fetch_type: first
        sequencer:
          dependencies:
            - ref: get_last_report_date
      - id: get_data_ref_deal_v #выгрузим наши данные
        description: выгрузка из источник.e00_ref_subject_h_inc_v
        type: ceh_core_idl.app.operators.etl.create_sql_proc_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          sql_select_statement: select sbjh_id, sbjh_gid, sbjh_start_date, sbjh_end_date, sbjh_row_status, sbjh_audit_id, sbjh_hash, sbjh_name, sbjh_name_eng,
                                sbjh_short_name, sbjh_last_name, sbjh_first_name, sbjh_patronymic_name, sbjh_client_status_cd, sbjh_is_client_flg,
                                sbjh_is_resident_flg, sbjh_client_type_cd, sbjh_client_subtype_cd, sbjh_inn, sbjh_okopf_cd, sbjh_okved,
                                sbjh_okved_cd, sbjh_okonh, sbjh_okonh_cd, sbjh_ogrn, sbjh_okpo, sbjh_kpp, sbjh_okfs_cd, sbjh_okato_cd,
                                sbjh_okogu, sbjh_clgrp_gid, sbjh_sex_cd, sbjh_birth_date, sbjh_birth_place, sbjh_job_place, sbjh_job_title,
                                sbjh_user_id_type_cd, sbjh_user_id_number, sbjh_pension_fund_reg_nmb, sbjh_sec_market_licence_nmb,
                                sbjh_business_size_cd, sbjh_borrower_type_cd, sbjh_rg_cntry_gid, sbjh_rg_city, sbjh_rg_address,
                                sbjh_rg_postal_code, sbjh_lc_cntry_gid, sbjh_lc_city, sbjh_lc_address, sbjh_lc_postal_code, sbjh_director_name,
                                sbjh_accountant_name, sbjh_bnk_swift_code, sbjh_bnk_bic, sbjh_bnk_license_number, sbjh_bnk_license_valid_from,
                                sbjh_bnk_license_valid_till, sbjh_bnk_bank_status_cd, sbjh_bnk_kgrko_name, sbjh_bnk_swift_name,
                                sbjh_ownership_capital_amnt, sbjh_kio, sbjh_score_value, sbjh_credit_rtng_gid, sbjh_industry_type_cd,
                                sbjh_opf_cd, sbjh_reference, sbjh_subject_status_cd, sbjh_financial_rating_cd, sbjh_bankruptcy_stage_cd,
                                sbjh_exec_auth_level_cd, sbjh_exec_auth_type_cd, sbjh_interbnk_sect_cl_cd, sbjh_is_bank_flg,
                                sbjh_is_lending_agency_flg, sbjh_is_serv_by_treasuer_flg, sbjh_is_exec_auth_flg, sbjh_is_manage_bud_funds_flg,
                                sbjh_is_financial_org_flg, sbjh_ownrsh_capital_crnc_gid, sbjh_is_offshore_flg, sbjh_kgrko_gid,
                                sbjh_bnk_bic_gid, sbjh_swift_gid, sbjh_bnk_gid, sbjh_cstm_client_type, sbjh_cstm_client_type_cd,
                                sbjh_nat_monopoly_flg, sbjh_egrul_name, sbjh_phone_number, sbjh_region_lc_okato_cd,
                                sbjh_region_reg_okato_cd, sbjh_ogrn_date, sbjh_ogrn_place, sbjh_bank_relation_cd,
                                sbjh_parent_company_clnt_gid, sbjh_otrsl_rsbu_cd, sbjh_business_size_cs_cd,
                                sbjh_is_fund_particip_flg, sbjh_is_fatca_flg, sbjh_is_fatca_abs_flg, sbjh_fatca_tax_payer_number,
                                sbjh_fatca_giin, sbjh_fatca_type_cd, sbjh_fatca_fi_status_cd, sbjh_source_id, sbjh_source,
                                sbjh_is_ofi_8966_flg, sbjh_crm_industry, sbjh_crm_frontoffice, sbjh_dataset_number,
                                sbjh_parent_sbj_gid, sbjh_src_change_date, sbjh_ownership_capital_part, sbjh_fatca_control_date,
                                sbjh_urfu_flg, sbjh_user_id_issue_date, sbjh_citizenship_cntry_gid, 'A' as record_mode
                                from dm_t_diur_skmb_work.e00_ref_subject_h_inc_v
                                where sbjh_start_date > date '${extract_last_report_date.jq_response.sbjh_start_date}'
          analyze_flg: true
          result_dataset:
            schema: dm_t_diur_skmb_dlt
            name: e00_ref_subject_h_inc_v_dlt
            physical_options:
              distribute_type: replicated
              materialize_flg: true
        sequencer:
          dependencies:
            - ref: extract_last_report_date
      - id: update_resource_state # Шаг обновления состояния у целевого ресурса. Шаг обязателен. Закрытие транакци.
        description: Обновление состояния ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          stable_version_id: ${stable_version_id}
          target_resource_name: ${target_resource_name}
          target_table_names:
            - ${target_table_name}
          source_resource_names: ${source_resource_names}
          max_src_version_id: ${src_version_id}
          load_id: ${get_load_id.load_id}
          algorithm_name: test_alg2
          txi_table_name: 'dm_t_diur_skmb_dlt.e00_ref_subject_h_inc_v_dlt'
        sequencer:
          dependencies:
            - ref: get_data_ref_deal_v
      - id: commit # Коммит целевого ресурса. Шаг обязателен
        description: Коммит операция
        type: ceh_core_idl.app.operators.services.commit_transaction_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          transaction_token: 'null' #${open_transactions.tx_token}
        sequencer:
          dependencies:
            - ref: update_resource_state
      - id: error_occurred # Выявление ошибок выполнения дага. Шаг обязателен
        description: Любой успех
        type: airflow.operators.dummy.DummyOperator
        sequencer:
          dependencies:
            - ref: update_resource_state
              condition: ${not update_resource_state.status}
          condition:
            any
      - id: rollback # Выполнение роллбека транзакции. Шаг обязателен
        description: Ролбэк операция
        type: ceh_core_idl.app.operators.services.rollback_transaction_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          transaction_token: 'null' #${open_transactions.tx_token}
        sequencer:
          dependencies:
            - ref: error_occurred
            - ref: open_transactions
          condition:
            all