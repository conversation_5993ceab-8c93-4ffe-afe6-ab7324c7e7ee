schema_version: 2.0
metadata:
  author: E<PERSON>ornev
  version: "1.0"
  description: выгрузка кредитный сделок из КИХа 
  tags:
    - "cf: cf_dm_e46_ref_acnt_deal_link_h_o_v_reg_inc"
    - "wrk: wrk_dm_e46_ref_acnt_deal_link_h_o_v_reg_inc"
  group: test
flows:
  - id: wrk_dm_e46_ref_acnt_deal_link_h_o_v_reg_inc
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: поток
    metadata:
      - name: instance_id
        datatype: int
        type: in
        default: 5000
      - name: stable_version_id
        datatype: int
        type: in
        default: 10
      - name: src_version_id
        datatype: int
        type: in
        default: 5
      - name: target_resource_name
        type: in
        default: ceh.dm_t_diur_skmb_tech.e46_ref_acnt_deal_link_h_o_v_reg_in
      - name: target_table_name
        type: in
        default: dm_t_diur_skmb_tech.e46_ref_acnt_deal_link_h_o_v_reg_in
      - name: source_resource_names
        type: in
        default:
          - ceh.dm_t_diur_skmb_work.e46_ref_acnt_deal_link_h_o_v_reg
      - name: acdlh_start_date
        type: in
    tasks:
      - id: check_dag_running
        description: Проверка состояние текущего дага
        type: ceh_core_idl.app.operators.core.check_dag_running
      - id: get_load_id # Шаг выделения уникального load_id. Шаг обязателен.
        description: Генерация уникального ключа потока
        type: ceh_core_idl.app.operators.etl.generate_load_id_operator
        metadata:
          - name: load_id
            datatype: str
            type: out
        properties:
          instance_id: ${instance_id}
          table_name: ${target_table_name}
        sequencer:
          dependencies:
            - ref: check_dag_running
      - id: open_transactions # Шаг открытия транзакции. Шаг обязателен
        description: открытие транзакции
        type: ceh_core_idl.app.operators.services.create_tx_operator
        properties:
          tx_timeout: 60000
        metadata:
          - name: tx_uid
            datatype: str
            type: out
          - name: tx_token
            datatype: str
            type: out
        sequencer:
          dependencies:
            - ref: get_load_id
      - id: block_resources # Шаг блокировки ресурса или группы ресурсов. Шаг обязателен
        description: блокировка ресурса
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          target_resource_names:
            - ${target_resource_name}
        sequencer:
          dependencies:
            - ref: open_transactions
      - id: get_data_account #выгрузим наши данные
        description: выгрузка из источник.e46_ref_acnt_deal_link_h_o_v_reg
        type: ceh_core_idl.app.operators.etl.create_sql_proc_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          sql_select_statement: select acdlh_gid, cast(acdlh_start_date as timestamp), acdlh_end_date, acdlh_id, acdlh_audit_id, acdlh_hash,
                                acdlh_row_status, acdlh_acnt_gid, acdlh_source, acdlh_acnt_deal_link_type_cd, acdlh_link_begin_date,
                                acdlh_link_end_date, acdlh_deal_gid, acdlh_branch_dept_gid, acdlh_dataset_number,
                                acdlh_source_id, acdlh_detail_acnt_number, 'A' as record_mode
                                from dm_t_diur_skmb_work.e46_ref_acnt_deal_link_h_o_v_reg
                                where cast(acdlh_start_date as date) > date '${acdlh_start_date}'
          result_dataset:
            schema: dm_t_diur_skmb_dlt
            name: e46_ref_acnt_deal_link_h_o_v_reg_dlt
            physical_options:
              distribute_type: replicated
              materialize_flg: true
        sequencer:
          dependencies:
            - ref: block_resources
      - id: update_resource_state # Шаг обновления состояния у целевого ресурса. Шаг обязателен. Закрытие транакци.
        description: Обновление состояния ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          stable_version_id: ${stable_version_id}
          target_resource_name: ${target_resource_name}
          target_table_names:
            - ${target_table_name}
          source_resource_names: ${source_resource_names}
          max_src_version_id: ${src_version_id}
          load_id: ${get_load_id.load_id}
          algorithm_name: test_alg2
          txi_table_name: 'dm_t_diur_skmb_dlt.e46_ref_acnt_deal_link_h_o_v_reg_dlt'
        sequencer:
          dependencies:
            - ref: get_data_account
      - id: commit # Коммит целевого ресурса. Шаг обязателен
        description: Коммит операция
        type: ceh_core_idl.app.operators.services.commit_transaction_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          transaction_token: 'null' #${open_transactions.tx_token}
        sequencer:
          dependencies:
            - ref: update_resource_state
      - id: error_occurred # Выявление ошибок выполнения дага. Шаг обязателен
        description: Любой успех
        type: airflow.operators.dummy.DummyOperator
        sequencer:
          dependencies:
            - ref: update_resource_state
              condition: ${not update_resource_state.status}
          condition:
            any
      - id: rollback # Выполнение роллбека транзакции. Шаг обязателен
        description: Ролбэк операция
        type: ceh_core_idl.app.operators.services.rollback_transaction_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          transaction_token: 'null' #${open_transactions.tx_token}
        sequencer:
          dependencies:
            - ref: error_occurred
            - ref: open_transactions
          condition:
            all