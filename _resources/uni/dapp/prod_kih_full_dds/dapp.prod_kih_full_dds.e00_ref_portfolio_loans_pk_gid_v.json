{"resource_cd": "dapp.prod_kih_full_dds.e00_ref_portfolio_loans_pk_gid_v", "resource_desc": "DAPP таблица prod_kih_full_dds.e00_ref_portfolio_loans_pk_gid_v", "tags": [], "features": {}, "status": {"is_readonly": false, "is_maintenance": false, "is_deleted": false}, "connections": "{{ hive_dapp_conn | for_instance('dapp_kih_full_dds') }}", "datasets": [{"name": "e00_ref_portfolio_loans_pk_gid_v", "schema_name": "{{ dapp_kih_full_dds.schema }}", "filter": "", "physical_options": {}}], "metrics": {"is_locked": "{{ metrics.is_locked | for_instance('dapp_kih_full_dds', resource_cd='dapp.prod_kih_full_dds.e00_ref_portfolio_loans_pk_gid_v') }}", "locked_by": "{{ metrics.locked_by | for_instance('dapp_kih_full_dds', resource_cd='dapp.prod_kih_full_dds.e00_ref_portfolio_loans_pk_gid_v') }}", "dataset_max_date_to": "{{ metrics.dapp_rp_dataset_max_date_to | for_instance('dapp_kih_full_dds', database=dapp_kih_full_dds.schema, table='e00_ref_portfolio_loans_pk_gid_v') }}", "dapp_actual_dttm": {"id": "dapp.prod_kih_full_dds.e00_ref_portfolio_loans_pk_gid_v.dapp_actual_dttm", "connection": "arena_conn", "query": "SELECT date_trunc('second', now())", "query_parameters": null, "default": "1900-01-01T00:00:00", "refresh": "PDT00H01M"}}}