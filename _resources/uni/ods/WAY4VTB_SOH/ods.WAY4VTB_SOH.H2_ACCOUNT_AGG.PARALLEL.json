{"resource_cd": "ods.WAY4VTB_SOH.H2_ACCOUNT_AGG.PARALLEL", "resource_desc": "Параллельный забор ods таблица WAY4VTB_SOH.H2_ACCOUNT_AGG", "tags": [], "features": {}, "status": {"is_readonly": true, "is_maintenance": true, "is_deleted": false}, "connections": "{{ jdbc_conn | for_instance('ods_parallel_way4vtb_soh') }}", "datasets": [{"name": "H2_ACCOUNT_AGG", "schema_name": "{{ ods_way4vtb_soh.schema }}", "filter": "", "physical_options": {}}], "metrics": {"dataset_max_date_to": "{{ metrics.dataset_max_date_to | for_instance('ods_parallel_way4vtb_soh', schema=ods_way4vtb_soh.schema, table='H2_ACCOUNT_AGG', type='SOH') }}", "wf_max_date_to": {"id": "ods.WAY4VTB_SOH.H2_ACCOUNT_AGG.PARALLEL.wf_max_date_to", "connection": "odbc", "query": "select 'alias_metric'", "query_parameters": null, "default": "1900-01-01T00:00:00.00000", "refresh": "PDT00H01M", "alias": "dataset_max_date_to"}, "is_locked": "{{ metrics.is_locked | for_instance('ods_parallel_way4vtb_soh', resource_cd='ods.WAY4VTB_SOH.H2_ACCOUNT_AGG.PARALLEL') }}", "locked_by": "{{ metrics.locked_by | for_instance('ods_parallel_way4vtb_soh', resource_cd='ods.WAY4VTB_SOH.H2_ACCOUNT_AGG.PARALLEL') }}", "wayn_actual_dttm": {"id": "ods.WAY4VTB_SOH.H2_ACCOUNT_AGG.PARALLEL.wayn_actual_dttm", "connection": "odbc", "query": "SELECT {{ ods_ods_meta.schema }}.LOAD_TABLE_TO_DATE('WAY4VTB_SOH', 'H2_ACCOUNT_AGG') FROM DUAL;", "query_parameters": null, "default": "1900-01-01T00:00:00", "refresh": "PDT00H01M"}}}