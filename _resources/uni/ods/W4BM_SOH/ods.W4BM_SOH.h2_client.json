{"resource_cd": "ods.W4BM_SOH.H2_CLIENT", "resource_desc": "ods таблица W4BM_SOH.H2_CLIENT", "tags": [], "features": {}, "status": {"is_readonly": true, "is_maintenance": true, "is_deleted": false}, "connections": "{{ jdbc_conn | for_instance('ods_w4bm_soh') }}", "datasets": [{"name": "H2_CLIENT", "schema_name": "{{ ods_w4bm_soh.schema }}", "filter": "", "physical_options": {}}], "metrics": {"dataset_max_date_to": "{{ metrics.dataset_max_date_to | for_instance('ods_w4bm_soh', schema=ods_w4bm_soh.schema, table='H2_CLIENT', type='SOH') }}", "wf_max_date_to": {"id": "ods.W4BM_SOH.H2_CLIENT.wf_max_date_to", "connection": "odbc", "query": "select 'alias_metric'", "query_parameters": null, "default": "1900-01-01T00:00:00.00000", "refresh": "PDT00H01M", "alias": "dataset_max_date_to"}, "is_locked": "{{ metrics.is_locked | for_instance('ods_w4bm_soh', resource_cd='ods.W4BM_SOH.H2_CLIENT') }}", "locked_by": "{{ metrics.locked_by | for_instance('ods_w4bm_soh', resource_cd='ods.W4BM_SOH.H2_CLIENT') }}", "wfbm_actual_dttm": {"id": "ods.W4BM_SOH.H2_CLIENT.wfbm_actual_dttm", "connection": "odbc", "query": "SELECT {{ ods_ods_meta.schema }}.LOAD_TABLE_TO_DATE('W4BM_SOH', 'H2_CLIENT') FROM DUAL;", "query_parameters": null, "default": "1900-01-01T00:00:00", "refresh": "PDT00H01M"}}}