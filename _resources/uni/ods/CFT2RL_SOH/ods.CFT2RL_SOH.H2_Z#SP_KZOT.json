{"resource_cd": "ods.CFT2RL_SOH.H2_Z#SP_KZOT", "resource_desc": "ods таблица CFT2RL_SOH.H2_Z#SP_KZOT", "tags": ["ODS", "ODS_CFT"], "features": {}, "status": {"is_readonly": true, "is_maintenance": true, "is_deleted": false}, "connections": "{{ jdbc_conn | for_instance('ods_cft2rl_soh') }}", "datasets": [{"name": "H2_Z#SP_KZOT", "schema_name": "{{ ods_cft2rl_soh.schema }}", "filter": "", "physical_options": {}}], "metrics": {"dataset_max_date_to": "{{ metrics.dataset_max_date_to | for_instance('ods_cft2rl_soh', schema=ods_cft2rl_soh.schema, table='H2_Z#SP_KZOT', type='SOH') }}", "wf_max_date_to": {"id": "ods.CFT2RL_SOH.H2_Z#SP_KZOT.wf_max_date_to", "connection": "odbc", "query": "select 'alias_metric'", "query_parameters": null, "default": "1900-01-01T00:00:00.00000", "refresh": "PDT00H01M", "alias": "dataset_max_date_to"}, "cftm_actual_dttm": {"id": "ods.CFT2RL_SOH.H2_Z#SP_KZOT.cftm_actual_dttm", "connection": "odbc", "query": "SELECT {{ ods_ods_meta.schema }}.LOAD_TABLE_TO_DATE('CFT2RL_SOH', 'H2_Z#SP_KZOT') FROM DUAL;", "query_parameters": null, "default": "1900-01-01T00:00:00", "refresh": "PDT00H01M"}, "is_locked": "{{ metrics.is_locked | for_instance('ods_cft2rl_soh', resource_cd='ods.CFT2RL_SOH.H2_Z#SP_KZOT') }}", "locked_by": "{{ metrics.locked_by | for_instance('ods_cft2rl_soh', resource_cd='ods.CFT2RL_SOH.H2_Z#SP_KZOT') }}"}}