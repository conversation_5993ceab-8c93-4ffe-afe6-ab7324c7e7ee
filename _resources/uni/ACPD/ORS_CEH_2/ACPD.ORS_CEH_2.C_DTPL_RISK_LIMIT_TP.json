{"resource_cd": "ACPD.ORS_CEH_2.C_DTPL_RISK_LIMIT_TP", "resource_desc": "ACPD table ORS_CEH_2.C_DTPL_RISK_LIMIT_TP", "tags": [], "features": {}, "status": {"is_readonly": true, "is_maintenance": true, "is_deleted": false}, "connections": "{{ jdbc_conn | for_instance('mac_ors_ceh_2') }}", "datasets": [{"name": "C_DTPL_RISK_LIMIT_TP", "schema_name": "{{ mac_ors_ceh_2.schema }}", "filter": "", "physical_options": {}}], "metrics": {"mac_last_update_date": "{{ metrics.mac_last_update_date | for_instance('mac_ors_ceh_2', schema=mac_ors_ceh_2.schema, table='C_DTPL_RISK_LIMIT_TP') }}", "is_locked": "{{ metrics.is_locked | for_instance('mac_ors_ceh_2', resource_cd='ACPD.ORS_CEH_2.C_DTPL_RISK_LIMIT_TP') }}", "locked_by": "{{ metrics.locked_by | for_instance('mac_ors_ceh_2', resource_cd='ACPD.ORS_CEH_2.C_DTPL_RISK_LIMIT_TP') }}", "acpd_actual_dttm": {"id": "ACPD.ORS_CEH_2.C_DTPL_RISK_LIMIT_TP.acpd_actual_dttm", "connection": "odbc", "query": "SELECT current_timestamp FROM DUAL", "query_parameters": null, "default": "1900-01-01T00:00:00", "refresh": "PDT00H01M"}}}