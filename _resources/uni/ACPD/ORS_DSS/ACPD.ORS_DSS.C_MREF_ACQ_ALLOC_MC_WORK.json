{"resource_cd": "ACPD.ORS_DSS.C_MREF_ACQ_ALLOC_MC_WORK", "resource_desc": "ACPD таблица ORS_DSS.C_MREF_ACQ_ALLOC_MC_WORK", "tags": ["ACPD"], "features": {}, "status": {"is_readonly": false, "is_maintenance": true, "is_deleted": false}, "connections": "{{ jdbc_conn | for_instance('mac_rawd_ors_dss') }}", "datasets": [{"name": "C_MREF_ACQ_ALLOC_MC_WORK", "schema_name": "{{ mac_rawd_ors_dss.schema }}", "filter": "", "physical_options": {}}], "metrics": {"mac_last_update_date": "{{ metrics.mac_last_update_date | for_instance('mac_rawd_ors_dss', schema=mac_rawd_ors_dss.schema, table='C_MREF_ACQ_ALLOC_MC_WORK') }}", "is_locked": "{{ metrics.is_locked | for_instance('mac_rawd_ors_dss', resource_cd='ACPD.ORS_DSS.C_MREF_ACQ_ALLOC_MC_WORK') }}", "locked_by": "{{ metrics.locked_by | for_instance('mac_rawd_ors_dss', resource_cd='ACPD.ORS_DSS.C_MREF_ACQ_ALLOC_MC_WORK') }}"}}