{"resource_cd": "ACPD.ORS_CEH.C_DTPL_ISSUANCE_PRGM", "resource_desc": "ACPD table   ORS_CEH.C_DTPL_ISSUANCE_PRGM ref", "tags": ["ACPD"], "features": {}, "status": {"is_readonly": true, "is_maintenance": true, "is_deleted": false}, "connections": "{{ jdbc_conn | for_instance('mac_ors_ceh') }}", "datasets": [{"name": "C_DTPL_ISSUANCE_PRGM", "schema_name": "{{ mac_ors_ceh.schema }}", "filter": "", "physical_options": {}}], "metrics": {"mac_last_update_date": "{{ metrics.mac_last_update_date | for_instance('mac_ors_ceh', schema=mac_ors_ceh.schema, table='C_DTPL_ISSUANCE_PRGM') }}", "is_locked": "{{ metrics.is_locked | for_instance('mac_ors_ceh', resource_cd='ACPD.ORS_CEH.C_DTPL_ISSUANCE_PRGM') }}", "locked_by": "{{ metrics.locked_by | for_instance('mac_ors_ceh', resource_cd='ACPD.ORS_CEH.C_DTPL_ISSUANCE_PRGM') }}"}}