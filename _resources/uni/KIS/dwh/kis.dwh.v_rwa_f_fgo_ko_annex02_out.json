{"resource_cd": "kis.dwh.v_rwa_f_fgo_ko_annex02_out", "resource_desc": "kis таблица dwh.V_RWA_F$FGO_KO_ANNEX02_OUT", "tags": [], "features": {}, "status": {"is_readonly": true, "is_maintenance": true, "is_deleted": false}, "connections": "{{ jdbc_conn | for_instance('kiss_dwh') }}", "datasets": [{"name": "V_RWA_F$FGO_KO_ANNEX02_OUT", "schema_name": "{{ kiss_dwh.schema }}", "filter": "", "physical_options": {}}], "metrics": {"is_locked": "{{ metrics.is_locked | for_instance('kiss_dwh', resource_cd='kis.dwh.v_rwa_f_fgo_ko_annex02_out') }}", "locked_by": "{{ metrics.locked_by | for_instance('kiss_dwh', resource_cd='kis.dwh.v_rwa_f_fgo_ko_annex02_out') }}", "wf_max_date_to": {"id": "kis.dwh.v_rwa_f_fgo_ko_annex02_out.wf_max_date_to", "connection": "odbc", "query": "SELECT now()", "query_parameters": null, "query_exec_timeout": null, "default": "1900-01-01T00:00:00.00000", "refresh": "PDT00H01M", "alias": ""}, "wf_dataset_max_lsn_to": {"id": "kis.dwh.v_rwa_f_fgo_ko_annex02_out.wf_dataset_max_lsn_to", "connection": "odbc", "query": "SELECT coalesce(max(LOAD_ID), -1) FROM DWH.V_DTPL_READER$UPLOAD_STATUSES s WHERE status='C' AND VIEW_NAME =?", "query_parameters": [{"name": "table", "value": "V_RWA_F$FGO_KO_ANNEX02_OUT", "sqltype": "<PERSON><PERSON><PERSON>"}], "default": "-1", "refresh": "PDT00H01M"}, "kis_actual_dttm": {"id": "kis.dwh.v_rwa_f_fgo_ko_annex02_out.kiss_actual_dttm", "connection": "odbc", "query": "SELECT now()", "query_parameters": null, "default": "1900-01-01T00:00:00.00000", "refresh": "PDT00H01M"}}}