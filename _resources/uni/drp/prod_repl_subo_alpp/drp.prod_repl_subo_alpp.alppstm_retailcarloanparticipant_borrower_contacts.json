{"resource_cd": "drp.prod_repl_subo_alpp.alppstm_retailcarloanparticipant_borrower_contacts", "resource_desc": "drp таблица prod_repl_subo_alpp.alppstm_retailcarloanparticipant_borrower_contacts", "tags": [], "features": {}, "status": {"is_readonly": true, "is_maintenance": true, "is_deleted": false}, "connections": "{{ hive_conn | for_instance('drp_repl_subo_alpp') }}", "datasets": [{"name": "ALPPSTM_RETAILCARLOANPARTICIPANT_BORROWER_CONTACTS", "schema_name": "{{ drp_repl_subo_alpp.schema }}", "filter": "", "physical_options": {}}], "metrics": {"is_locked": "{{ metrics.is_locked | for_instance('drp_repl_subo_alpp', resource_cd='drp.prod_repl_subo_alpp.alppstm_retailcarloanparticipant_borrower_contacts') }}", "locked_by": "{{ metrics.locked_by | for_instance('drp_repl_subo_alpp', resource_cd='drp.prod_repl_subo_alpp.alppstm_retailcarloanparticipant_borrower_contacts') }}", "dataset_max_date_to": {"id": "drp.prod_repl_subo_alpp.alppstm_retailcarloanapplication.dataset_max_date_to", "connection": "arena_conn", "query": "SELECT now()::timestamp(0)", "query_parameters": null, "default": "1900-01-01", "refresh": "PDT00H01M"}, "alpp_actual_dttm": {"id": "drp.prod_repl_subo_alpp.alppstm_retailcarloanparticipant_borrower_contacts.alpp_actual_dttm", "connection": "arena_conn", "query": "SELECT date_trunc('second', now())", "query_parameters": null, "default": "1900-01-01T00:00:00", "refresh": "PDT00H01M"}}}