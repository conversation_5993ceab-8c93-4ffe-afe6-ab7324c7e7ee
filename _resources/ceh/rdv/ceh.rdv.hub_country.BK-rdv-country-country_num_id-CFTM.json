{"resource_desc": "таблица rdv.hub_country (rdv)", "tags": ["customer", "owner"], "features": {"domain": "rdv", "source_system": "pkb"}, "metrics": {}, "resource_cd": "ceh.rdv.hub_country.BK-rdv-country-country_num_id-CFTM", "is_readonly": false, "is_deleted": null, "datasets": [{"name": "hub_country", "schema_name": "rdv", "filter": "", "columns": [{"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "bk_type", "type": "text", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "country_id", "type": "text", "primary_key": false, "nullable": false}, {"name": "country_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}