{"resource_desc": "Table mart_type_debt_vid_debt_rtll", "tags": ["mart_type_debt_vid_debt_rtll"], "features": {"domain": "RDV"}, "metrics": {"rtll_actual_dttm": {"id": "ceh.rdv.mart_type_debt_vid_debt_rtll:rtll_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"rtll_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.mart_type_debt_vid_debt_rtll", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "mart_type_debt_vid_debt_rtll", "schema_name": "rdv", "filter": "", "columns": [{"name": "su", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "sn", "type": "smallint", "primary_key": false, "nullable": true}, {"name": "c_type_debt_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "c_type_calc_debt", "type": "smallint", "primary_key": false, "nullable": true}, {"name": "c_to_product", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "c_tip_acc_vek", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "c_tip_acc_debt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "c_separate", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_sec_period_unit_intervals_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_sec_period_num_intervals", "type": "integer", "primary_key": false, "nullable": true}, {"name": "c_scheme_prc", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_prp_comiss", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_prioritet_part", "type": "smallint", "primary_key": false, "nullable": true}, {"name": "c_prioritet", "type": "smallint", "primary_key": false, "nullable": true}, {"name": "c_pps_tip_acc_rk", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "c_pps_sort_rest", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "c_plan_prioritet", "type": "smallint", "primary_key": false, "nullable": true}, {"name": "c_plan_dt_oper_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "c_plan_ct_oper_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "c_param_for_round", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "c_off_guar", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_meth_build_rec", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_journal_prc", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_favour_period", "type": "smallint", "primary_key": false, "nullable": true}, {"name": "c_deff_rec_oper_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "c_debets", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "c_date_stop", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_code", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_check_diff", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_charge_on_date_e", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_calc_with_comiss", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_calc_complete", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_base_tax_debt_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "c_base_part_debt_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "c_bal_acc_str", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_acc_reserv", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "ods_processed_dt", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "effective_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "type_debt_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}