{"resource_desc": "Таблица rdv.hub_property", "tags": [], "features": {"domain": "RDV"}, "metrics": {"alpp_actual_dttm": {"id": "ceh.rdv.hub_property.BK_RDV_property_property_seq_ALPP:alpp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"alpp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.hub_property.BK_RDV_property_property_seq_ALPP", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "hub_property", "schema_name": "rdv", "filter": "", "columns": [{"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "bk_type", "type": "text", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "property_id", "type": "text", "primary_key": false, "nullable": false}, {"name": "property_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}