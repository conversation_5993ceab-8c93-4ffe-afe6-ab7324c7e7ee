{"resource_desc": "Table ceh.rdv.mart_app_prop_cl_collateral_participants_r_ipo", "tags": ["ipo", "RBI5", "rdv", "mart_app_prop_cl_collateral_participants_r_ipo"], "features": {"domain": "RDV", "schema": "rdv", "table": "mart_app_prop_cl_collateral_participants_r_ipo", "partition_rule": "no"}, "metrics": {"ipo_actual_dttm": {"id": "ceh.rdv.mart_app_prop_cl_collateral_participants_r_ipo:ipo_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"ipo_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.mart_app_prop_cl_collateral_participants_r_ipo", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "mart_app_prop_cl_collateral_participants_r_ipo", "schema_name": "rdv", "filter": "", "columns": [{"name": "property_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "application_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "client_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "effective_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "property_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "client_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "participants_role", "type": "text", "primary_key": false, "nullable": true}, {"name": "participants_id_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "participants_id_subsystem", "type": "text", "primary_key": false, "nullable": true}, {"name": "participants_id_version", "type": "text", "primary_key": false, "nullable": true}, {"name": "participants_id_updatetime", "type": "text", "primary_key": false, "nullable": true}, {"name": "participants_id_createtime", "type": "text", "primary_key": false, "nullable": true}, {"name": "changeid", "type": "text", "primary_key": false, "nullable": true}, {"name": "ceh_object_hash", "type": "text", "primary_key": false, "nullable": true}, {"name": "ceh_hash", "type": "text", "primary_key": false, "nullable": true}, {"name": "changetimestamp", "type": "text", "primary_key": false, "nullable": true}], "physical_options": ""}]}