{"resource_cd": "ceh.rdv.map_anti_fraud_counterparty_link_type", "resource_desc": "Table rdv.map_anti_fraud_counterparty_link_type", "tags": ["dict", "map"], "features": {"domain": "rdv", "source_system": "ACPD"}, "is_readonly": false, "datasets": [{"name": "map_anti_fraud_counterparty_link_type", "schema_name": "rdv", "filter": "", "columns": [{"name": "aux$compos_key", "type": "text", "primary_key": true, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": true}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "anti_fraud_counterparty_link_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "anti_fraud_counterparty_link_type_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "src_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "src_anti_fraud_counterparty_link_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "src_anti_fraud_counterparty_link_type_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "effective_from_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": true}], "physical_options": ""}], "metrics": {"acpd_actual_dttm": {"id": "ceh.rdv.map_anti_fraud_counterparty_link_type:acpd_actual_dttm", "query": "[.version.operations[].source.conf.by_src[].acpd_actual_dttm | values] | min", "default": "default_value"}}, "is_deleted": false}