{"resource_desc": "Table mart_retailclientsalarysegmentdecision_employeer_salp", "tags": ["salp", "PIONNR3", "rdv"], "features": {"domain": "RDV", "schema": "rdv", "table": "mart_retailclientsalarysegmentdecision_employeer_salp", "partition_rule": "no"}, "metrics": {"salp_actual_dttm": {"id": "ceh.rdv.mart_retailclientsalarysegmentdecision_employeer_salp:salp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"salp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.mart_retailclientsalarysegmentdecision_employeer_salp", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "mart_retailclientsalarysegmentdecision_employeer_salp", "schema_name": "rdv", "filter": "", "columns": [{"name": "counterparty_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "effective_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "employeer_dk", "type": "text", "primary_key": true, "nullable": false}, {"name": "id_createtime", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_subsystem", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_updatetime", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_version", "type": "text", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": false}], "physical_options": ""}]}