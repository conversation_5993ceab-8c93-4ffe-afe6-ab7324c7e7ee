{"resource_desc": "Таблица rdv.hub_deal", "tags": [], "features": {"domain": "RDV"}, "metrics": {"dapp_actual_dttm": {"id": "ceh.rdv.hub_deal.BK-rdv-deal-deal_code-DKO:dapp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"dapp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}, "dks_actual_dttm": {"id": "ceh.rdv.hub_deal.BK-rdv-deal-deal_code-DKO:dks_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"dks_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}, "dko_actual_dttm": {"id": "ceh.rdv.hub_deal.BK-rdv-deal-deal_code-DKO:dko_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"dko_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.hub_deal.BK-rdv-deal-deal_code-DKO", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "hub_deal", "schema_name": "rdv", "filter": "", "columns": [{"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "bk_type", "type": "text", "primary_key": false, "nullable": false}, {"name": "deal_id", "type": "text", "primary_key": false, "nullable": false}, {"name": "deal_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}