{"resource_desc": "table mart_loan_app_que_incomelist_documents_tbcv", "tags": ["mart", "cprt", "mart_loan_app_que_incomelist_documents_tbcv"], "features": {"domain": "rdv", "source_system": "TBCV"}, "metrics": {"dapp_actual_dttm": {"id": "ceh.rdv.mart_loan_app_que_incomelist_documents_tbcv:dapp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"dapp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.mart_loan_app_que_incomelist_documents_tbcv", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "mart_loan_app_que_incomelist_documents_tbcv", "schema_name": "rdv", "filter": "", "columns": [{"name": "hdp_processed_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "changeid", "type": "text", "primary_key": false, "nullable": true}, {"name": "retcustanapp_que_incomelist_documents_uploaddate", "type": "text", "primary_key": false, "nullable": true}, {"name": "retcustanapp_que_incomelist_documents_expirationdate", "type": "text", "primary_key": false, "nullable": true}, {"name": "retcustanapp_que_incomelist_documents_documentsize", "type": "text", "primary_key": false, "nullable": true}, {"name": "retcustanapp_que_incomelist_documents_documentname", "type": "text", "primary_key": false, "nullable": true}, {"name": "retcustanapp_que_incomelist_documents_documentuid", "type": "text", "primary_key": false, "nullable": true}, {"name": "retcustanapp_que_incomelist_documents_documentcomment", "type": "text", "primary_key": false, "nullable": true}, {"name": "retcustanapp_que_incomelist_documents_confirmingdocument", "type": "text", "primary_key": false, "nullable": true}, {"name": "retcustanapp_que_incomelist_documents_hash", "type": "text", "primary_key": false, "nullable": true}, {"name": "effective_dttm", "type": "timestamp", "primary_key": true, "nullable": false}, {"name": "effective_date", "type": "date", "primary_key": false, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "income_document_dk", "type": "text", "primary_key": true, "nullable": false}], "physical_options": ""}]}