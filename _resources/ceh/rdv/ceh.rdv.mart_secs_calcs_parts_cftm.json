{"resource_desc": "Table mart_secs_calcs_parts_cftm", "tags": ["mart_secs_calcs_parts_cftm"], "features": {"schema": "RDV", "table": "mart_secs_calcs_parts_cftm", "partition_rule": "no"}, "metrics": {"cftm_actual_dttm": {"id": "ceh.rdv.mart_secs_calcs_parts_cftm:cftm_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"cftm_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.mart_secs_calcs_parts_cftm", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "mart_secs_calcs_parts_cftm", "schema_name": "rdv", "filter": "", "columns": [{"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "invalid_id", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "su", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "sn", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "c_part_code", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "c_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "c_factor", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "c_code", "type": "text", "primary_key": false, "nullable": true}, {"name": "ods_processed_dt", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "effective_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "id_dk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}