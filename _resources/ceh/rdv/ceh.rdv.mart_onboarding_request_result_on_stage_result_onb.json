{"resource_desc": "table mart_onboarding_request_result_on_stage_result_onb (rdv)", "tags": ["mart", "mart_onboarding_request_result_on_stage_result_onb"], "features": {"domain": "rdv", "source_system": "onb"}, "metrics": {"onb_actual_dttm": {"id": "ceh.rdv.mart_onboarding_request_result_on_stage_result_onb:onb_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"onb_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.mart_onboarding_request_result_on_stage_result_onb", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "mart_onboarding_request_result_on_stage_result_onb", "schema_name": "rdv", "filter": "", "columns": [{"name": "invalid_id", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "hdp_processed_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "checkresult_hash", "type": "text", "primary_key": false, "nullable": true}, {"name": "checkstage_description", "type": "text", "primary_key": false, "nullable": true}, {"name": "checkstage_code", "type": "text", "primary_key": false, "nullable": true}, {"name": "onboardingprocess_id_createtime", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "onboardingprocess_id_updatetime", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "onboardingprocess_id_version", "type": "text", "primary_key": false, "nullable": true}, {"name": "onboardingprocess_id_subsystem", "type": "text", "primary_key": false, "nullable": true}, {"name": "onboardingprocess_id_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "onboardingprocess_id_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "onboardingprocess_id_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "changetimestamp", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "changetype", "type": "text", "primary_key": false, "nullable": true}, {"name": "changeid_dk", "type": "text", "primary_key": true, "nullable": false}, {"name": "effective_dttm", "type": "timestamp", "primary_key": true, "nullable": false}, {"name": "id_subsystem", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "onboarding_request_result_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}