{"resource_desc": "table ceh.rdv.mart_participant_borrower_lssemd", "tags": ["zi6", "mart_participant_borrower_lssemd"], "features": {"domain": "rdv", "source_system": "ALPP"}, "metrics": {"alpp_actual_dttm": {"id": "ceh.rdv.mart_participant_borrower_lssemd:alpp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"alpp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv.mart_participant_borrower_lssemd", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "mart_participant_borrower_lssemd", "schema_name": "rdv", "filter": "", "columns": [{"name": "invalid_id", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "customerloanapplicationrole", "type": "text", "primary_key": false, "nullable": true}, {"name": "partyref_inn", "type": "text", "primary_key": false, "nullable": true}, {"name": "partyref_prospectid", "type": "text", "primary_key": false, "nullable": true}, {"name": "partyref_mdmid", "type": "text", "primary_key": false, "nullable": true}, {"name": "partyref_unc", "type": "text", "primary_key": false, "nullable": true}, {"name": "partyref_partytype", "type": "text", "primary_key": false, "nullable": true}, {"name": "partyref_id_createtime", "type": "text", "primary_key": false, "nullable": true}, {"name": "partyref_id_updatetime", "type": "text", "primary_key": false, "nullable": true}, {"name": "partyref_id_version", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "partyref_id_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "client_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "applicationref_id_createtime", "type": "text", "primary_key": false, "nullable": true}, {"name": "applicationref_id_updatetime", "type": "text", "primary_key": false, "nullable": true}, {"name": "applicationref_id_version", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "applicationref_id_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "roletype", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_createtime", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_updatetime", "type": "text", "primary_key": false, "nullable": true}, {"name": "id_version", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "id_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "borrower_id", "type": "text", "primary_key": false, "nullable": false}, {"name": "effective_date", "type": "date", "primary_key": true, "nullable": false}], "physical_options": ""}]}