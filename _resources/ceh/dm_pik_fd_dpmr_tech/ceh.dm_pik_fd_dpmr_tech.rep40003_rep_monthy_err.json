{"resource_desc": "ceh.dm_pik_fd_dpmr_tech.rep40003_rep_monthy_err", "tags": ["rep40003_rep_monthy_err"], "features": {}, "metrics": {}, "resource_cd": "ceh.dm_pik_fd_dpmr_tech.rep40003_rep_monthy_err", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "rep40003_rep_monthy_err", "schema_name": "dm_pik_fd_dpmr_tech", "filter": "", "columns": [{"name": "contract_type_cd__", "type": "text", "primary_key": false, "nullable": true}, {"name": "total_square_amt_", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "seller_nm_", "type": "text", "primary_key": false, "nullable": true}, {"name": "seller_inn_", "type": "text", "primary_key": false, "nullable": true}, {"name": "agreement_cost_rur_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "registration_record_dt_", "type": "date", "primary_key": false, "nullable": true}, {"name": "loan_goal", "type": "integer", "primary_key": false, "nullable": true}, {"name": "client_snils", "type": "text", "primary_key": false, "nullable": true}, {"name": "husband_wife_snils", "type": "text", "primary_key": false, "nullable": true}, {"name": "contract_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "open_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "issue_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "issue_rur_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "mat_cap_amt", "type": "integer", "primary_key": false, "nullable": true}, {"name": "int_rate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "contract_period", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "monthly_int_payment_rur_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "okato_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_okato_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "oktmo_", "type": "text", "primary_key": false, "nullable": true}, {"name": "ownership_reg_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "client_category", "type": "text", "primary_key": false, "nullable": true}, {"name": "fact_registration_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "data_type", "type": "integer", "primary_key": false, "nullable": true}, {"name": "correction_period", "type": "date", "primary_key": false, "nullable": true}, {"name": "correction_reason", "type": "text", "primary_key": false, "nullable": true}, {"name": "perc_loan_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "key_rate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "rate_to_compensate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "to_compensate_rur_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "to_compensate_rur_amt_round", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "is_subsidized_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "loan_errors_count_part", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_square_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_seller_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_price_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_pledge_coeff_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_pledge_reg_dt_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_brwr_snils_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_cobrwr_snils_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_repeat_loan_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_brwr_age_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_cobrwr_age_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_child_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_loan_period_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_sp_oktmo_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_pledge_addr_reg_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_loan_amt_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "err_dfo_place_flg", "type": "integer", "primary_key": false, "nullable": true}, {"name": "agreement_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "application_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "app_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "fixed_rate_program_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_address_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "marital_status", "type": "text", "primary_key": false, "nullable": true}, {"name": "fo", "type": "text", "primary_key": false, "nullable": true}, {"name": "close_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "source_system_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "full_seller_nm_inn", "type": "text", "primary_key": false, "nullable": true}, {"name": "report_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "report_month_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": true}, {"name": "phone_num_cleaned", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "email_address", "type": "text", "primary_key": false, "nullable": true}, {"name": "customer_okato_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "report_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "processed_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "registration_record_dt_orig", "type": "date", "primary_key": false, "nullable": true}, {"name": "init_internal_org_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "init_internal_org_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "chief_employee_full_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "zam_employee_full_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "init_lender_tax_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "ro_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "husband_open_age", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "property_type_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "realestate", "type": "text", "primary_key": false, "nullable": true}, {"name": "industry_type_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "counterparty_developer", "type": "text", "primary_key": false, "nullable": true}, {"name": "counterparty_seller", "type": "text", "primary_key": false, "nullable": true}, {"name": "osz", "type": "numeric", "primary_key": false, "nullable": true}], "physical_options": ""}]}