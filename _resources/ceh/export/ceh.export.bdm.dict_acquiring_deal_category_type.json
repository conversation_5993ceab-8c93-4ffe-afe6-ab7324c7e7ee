{"resource_desc": "AUX export resource for bdm.dict_acquiring_deal_category_type", "tags": ["aux", "export"], "features": {"source_system": "aux"}, "metrics": {}, "resource_cd": "ceh.export.bdm.dict_acquiring_deal_category_type", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "dict_acquiring_deal_category_type", "schema_name": "bdm", "filter": "", "columns": [{"name": "acquiring_deal_category_type_cd", "type": "text", "primary_key": true, "nullable": true}, {"name": "effective_from_date", "type": "text", "primary_key": true, "nullable": true}], "physical_options": ""}]}