{"resource_desc": "Export resource for dm_rb_crm_bcrm.agg_customer_churn_category_un", "tags": ["export", "dm_rb_crm_bcrm"], "features": {}, "metrics": {}, "resource_cd": "ceh.export.dm_rb_crm_bcrm.agg_customer_churn_category_un", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "agg_customer_churn_category_un", "schema_name": "dm_rb_crm_bcrm", "filter": "", "columns": [{"name": "mdm_customer_rk", "type": "text", "primary_key": true, "nullable": true}, {"name": "churn_category_value_cd", "type": "text", "primary_key": true, "nullable": true}], "physical_options": ""}]}