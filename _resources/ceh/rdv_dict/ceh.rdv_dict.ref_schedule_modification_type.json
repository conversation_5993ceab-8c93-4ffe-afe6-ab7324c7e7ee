{"resource_desc": "Table rdv_dict.ref_schedule_modification_type", "tags": ["dict", "ref"], "features": {"domain": "rdv_dict", "source_system": "ACPD"}, "metrics": {"acpd_actual_dttm": {"id": "ceh.rdv_dict.ref_schedule_modification_type:acpd_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"acpd_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.rdv_dict.ref_schedule_modification_type", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "ref_schedule_modification_type", "schema_name": "rdv_dict", "filter": "", "columns": [{"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "effective_from_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "schedule_modification_type_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "schedule_modification_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": true}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "invalid_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "aux$compos_key", "type": "text", "primary_key": true, "nullable": true}], "physical_options": ""}]}