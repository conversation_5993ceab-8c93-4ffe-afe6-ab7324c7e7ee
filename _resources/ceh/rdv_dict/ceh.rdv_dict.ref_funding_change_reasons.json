{"resource_cd": "ceh.rdv_dict.ref_funding_change_reasons", "resource_desc": "Table rdv_dict.ref_funding_change_reasons", "tags": ["dict", "map"], "features": {"domain": "rdv_dict", "source_system": "ACPD"}, "is_readonly": false, "datasets": [{"name": "ref_funding_change_reasons", "schema_name": "rdv_dict", "filter": "", "columns": [{"name": "aux$compos_key", "type": "text", "primary_key": false, "nullable": true}, {"name": "invalid_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": true}, {"name": "valid_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "funding_change_reasons_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "funding_change_reasons_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "funding_change_reasons_code", "type": "text", "primary_key": false, "nullable": true}, {"name": "local_src_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "uniq_funding_change_reasons_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "uniq_code", "type": "text", "primary_key": false, "nullable": true}, {"name": "effective_from_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": true}], "physical_options": ""}], "metrics": {"acpd_actual_dttm": {"id": "ceh.rdv_dict.ref_funding_change_reasons:acpd_actual_dttm", "query": "[.version.operations[].source.conf.by_src[].acpd_actual_dttm | values] | min", "default": "default_value"}}, "is_deleted": false}