{"resource_desc": "Table idl.dict_application_status", "tags": ["dict", "application", "status"], "features": {"domain": "IDL", "source_system": "CSV"}, "metrics": {"acpd_actual_dttm": {"id": "ceh.idl.dict_application_status:acpd_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select((.key | endswith(\"_actual_dttm\")) and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.dict_application_status", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "dict_application_status", "schema_name": "idl", "filter": "", "columns": [{"name": "application_status_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "effective_from_date", "type": "date", "primary_key": true, "nullable": true}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "application_status_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_status_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": true}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": true, "nullable": true}, {"name": "aux$compos_key", "type": "text", "primary_key": false, "nullable": true}], "physical_options": ""}]}