{"resource_desc": "Table ceh.idl.blink_application_x_department.ccpp_crca", "tags": ["ceh.idl.blink_application_x_department.ccpp_crca"], "features": {"domain": "IDL", "source_system": "CCPP"}, "metrics": {"ccpp_actual_dttm": {"id": "ceh.idl.blink_application_x_department.ccpp_crca:ccpp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"ccpp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.blink_application_x_department.ccpp_crca", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "blink_application_x_department", "schema_name": "idl", "filter": "", "columns": [{"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": false}, {"name": "effective_from_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "application_department_link_type_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "department_rk", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "application_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}