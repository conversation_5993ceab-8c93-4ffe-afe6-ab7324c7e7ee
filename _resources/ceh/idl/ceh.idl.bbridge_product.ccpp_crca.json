{"resource_desc": "Table idl.bbridge_product", "tags": ["idl", "bbridge_product"], "features": {"domain": "idl"}, "metrics": {"ccpp_crca_actual_dttm": {"id": "ceh.idl.bbridge_product.ccpp_crca:ccpp_crca_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"ccpp_crca_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}, "ccpp_actual_dttm": {"id": "ceh.idl.bbridge_product.ccpp_crca:ccpp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"ccpp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.bbridge_product.ccpp_crca", "is_readonly": false, "is_deleted": null, "datasets": [{"name": "bbridge_product", "schema_name": "idl", "filter": "", "columns": [{"name": "market_status_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "on_sale_indicator_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_rater_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_passport_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "product_legacy_code", "type": "text", "primary_key": false, "nullable": true}, {"name": "auto_prolongation_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "client_product_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_end_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "product_begin_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "profile_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "product_status_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "business_size_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "effective_from_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "product_group_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "parent_product_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "product_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_code", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "product_level_no", "type": "smallint", "primary_key": false, "nullable": true}], "physical_options": ""}, {"name": "sal_product", "schema_name": "idl", "filter": "", "columns": [{"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "secondary_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "primary_id", "type": "text", "primary_key": false, "nullable": false}, {"name": "primary_rk", "type": "bigint", "primary_key": false, "nullable": false}], "physical_options": ""}]}