{"resource_desc": "Table idl.sal_application", "tags": ["sal_application.onb"], "features": {"domain": "IDL", "source_system": "ONB"}, "metrics": {"onb_actual_dttm": {"id": "ceh.idl.sal_application.onb:onb_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"onb_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.sal_application.onb", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "sal_application", "schema_name": "idl", "filter": "", "columns": [{"name": "bk_schema_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "secondary_rk", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "primary_id", "type": "text", "primary_key": false, "nullable": false}, {"name": "primary_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}