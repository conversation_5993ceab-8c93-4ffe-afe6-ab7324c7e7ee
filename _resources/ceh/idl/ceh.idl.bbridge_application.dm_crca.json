{"resource_desc": "Table idl.bbridge_application", "tags": ["idl", "dummi", "bbridge_application"], "features": {"domain": "idl"}, "metrics": {"ccpp_actual_dttm": {"id": "ceh.idl.bbridge_application.dm_crca:ccpp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"ccpp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.bbridge_application.dm_crca", "is_readonly": false, "is_deleted": null, "datasets": [{"name": "bbridge_application", "schema_name": "idl", "filter": "", "columns": [{"name": "application_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "effective_from_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": false}, {"name": "application_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_begin_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "application_end_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "parent_application_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "application_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "currency_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "employee_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "counterparty_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "region_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "request_product_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "application_request_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "request_credit_term_months_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_marketing_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "receive_product_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "application_receive_total_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "application_receive_cash_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "discount_payment_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "discount_payment_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "monthly_payment_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "interest_rate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "interest_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "overdue_interest_rate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "penalty_interest_rate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "payment_day_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "card_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "preapproved_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "holiday_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "insurance_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "service_package_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "max_limit_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "application_security_cat_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "prepayment_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "application_credit_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "financing_purpose_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_credit_form_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "receive_credit_term_months_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_sale_method_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_credit_class_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_request_rur_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "request_interest_rate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "distant_internal_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "increase_limit_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "sales_channel_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "department_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "application_method_sign_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_advertising_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_uid_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_global_uid_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "electro_signature_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "edo_available_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "application_create_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "application_add_id_txt", "type": "text", "primary_key": false, "nullable": true}, {"name": "bk_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "bk_src_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "synth_code", "type": "text", "primary_key": false, "nullable": true}, {"name": "client_other_company_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "client_other_company_code", "type": "text", "primary_key": false, "nullable": true}], "physical_options": ""}]}