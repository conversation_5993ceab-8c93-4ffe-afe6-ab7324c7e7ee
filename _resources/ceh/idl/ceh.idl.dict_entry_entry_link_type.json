{"resource_desc": "dict_entry_entry_link_type (IDL)", "tags": ["dict", "dict_entry_entry_link_type"], "features": {"domain": "IDL", "source_system": "KIH"}, "metrics": {"kih_actual_dttm": {"id": "ceh.idl.dict_entry_entry_link_type:kih_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"kih_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.dict_entry_entry_link_type", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "dict_entry_entry_link_type", "schema_name": "idl", "filter": "", "columns": [{"name": "hash_diff", "type": "text", "primary_key": false, "nullable": true}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "effective_from_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "entry_entry_link_type_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "entry_entry_link_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "aux$compos_key", "type": "text", "primary_key": false, "nullable": true}, {"name": "\"aux$compos_key\"", "type": "text", "primary_key": true, "nullable": false}], "physical_options": ""}]}