{"resource_desc": "Table idl.blink_deal_x_counterparty", "tags": ["blink_deal_x_counterparty", "WAYN_WVTB"], "features": {"domain": "IDL", "source_system": "WAYN_WVTB"}, "metrics": {"wayn_actual_dttm": {"id": "ceh.idl.blink_deal_x_counterparty.wayn_wvtb:wayn_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"wayn_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}, "rtll_actual_dttm": {"id": "ceh.idl.blink_deal_x_counterparty.wayn_wvtb:rtll_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"rtll_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}, "prof_wayn_actual_dttm": {"id": "ceh.idl.blink_deal_x_counterparty.wayn_wvtb:prof_wayn_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"prof_wayn_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}, "cftm_actual_dttm": {"id": "ceh.idl.blink_deal_x_counterparty.wayn_wvtb:cftm_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"cftm_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.blink_deal_x_counterparty.wayn_wvtb", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "blink_deal_x_counterparty", "schema_name": "idl", "filter": "", "columns": [{"name": "algo_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "department_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "deal_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "deal_counterparty_link_type_cd", "type": "text", "primary_key": true, "nullable": true}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": false}, {"name": "effective_from_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "counterparty_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "deal_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}