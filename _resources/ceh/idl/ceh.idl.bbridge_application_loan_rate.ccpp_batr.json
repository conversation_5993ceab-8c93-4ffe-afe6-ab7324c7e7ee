{"resource_desc": "ceh.idl.bbridge_application_loan_rate.ccpp_batr", "tags": ["IDL", "ccpp_batr"], "features": {"domain": "IDL", "source_system": "ccpp_batr"}, "metrics": {"ccpp_actual_dttm": {"id": "ceh.idl.bbridge_application_loan_rate.ccpp_batr:ccpp_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"ccpp_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.bbridge_application_loan_rate.ccpp_batr", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "bbridge_application_loan_rate", "schema_name": "idl", "filter": "", "columns": [{"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": false}, {"name": "effective_from_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "application_loan_rate_type_cd", "type": "text", "primary_key": true, "nullable": false}, {"name": "application_loan_rate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "application_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}