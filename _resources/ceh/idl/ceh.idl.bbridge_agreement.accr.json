{"resource_desc": "table bbridge_agreement.accr (idl)", "tags": ["idl", "agreement", "bbridge_agreement"], "features": {"domain": "IDL", "source_system": "ACCR"}, "metrics": {"accr_actual_dttm": {"id": "ceh.idl.bbridge_agreement.accr:accr_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"accr_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.bbridge_agreement.accr", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "sal_agreement", "schema_name": "idl", "filter": "", "columns": [{"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "secondary_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "bk_schema_cd", "type": "text", "primary_key": true, "nullable": false}, {"name": "primary_rk", "type": "bigint", "primary_key": false, "nullable": false}], "physical_options": ""}, {"name": "bbridge_agreement", "schema_name": "idl", "filter": "", "columns": [{"name": "agreement_uid_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "agreement_uid", "type": "uuid", "primary_key": false, "nullable": true}, {"name": "agreement_status_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "department_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "parent_agreement_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "employee_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "agreement_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "agreement_sum", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "currency_rk", "type": "integer", "primary_key": false, "nullable": true}, {"name": "counterparty_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "agreement_fact_end_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "agreement_plan_end_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "agreement_start_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "agreement_open_date", "type": "date", "primary_key": false, "nullable": true}, {"name": "agreement_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "agreement_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "agreement_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "agreement_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": false}, {"name": "effective_from_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "agreement_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}