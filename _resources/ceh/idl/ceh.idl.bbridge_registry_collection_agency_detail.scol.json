{"resource_desc": "Table idl.bbridge_registry_collection_agency_detail.scol", "tags": ["bbridge_registry_collection_agency_detail"], "features": {"schema": "IDL", "table": "bbridge_registry_collection_agency_detail", "partition_rule": "no"}, "metrics": {"sblcl_actual_dttm": {"id": "ceh.idl.bbridge_registry_collection_agency_detail.scol:sblcl_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"sblcl_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.bbridge_registry_collection_agency_detail.scol", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "bbridge_registry_collection_agency_detail", "schema_name": "idl", "filter": "", "columns": [{"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "exlusion_reason_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "employee_rk", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "finish_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "start_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "agreement_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "effective_to_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "effective_from_dttm", "type": "timestamp", "primary_key": true, "nullable": false}, {"name": "registry_collection_agency_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}