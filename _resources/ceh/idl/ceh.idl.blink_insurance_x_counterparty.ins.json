{"resource_desc": "Таблица ceh.idl.blink_insurance_x_counterparty.ins", "tags": ["ceh.idl.blink_insurance_x_counterparty.ins"], "features": {"domain": "IDL", "source_system": "INS"}, "metrics": {"ins_actual_dttm": {"id": "ceh.idl.blink_insurance_x_counterparty.ins:ins_actual_dttm", "query": "[.last_sources[].conf.by_src | select(. != null) | .[] | to_entries | .[] | select(.key == \"ins_actual_dttm\" and .value != \"default_value\" and .value != null).value] | min", "default": "default_value"}}, "resource_cd": "ceh.idl.blink_insurance_x_counterparty.ins", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "blink_insurance_x_counterparty", "schema_name": "idl", "filter": "", "columns": [{"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "effective_to_date", "type": "date", "primary_key": false, "nullable": false}, {"name": "effective_from_date", "type": "date", "primary_key": true, "nullable": false}, {"name": "insurance_counterparty_link_type_cd", "type": "text", "primary_key": true, "nullable": false}, {"name": "counterparty_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "insurance_rk", "type": "bigint", "primary_key": true, "nullable": false}], "physical_options": ""}]}