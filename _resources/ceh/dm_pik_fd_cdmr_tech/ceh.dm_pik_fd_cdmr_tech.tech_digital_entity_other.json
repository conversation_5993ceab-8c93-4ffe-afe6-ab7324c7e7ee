{"resource_desc": "ceh.dm_pik_fd_cdmr_tech.tech_digital_entity_other", "tags": ["dm_pik_fd_cdmr_tech", "tech_digital_entity_other"], "features": {"p_source_resource_names_list": "ceh.rd_rawd.mart_dapp_prod_dm_chmix_tech_digital_entity_detail, ceh.bdm.counterparty, ceh.bdm.application_debit_card, ceh.bdm.counterparty_x_uniq_counterparty, ceh.bdm.employee, ceh.bdm.employee_x_department, ceh.bdm.department", "p_start_date": "(date_trunc('quarter', current_date) - interval '3' month)::date", "p_max_dttm": "'2999-12-31 00:00:00'", "p_max_date": "'2999-12-31'", "p_db_sbx": "sbx_orb_fd", "p_db_bdm": "bdm", "p_db_dm": "dm_pik_fd_cdmr", "p_db_rawd": "rd_rawd_orb", "p_rds_report_dt_date": "'2023-10-01'", "p_department_rename_date": "'2024-10-01'", "p_load_src_employee_text": "'SHCM'", "p_load_department_dko_code_text": "'455%'", "p_load_department_dkp_code_text": "'014%'", "p_load_department_drkb_code_text": "'032%'", "p_load_dep_telemark_code_text": "'032%','014%'", "p_load_dep_serv_code_text": "'455%'", "p_digital_type_application": "'ЗАКАЗ КАРТЫ'", "p_digital_type_not": "'ДОГОВОР'"}, "metrics": {}, "resource_cd": "ceh.dm_pik_fd_cdmr_tech.tech_digital_entity_other", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "tech_digital_entity_other", "schema_name": "dm_pik_fd_cdmr_tech", "filter": "", "columns": [{"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "digital_entity_id", "type": "text", "primary_key": false, "nullable": false}, {"name": "digital_type_cd", "type": "text", "primary_key": false, "nullable": false}, {"name": "level25_product_bp_nm", "type": "text", "primary_key": false, "nullable": false}, {"name": "customer_global_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "digital_entity_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "digital_entity_rk_from_dapp", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "digital_entity_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "agreement_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "employee_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "channel_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "channel_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "channel_subtype_name", "type": "text", "primary_key": false, "nullable": true}, {"name": "channel_priority", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "info_channel_priority", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "final_flg", "type": "text", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "src_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "customer_global_rk", "type": "bigint", "primary_key": false, "nullable": true}], "physical_options": ""}]}