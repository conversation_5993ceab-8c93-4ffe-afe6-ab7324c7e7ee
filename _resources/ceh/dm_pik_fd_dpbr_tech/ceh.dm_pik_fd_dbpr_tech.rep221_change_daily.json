{"resource_desc": "ceh.dm_pik_fd_dpbr_tech.rep221_change_daily", "tags": ["dm_pik_fd_dpbr_tech", "rep221_change_daily"], "features": {"p_start_dt": "'2014-07-01'", "p_amt_upper_bound": "'3000000'", "p_amt_lower_bound": "'400000'", "p_vehicle_upper_bound": "'3000000'", "p_ini_deposit_lower_bound": "'50'", "p_repayed_earlier_flg_auto": "'1'", "p_repayed_earlier_flg_mort": "'1'", "p_percentage_total": 20, "p_percentage_meeting_criteria": 50, "p_rep221_product_name": "'Авто стандарт','Авто экспресс стандарт','Авто.Рефинансирование без замены залога. Кредит группы ВТБ.СФР','Авто.Рефинансирование без замены залога. Кредит стороннего Банка.СФР','Автокредит наличными','Автокредит с залогом. Реструктуризация','Автокредит с остаточным платежом','Автолайт','Беззалоговый кредит.Покупка автомобиля','Потребительский кредит под залог транспортного средства','Рефинансирование автокредита с остаточным платежом','БФКО.РГСБ.АК.Залог.Стандарт','БФКО.РГСБ.АК.Залог.Госпрограмма','БФКО.РГСБ.АК.Залог.РЕФИН','БФКО.РГСБ.АК.Залог.Диф.Ставка','БФКО.РГСБ.АК.Залог.С2С','БФКО.Покупка.АК.БезЗалог.Стандарт','БФКО.РГСБ.АК.БезЗалог.Госпрограмма','БФКО.РГСБ.АК.Залог.ОстатПлатеж','БФКО.Покупка.АК.Залог.Стандарт','БФКО.РГСБ.АК.БезЗалог.Стандарт','Реструктуризация с консолидацией','Авто - PAAL','Авто экспресс (LTW)','Авто экспресс 50','Авто экспресс 50 (LTW)','Авто.Рефинансирование с заменой залога. Кредит группы ВТБ.СФР','Авто.Рефинансирование с заменой залога. Кредит стороннего Банка.СФР','Автокредит Платежные каникулы. Реструктуризация','Автокредитование','Автокредиты Банка Москвы','Автокредиты БМ','Автокредиты классическая схема','Автокредиты моментальная схема','Автокредиты СББ','АР (для АО ост.платежом с залогом). Реструктуризация','Кредит наличными с большими возможностями','Льготные автокредиты Банка Москвы','Русский Стандарт - автокредит','Ц.ПСБ-автокредит(аннуитет)','Ц.ПСБ-автокредит(произв.график)','Юниаструм-автокредит'", "p_rep221_agreement_zalog": "'%-З01', '%-Z01'", "p_start_dt_revision": "'2020-09-30'", "p_start_close_daily_dt": "'2024-07-01'", "p_source_resources_names_list_1": "ceh.idl.sal_product.lecs, ceh.idl.bbridge_product.bb_loan, ceh.idl.bbridge_product.loan, ceh.idl.bbridge_counterparty, ceh.idl.bbridge_product.wayn, ceh.idl.bbridge_product.ccpp_batr, ceh.idl.bbridge_product.ccpp_crca, ceh.idl.sal_product.crca_lims, ceh.idl.sal_product.ipo, ceh.idl.sal_product.alpp, ceh.idl.bbridge_product.dks, ceh.idl.sal_product.grnt, ceh.idl.sal_product.grnt_exp, ceh.idl.bbridge_product.mscl, ceh.idl.bbridge_product.ins_ins, ceh.idl.bbridge_product.alpp, ceh.idl.sal_product, ceh.idl.sal_product.szkf, ceh.idl.sal_product.product_alpp, ceh.idl.sal_product.prof, ceh.idl.sal_product.ins, ceh.idl.bbridge_product.mssa, ceh.idl.sal_product.dm_prof, ceh.idl.bbridge_product.csrb, ceh.idl.bbridge_loan_deal.wfbm, ceh.idl.bbridge_loan_deal.rtll, ceh.idl.bbridge_loan_deal, ceh.idl.bbridge_loan_deal.dm_rtll, ceh.idl.bbridge_loan_deal.prof, ceh.idl.bbridge_loan_deal.dm_prof, ceh.idl.bbridge_loan_deal.wayn_wvtb, ceh.idl.application_x_deal.wayn_wvtb, ceh.idl.application_x_deal, ceh.idl.application_x_deal.dks, ceh.idl.blink_application_x_deal.salp, ceh.idl.blink_application_x_deal.grnt_exp, ceh.idl.blink_application_x_deal.loan, ceh.idl.application_x_deal.prof, ceh.idl.blink_application_x_deal.grnt, ceh.idl.sal_application.crca_lims, ceh.idl.bbridge_application.blink_application_x_counterparty.sfcs, ceh.idl.bbridge_application.smkp, ceh.idl.bbridge_application.ccpp_batr, ceh.idl.bbridge_product.bb_loan, ceh.idl.bbridge_application.ins_ins, ceh.idl.bbridge_application.bbridge_application_status, ceh.idl.bbridge_application.epul, ceh.idl.sal_application.pzp, ceh.idl.bbridge_application.salp, ceh.idl.bbridge_application.bbridge_application_loan, ceh.idl.bbridge_application.onb, ceh.idl.sal_application.dks, ceh.idl.sal_application.cssc, ceh.idl.bbridge_application.mscl, ceh.idl.sal_application.loan, ceh.idl.sal_application.accr, ceh.idl.bbridge_application.loan, ceh.idl.bbridge_application.grnt_exp, ceh.idl.sal_application.epul, ceh.idl.bbridge_application.smbq_fps, ceh.idl.bbridge_application.smbu, ceh.idl.sal_deal, ceh.idl.bbridge_application.tspb, ceh.idl.bbridge_application.blink_application_x_employee, ceh.idl.bbridge_application.bbridge_application_loan_smb.loan, ceh.idl.sal_application.szkf, ceh.idl.sal_application.ipo, ceh.idl.sal_application.refinancing_mssa, ceh.idl.bbridge_application.podr, ceh.idl.bbridge_application.smbq_trade, ceh.idl.bbridge_application.srb, ceh.idl.bbridge_application.ins_app, ceh.idl.sal_application.ccpp_crca, ceh.idl.bbridge_application.accr, ceh.idl.bbridge_application.dlvr, ceh.idl.bbridge_application.dm_wayn_wvtb, ceh.idl.sal_application.grnt, ceh.idl.bbridge_application.lecs, ceh.idl.bbridge_application.dm_ipo, ceh.idl.bbridge_application.crca, ceh.idl.bbridge_application.mssa, ceh.idl.bbridge_application.ipo, ceh.idl.sal_application, ceh.idl.bbridge_application.usmz, ceh.idl.bbridge_application.dks, ceh.idl.bbridge_application.blink_application_x_counterparty, ceh.idl.bbridge_application.blink_application_x_department, ceh.idl.bbridge_application.dm_lssemd, ceh.idl.bbridge_application.sacc, ceh.idl.bbridge_application.sfcs, ceh.idl.bbridge_application.application_status_szp, ceh.idl.sal_application.alpp, ceh.idl.sal_application.grnt_exp, ceh.idl.bbridge_application.lssemd, ceh.idl.sal_application.salp, ceh.idl.bbridge_application.dm_alpp, ", "p_source_resources_names_list_2": "ceh.idl.sal_application.crsl, ceh.idl.bbridge_application.grnt, ceh.idl.sal_application.onb, ceh.idl.bbridge_application.crca_lims, ceh.idl.bbridge_application.product_alpp, ceh.idl.bbridge_application.blink_application_x_deal, ceh.idl.bbridge_application.blink_application_product.loan, ceh.idl.bbridge_application.cssc, ceh.idl.sal_counterparty.srb, ceh.idl.sal_application.product_alpp, ceh.idl.bbridge_application.refinancing_mssa, ceh.idl.bbridge_application.smbq, ceh.idl.sal_application.ins, ceh.idl.sal_application.srb, ceh.idl.bbridge_application.ccpp_crca, ceh.idl.sal_application.mvs, ceh.idl.sal_application.smbq, ceh.idl.bbridge_application.smbq_internet, ceh.idl.bbridge_application.szkf, ceh.idl.bbridge_application.smbq_vtb, ceh.idl.bbridge_application.alpp, ceh.idl.sal_application.sacc, ceh.idl.bbridge_application.tbcv, ceh.idl.sal_agreement.prof, ceh.idl.bbridge_agreement.1496_dko, ceh.idl.bbridge_agreement.rtll, ceh.idl.sal_agreement.wfbm, ceh.idl.blink_agreement_x_agreement.rtll, ceh.idl.bbridge_agreement, ceh.idl.sal_agreement.cftm, ceh.idl.blink_agreement_x_agreement, ceh.idl.bbridge_agreement.project_deal_szp, ceh.idl.bbridge_agreement.smbu, ceh.idl.bbridge_agreement.wfbm, ceh.idl.bbridge_agreement.dm_wayn, ceh.idl.bbridge_agreement.dm_wayn_wvtb, ceh.idl.sal_agreement.dm_prof, ceh.idl.bbridge_agreement.zfnt, ceh.idl.bbridge_agreement.rtls, ceh.idl.bbridge_agreement.wayn, ceh.idl.sal_agreement.kih_ti, ceh.idl.blink_agreement_x_agreement.zfnt, ceh.idl.sal_agreement.zfnt, ceh.idl.blink_agreement_x_agreement.prof, ceh.idl.bbridge_agreement.deal_cession, ceh.idl.blink_agreement_x_agreement.cftm, ceh.idl.bbridge_agreement.dm_prof, ceh.idl.bbridge_agreement.apcm, ceh.idl.sal_agreement, ceh.idl.sal_agreement.prof_wayn, ceh.idl.bbridge_agreement.kih_ti, ceh.idl.bbridge_agreement.ztxa, ceh.idl.bbridge_agreement.wayn_wvtb, ceh.idl.bbridge_agreement.escrow_deal, ceh.idl.bbridge_agreement.ins_ins, ceh.idl.bbridge_agreement.main_agg_ins, ceh.idl.bbridge_agreement.dm_rtll, ceh.idl.sal_agreement.rtll, ceh.idl.bbridge_agreement.deal_accreditiv_v2, ceh.idl.sal_counterparty, ceh.idl.bbridge_agreement.szp, ceh.idl.bbridge_agreement.prof, ceh.idl.bbridge_agreement.cftm, ceh.idl.bbridge_agreement.dm_cftm, ceh.idl.bbridge_agreement.wayn_wvtb_dm, ceh.idl.bbridge_agreement.bbridge_insurance, ceh.idl.bbridge_insurance.bbridge_insurance_operation, ceh.idl.bbridge_schedule_insurance_payment.bbridge_insurance.rtll, ceh.idl.bbridge_insurance.main_ins_ins, ceh.idl.bbridge_insurance.prof, ceh.idl.sal_insurance, ceh.idl.bbridge_insurance.rtll, ceh.idl.bbridge_insurance.ins, ceh.idl.bbridge_insurance.blink_insurance_x_counterparty, ceh.idl.sal_insurance.ins_op, ceh.idl.bbridge_insurance.ins_op, ceh.idl.hub_schedule_insurance_payment, ceh.idl.bbridge_schedule_insurance_payment.rtll, ceh.idl.sal_schedule_insurance_payment, ceh.idl.bbridge_property_vehicle.rtll, ceh.idl.bbridge_property_vehicle.alpp, ceh.idl.bbridge_property_vehicle.ins, ceh.idl.blink_collateral_object_x_property.rtll, ceh.idl.bbridge_collateral_object_vehicle.rtll, ceh.idl.blink_property_x_counterparty.ipo, ceh.idl.blink_property_x_counterparty.rtll, ceh.idl.blink_property_x_counterparty.ins, ceh.idl.blink_property_x_counterparty.alpp, ceh.idl.sal_collateral.rtll, ceh.idl.bbridge_collateral.dm_cftm, ceh.idl.sal_collateral, ceh.idl.sal_collateral.kih_ti, ceh.idl.bbridge_collateral.kih_ti, ceh.idl.bbridge_collateral.cftm, ceh.idl.bbridge_collateral.rtll, ceh.idl.bbridge_collateral, ceh.idl.sal_counterparty.prof_wayn, ceh.idl.bbridge_counterparty.deal_x_cntrpt, ", "p_source_resources_names_list_3": "ceh.idl.sal_counterparty.ins_op, ceh.idl.bbridge_counterparty_individual.mssa, ceh.idl.bbridge_counterparty.crer, ceh.idl.bbridge_counterparty.rtls, ceh.idl.bbridge_counterparty_individual.dm_mdmp, ceh.idl.sal_counterparty.prof, ceh.idl.sal_counterparty.crca_lims, ceh.idl.bbridge_counterparty.szp_2, ceh.idl.sal_counterparty.wayn_wvtb, ceh.idl.bbridge_counterparty_contact.mdmp, ceh.idl.hub_counterparty_contact.ipo, ceh.idl.bbridge_counterparty.blink_application_x_counterparty.sfcs, ceh.idl.bbridge_counterparty.smbu, ceh.idl.bbridge_counterparty.cntr_exp_szp, ceh.idl.bbridge_counterparty_individual.mscl, ceh.idl.sal_counterparty.mole, ceh.idl.bbridge_counterparty_document, ceh.idl.bbridge_counterparty.cntp_contact_alpp, ceh.idl.blink_counterparty_x_address.scol, ceh.idl.hub_counterparty_document, ceh.idl.bbridge_counterparty.dm_mdmp_chain, ceh.idl.bbridge_counterparty.scol, ceh.idl.bbridge_counterparty, ceh.idl.bbridge_counterparty.bbr_agreement, ceh.idl.bbridge_counterparty.tbcv, ceh.idl.sal_counterparty_contact, ceh.idl.bbridge_counterparty.pmnt, ceh.idl.bbridge_counterparty_individual, ceh.idl.bbridge_counterparty.bcxuc_crer, ceh.idl.bbridge_counterparty.crca_lims, ceh.idl.sal_counterparty.csep, ceh.idl.bbridge_counterparty.bki_subject_trig_cre_crer, ceh.idl.bbridge_counterparty_individual.ccpp_batr, ceh.idl.bbridge_counterparty_document.ins, ceh.idl.sal_counterparty.onb, ceh.idl.sal_counterparty.grnt, ceh.idl.bbridge_counterparty.dm_wayn_wvtb, ceh.idl.sal_counterparty_contact.cntp_contact_alpp, ceh.idl.sal_counterparty.szkf, ceh.idl.bbridge_counterparty.dm_alpp, ceh.idl.bbridge_counterparty_individual.ccpp_crca, ceh.idl.sal_counterparty.dm_prof, ceh.idl.sal_counterparty.rtll_dm, ceh.idl.bbridge_counterparty.csrf, ceh.idl.bbridge_counterparty_individual.scol, ceh.idl.bbridge_counterparty.lssemd, ceh.idl.bbridge_counterparty.mssa, ceh.idl.blink_counterparty_x_address.ipo, ceh.idl.bbridge_counterparty_contact.alpp, ceh.idl.bbridge_counterparty.boop, ceh.idl.bbridge_counterparty.cntrpt_individual, ceh.idl.bbridge_counterparty.bki_counterparty_score_cre_crer, ceh.idl.bbridge_counterparty_contact.srb, ceh.idl.bbridge_counterparty.counterparty_experience_lssemd, ceh.idl.hub_counterparty_document.ipo, ceh.idl.bbridge_counterparty.grnt, ceh.idl.sal_counterparty.cntp_contact_alpp, ceh.idl.bbridge_counterparty_individual.rtll, ceh.idl.bbridge_counterparty_individual.szp, ceh.idl.bbridge_counterparty.bbridge_application, ceh.idl.bbridge_counterparty_individual.cftb, ceh.idl.hub_counterparty_document.counterparty_document_lssemd, ceh.idl.bbridge_counterparty.ins_risk, ceh.idl.sal_counterparty.bki_subject_trig_cre_crer, ceh.idl.bbridge_counterparty.smbq, ceh.idl.sal_counterparty.grnt_exp, ceh.idl.bbridge_counterparty.smkp, ceh.idl.sal_counterparty.wayn, ceh.idl.bbridge_counterparty_document.dks, ceh.idl.sal_counterparty.alpp, ceh.idl.bbridge_counterparty_document.ipo, ceh.idl.bbridge_counterparty.parb, ceh.idl.bbridge_counterparty.dm_usmz, ceh.idl.bbridge_counterparty.cntrpty_corp_szp, ceh.idl.sal_counterparty.wfbm, ceh.idl.hub_counterparty_contact.podr, ceh.idl.blink_counterparty_x_address.lssemd, ceh.idl.bbridge_counterparty.wayn_wvtb, ceh.idl.bbridge_counterparty.tspb, ceh.idl.bbridge_counterparty.mscl, ceh.idl.sal_counterparty.ascm, ceh.idl.sal_counterparty.cftm, ceh.idl.sal_counterparty.dks, ceh.idl.bbridge_counterparty.tsn_scol, ceh.idl.bbridge_counterparty.dm_cftm, ceh.idl.bbridge_counterparty_contact, ceh.idl.sal_counterparty.accr, ceh.idl.bbridge_counterparty.ins, ceh.idl.bbridge_counterparty.agreement_rtll, ceh.idl.bbridge_counterparty.alpp, ceh.idl.bbridge_counterparty.blink_insurance_x_counterparty, ceh.idl.hub_counterparty_contact, ceh.idl.bbridge_counterparty.ztxa, ceh.idl.bbridge_counterparty_index.csoc, ceh.idl.sal_counterparty.mdmp, ceh.idl.bbridge_counterparty_document.mdmp, ceh.idl.hub_counterparty_contact.stop, ceh.idl.bbridge_counterparty.dm_ppop, ceh.idl.bbridge_counterparty.rtll_dm, ceh.idl.bbridge_counterparty.apcm, ceh.idl.bbridge_counterparty.accr, ", "p_source_resources_names_list_4": "ceh.idl.bbridge_counterparty.csep, ceh.idl.bbridge_counterparty.ins_agg, ceh.idl.bbridge_counterparty.dm_rtll, ceh.idl.bbridge_counterparty.szkf, ceh.idl.bbridge_counterparty_public_official.csoc, ceh.idl.bbridge_counterparty.cftm, ceh.idl.bbridge_counterparty.cprt_x_cprt_szp, ceh.idl.bbridge_counterparty_individual.smkp, ceh.idl.bbridge_counterparty.dm_prof, ceh.idl.bbridge_counterparty.ins_cons_dm, ceh.idl.bbridge_counterparty.ins_app_dm, ceh.idl.bbridge_counterparty_individual.ipo, ceh.idl.bbridge_counterparty.nsm, ceh.idl.hub_counterparty_contact.alpp, ceh.idl.sal_counterparty.tbcv, ceh.idl.bbridge_counterparty.srb, ceh.idl.blink_counterparty_x_address.dks, ceh.idl.bbridge_counterparty_contact.stop, ceh.idl.sal_counterparty.epul, ceh.idl.bbridge_counterparty.bmc_crer, ceh.idl.bbridge_counterparty.counterparty_document_lssemd, ceh.idl.hub_counterparty_document.alpp, ceh.idl.bbridge_counterparty.bki_cprt_x_sngl_cre, ceh.idl.bbridge_counterparty.wfbm, ceh.idl.sal_counterparty_document.ascm, ceh.idl.bbridge_counterparty_individual.alpp, ceh.idl.bbridge_counterparty.dks, ceh.idl.sal_counterparty_contact.alpp, ceh.idl.bbridge_counterparty.onb, ceh.idl.bbridge_counterparty.ascm, ceh.idl.bbridge_counterparty.dm_ipo, ceh.idl.bbridge_counterparty_individual.csoc, ceh.idl.blink_counterparty_x_address.csoc, ceh.idl.bbridge_counterparty_contact.ins, ceh.idl.sal_counterparty.smbq, ceh.idl.bbridge_counterparty.podr, ceh.idl.sal_counterparty.counterparty_document_lssemd, ceh.idl.hub_counterparty_contact.cntp_contact_alpp, ceh.idl.bbridge_counterparty.1496_dko, ceh.idl.bbridge_counterparty.jcms, ceh.idl.hub_counterparty_document.mdmp, ceh.idl.bbridge_counterparty.cftb, ceh.idl.bbridge_counterparty_document.smkp, ceh.idl.bbridge_counterparty.mdmp, ceh.idl.bbridge_counterparty_individual.wayn_wvtb, ceh.idl.bbridge_counterparty_contact.podr, ceh.idl.sal_counterparty.ins_risk, ceh.idl.bbridge_counterparty.lecs, ceh.idl.blink_counterparty_x_address.mssa, ceh.idl.bbridge_counterparty.grnt_exp, ceh.idl.bbridge_counterparty_contact.ipo, ceh.idl.sal_counterparty.rtls, ceh.idl.bbridge_counterparty.project_deal_szp, ceh.idl.bbridge_counterparty.trns, ceh.idl.bbridge_counterparty_document.alpp, ceh.idl.bbridge_counterparty.executory_document_scol, ceh.idl.bbridge_counterparty_contact.dks, ceh.idl.bbridge_counterparty_contact.cntp_contact_alpp, ceh.idl.blink_counterparty_x_address.zfnt, ceh.idl.sal_counterparty.ipo, ceh.idl.bbridge_counterparty.stop, ceh.idl.sal_counterparty.cntr_exp_szp, ceh.idl.bbridge_counterparty.tsc_crer, ceh.idl.hub_counterparty_contact.mdmp, ceh.idl.bbridge_counterparty.ipo, ceh.idl.bbridge_counterparty.rtll, ceh.idl.bbridge_counterparty_individual.wfbm, ceh.idl.bbridge_counterparty.mole, ceh.idl.blink_counterparty_x_address.smkp, ceh.idl.bbridge_counterparty_document.csoc, ceh.idl.sal_counterparty.apcm, ceh.idl.bbridge_counterparty.wayn_wvtb_dm, ceh.idl.sal_counterparty.bki_counterparty_score_cre_crer, ceh.idl.bbridge_counterparty.ccpp_crca, ceh.idl.blink_counterparty_x_address.mscl, ceh.idl.bbridge_counterparty.bbridge_counterparty_document_mdmp, ceh.idl.bbridge_counterparty.szp, ceh.idl.bbridge_counterparty_individual.prof, ceh.idl.blink_counterparty_x_address.mdmp, ceh.idl.bbridge_counterparty_individual.srb, ceh.idl.sal_counterparty.salp, ceh.idl.bbridge_counterparty.dlvr, ceh.idl.sal_counterparty.srb, ceh.idl.bbridge_counterparty.dm_rtls, ceh.idl.bbridge_counterparty_contact.mssa, ceh.idl.bbridge_counterparty_individual.rtls, ceh.idl.sal_counterparty.rtll, ceh.idl.bbridge_counterparty.salp, ceh.idl.bbridge_counterparty.ins_ins, ceh.idl.bbridge_counterparty.cssc, ceh.idl.blink_counterparty_x_address.csep, ceh.idl.sal_counterparty.zfnt, ceh.idl.bbridge_counterparty_individual.podr, ceh.idl.bbridge_counterparty.ccpp_batr, ceh.idl.bbridge_counterparty.dm_wayn, ceh.idl.bbridge_counterparty.cprt_x_empl_szp, ceh.idl.sal_counterparty, ceh.idl.bbridge_counterparty.bbridge_application_loan, ceh.idl.bbridge_counterparty.wayn, ", "p_source_resources_names_list_5": "ceh.rd_rawd.mart_mrod_mref_bfko_notification_pledge, ceh.idl.sal_counterparty.counterparty_experience_lssemd, ceh.idl.bbridge_counterparty_individual.dks, ceh.idl.bbridge_counterparty.prof, ceh.idl.bbridge_counterparty.csoc, ceh.idl.sal_counterparty.csoc, ceh.idl.bbridge_counterparty.ins_op, ceh.idl.bbridge_counterparty.zfnt, ceh.idl.bbridge_counterparty_contact.csoc, ceh.idl.bbridge_counterparty.dm_lssemd, ceh.idl.bbridge_counterparty.bbridge_insurance, ceh.idl.bbridge_counterparty.dm_mdmp, ceh.idl.bbridge_counterparty.loan, ceh.idl.bbridge_counterparty_individual.mdmp, ceh.idl.bbridge_counterparty_document.counterparty_document_lssemd, ceh.idl.bbridge_counterparty.blink_application_x_counterparty, ceh.idl.bbridge_counterparty.epul, ceh.idl.blink_counterparty_x_address.alpp, ceh.idl.sal_counterparty_contact.srb, ceh.idl.bbridge_counterparty_document.mssa, ceh.idl.bbridge_counterparty.dsr, ceh.idl.bbridge_address.mscl, ceh.idl.bbridge_address.ipo, ceh.idl.bbridge_address.counterparty_experience_lssemd, ceh.idl.bbridge_address.rtll, ceh.idl.bbridge_address.alpp, ceh.idl.sal_address.dks, ceh.idl.bbridge_address.zfnt, ceh.idl.bbridge_address.wayn, ceh.idl.sal_address.zfnt, ceh.idl.sal_address.csep, ceh.idl.sal_address.ipo, ceh.idl.sal_address, ceh.idl.bbridge_address, ceh.idl.bbridge_address.csoc, ceh.idl.bbridge_counterparty.csep, ceh.idl.bbridge_address.lssemd, ceh.idl.bbridge_address.smkp, ceh.idl.bbridge_address.csep, ceh.idl.sal_address.alpp, ceh.idl.sal_address.counterparty_experience_lssemd, ceh.idl.bbridge_address.tbcv, ceh.idl.bbridge_address.mssa, ceh.idl.bbridge_address.scol, ceh.idl.bbridge_address.dks, ceh.idl.bbridge_address.ins, ceh.idl.sal_address.rtll, ceh.idl.bbridge_address.blink_counterparty_x_address_mdmp, ceh.idl.bbridge_address.dm_wayn, ceh.rd_rawd.mart_mrod_get_raw_data, ceh.bdm.agreement_x_agreement, ceh.bdm.agreement, ceh.rdv_dict.ref_country, ceh.rdv_dict.ref_region"}, "metrics": {}, "resource_cd": "ceh.dm_pik_fd_dpbr_tech.rep221_change_daily", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "rep221_change_daily", "schema_name": "dm_pik_fd_dpbr_tech", "filter": "", "columns": [{"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "zap_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "bnk_migr_bfko_sign_notkom", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "customer_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "bnk_migr_bfko_dog_zalog", "type": "text", "primary_key": false, "nullable": true}, {"name": "bfko_auto_migr", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "doc_pledge", "type": "text", "primary_key": false, "nullable": true}, {"name": "doc_pledge_flg", "type": "text", "primary_key": false, "nullable": true}, {"name": "full_address", "type": "text", "primary_key": false, "nullable": true}, {"name": "geography_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "geography_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "flat", "type": "text", "primary_key": false, "nullable": true}, {"name": "construction", "type": "text", "primary_key": false, "nullable": true}, {"name": "building", "type": "text", "primary_key": false, "nullable": true}, {"name": "street", "type": "text", "primary_key": false, "nullable": true}, {"name": "town", "type": "text", "primary_key": false, "nullable": true}, {"name": "city", "type": "text", "primary_key": false, "nullable": true}, {"name": "district", "type": "text", "primary_key": false, "nullable": true}, {"name": "region_tax_insp_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "region_tax_insp_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "overlap_address_flg", "type": "text", "primary_key": false, "nullable": true}, {"name": "address_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "email_address", "type": "text", "primary_key": false, "nullable": true}, {"name": "doc_full_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "doc_type_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "doc_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "customer_full_eng_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "customer_full_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "customer_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "car_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "car_pin_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "car_chassis_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "car_body_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "car_vin", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_other_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_other_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "form", "type": "text", "primary_key": false, "nullable": true}, {"name": "uid", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_group_flg", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_agreement_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "close_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "pledge_open_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "birth_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "report_dt", "type": "date", "primary_key": true, "nullable": true}, {"name": "processed_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "src_processed_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "start_collateral_dt", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "agreement_rk", "type": "numeric", "primary_key": false, "nullable": true}], "physical_options": ""}]}