{"resource_desc": "ceh.dm_pik_fd_dpbr_tech.rep20144", "tags": ["dm_pik_fd_dpbr_tech", "rep20144"], "features": {"p_max_date_khd": "'5999-12-31'", "p_delinquency_method_cd": "'FIFO'", "p_delinquency_type_cd": "'PRI'", "p_collat_agr_type": "'LOAN_COLLATERAL'", "p_check_doubles_flg": "1", "p_source_resource_names_list": "ceh.dm_cdm_tech.mart_recoding_agreement, ceh.dm_common_tech.r_cd_loan_agreement, ceh.dm_common_tech.r_fct_delinquency, ceh.dm_common_tech.r_fct_loan_balance, ceh.idl.blink_agreement_x_agreement, ceh.idl.bbridge_agreement, ceh.idl.bbridge_collateral, ceh.idl.bbridge_loan_deal, ceh.idl.blink_collateral_object_x_collateral, ceh.dm_pik_fd_dpbr_tech.rep20144_core"}, "metrics": {}, "resource_cd": "ceh.dm_pik_fd_dpbr_tech.rep20144", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "rep20144", "schema_name": "dm_pik_fd_dpbr_tech", "filter": "", "columns": [{"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "loan_agreement_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "full_application_rk", "type": "bigint", "primary_key": false, "nullable": true}, {"name": "mini_application_rk", "type": "bigint", "primary_key": true, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "delinquency_days_cnt", "type": "integer", "primary_key": false, "nullable": true}, {"name": "issue_mini_days_cnt", "type": "integer", "primary_key": false, "nullable": true}, {"name": "full_mini_days_cnt", "type": "integer", "primary_key": false, "nullable": true}, {"name": "loan_contract_period", "type": "integer", "primary_key": false, "nullable": true}, {"name": "mini_application_cnt", "type": "integer", "primary_key": true, "nullable": false}, {"name": "pledge_type_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_type_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "product_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "pledge_builder_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "negative_reason_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "negative_reason_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "limit_credit_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "loan_contract_id", "type": "text", "primary_key": false, "nullable": true}, {"name": "loan_agreement_source_type", "type": "text", "primary_key": false, "nullable": true}, {"name": "full_application_decision_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "full_application_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "full_fixed_rate_program_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "mini_application_decision_desc", "type": "text", "primary_key": false, "nullable": true}, {"name": "mini_application_num", "type": "text", "primary_key": false, "nullable": true}, {"name": "program_change_flg", "type": "text", "primary_key": false, "nullable": true}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "pledge_registration_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "loan_close_fact_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "loan_close_plan_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "loan_balance_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "loan_issue_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "loan_open_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "full_application_decision_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "full_application_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "mini_application_decision_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "mini_application_dt", "type": "date", "primary_key": false, "nullable": true}, {"name": "report_dt", "type": "date", "primary_key": true, "nullable": false}, {"name": "pledge_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "loan_interest_rate", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "loan_balance_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "loan_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "full_application_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "mini_application_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "compensation_amt", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "delinquency_amt", "type": "numeric", "primary_key": false, "nullable": true}], "physical_options": ""}]}