{"resource_desc": "Table dm_rb_crm_bcrm_tech.dfct_mcc_x_mcg", "tags": [], "features": {"domain": "dm_rb_crm_bcrm_tech", "source_system": ""}, "metrics": {}, "resource_cd": "ceh.dm_rb_crm_bcrm_tech.dfct_mcc_x_mcg", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "dfct_mcc_x_mcg", "schema_name": "dm_rb_crm_bcrm_tech", "filter": "", "columns": [{"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "khd_flg", "type": "boolean", "primary_key": false, "nullable": true}, {"name": "processed_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "mcg_id", "type": "integer", "primary_key": true, "nullable": true}, {"name": "mcc_cd", "type": "text", "primary_key": true, "nullable": true}], "physical_options": ""}]}