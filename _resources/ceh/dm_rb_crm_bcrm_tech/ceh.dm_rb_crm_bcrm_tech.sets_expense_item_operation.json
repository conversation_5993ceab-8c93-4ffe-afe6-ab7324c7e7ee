{"resource_desc": "ceh.dm_rb_crm_bcrm_tech.sets_expense_item_operation", "tags": ["string"], "features": null, "metrics": {}, "resource_cd": "ceh.dm_rb_crm_bcrm_tech.sets_expense_item_operation", "is_readonly": false, "is_deleted": false, "datasets": [{"name": "sets_expense_item_operation", "schema_name": "dm_rb_crm_bcrm_tech", "filter": "", "columns": [{"name": "deleted_flg", "type": "boolean", "primary_key": false, "nullable": false}, {"name": "hash_diff", "type": "text", "primary_key": false, "nullable": false}, {"name": "version_id", "type": "bigint", "primary_key": false, "nullable": false}, {"name": "effective_to_dttm", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "effective_from_dttm", "type": "timestamp", "primary_key": true, "nullable": true}, {"name": "operation_template_id", "type": "integer", "primary_key": false, "nullable": true}, {"name": "operation_type_cn", "type": "integer", "primary_key": false, "nullable": true}, {"name": "operation_template_cn", "type": "integer", "primary_key": false, "nullable": true}, {"name": "customer_expense_item_cd", "type": "text", "primary_key": false, "nullable": true}, {"name": "operation_subtype_lvl2_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "operation_subtype_lvl1_nm", "type": "text", "primary_key": false, "nullable": true}, {"name": "expense_item_operation_id", "type": "integer", "primary_key": true, "nullable": true}, {"name": "cm_dirty_ind", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "hub_state_ind", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "interaction_id", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "dirty_ind", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "last_rowid_system", "type": "text", "primary_key": false, "nullable": true}, {"name": "deleted_date", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "deleted_by", "type": "text", "primary_key": false, "nullable": true}, {"name": "deleted_ind", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "consolidation_ind", "type": "numeric", "primary_key": false, "nullable": true}, {"name": "last_update_date", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "updated_by", "type": "text", "primary_key": false, "nullable": true}, {"name": "create_date", "type": "timestamp", "primary_key": false, "nullable": true}, {"name": "creator", "type": "text", "primary_key": false, "nullable": true}, {"name": "rowid_object", "type": "text", "primary_key": false, "nullable": true}, {"name": "src_cd", "type": "text", "primary_key": false, "nullable": true}], "physical_options": ""}]}