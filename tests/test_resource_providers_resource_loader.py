import os
from datetime import datetime

from metaloader_rest_api.module_repository import ModuleRepository
from metaloader_rest_api.resource_provider_client import ResourceProviderClient
from metaloader_rest_api.resource_providers_resource_loader import (
    ResourceProvidersResourceLoader,
)
from metaloader_rest_api.resource_repository import (
    ResourceRepository,
    ResourceStageBatchRepository,
)
from metaloader_rest_api.version_repository import VersionRepository
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session


def test_load(
    uni_provider_url,
    ceh_provider_url,
    db_session,
    session_resource,
    table_id,
    setup_metamodel,
):
    ResourceProvidersResourceLoader(
        uni_client=ResourceProviderClient(base_url=uni_provider_url),
        ceh_client=ResourceProviderClient(base_url=ceh_provider_url),
        version_repository=VersionRepository(db_session),
        module_repository=ModuleRepository(db_session),
        session_resource=session_resource,
        resource_stage_repository=ResourceStageBatchRepository(
            session=db_session,
            table_id=table_id,
        ),
        resource_repository=ResourceRepository(db_session),
    ).load(
        module=setup_metamodel,
        version="1.2.3",
        effective_date=datetime(2024, 6, 15),
        limit=100,
    )

    resources = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_resource
         """)
    ).fetchall()

    assert len(resources) == 4


@fixture
def module_repository(
    db_session: Session,
) -> ModuleRepository:
    return


@fixture
def table_id() -> str:
    return 32 * "0"


@fixture
def setup_metamodel(db_session) -> str:
    db_session.execute(
        text("""
        INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                          , code_delivery_cd
                                          , code_delivery_name
                                          , is_ddl_flg
                                          , is_flow_flg
                                          , is_config_flg
                                          , deleted_flg)
        VALUES (1
              , 'core'
              , 'core'
              , FALSE
              , TRUE
              , FALSE
              , FALSE)
    """)
    )
    db_session.commit()

    db_session.execute(text(
        """
        TRUNCATE dict.dict_code_delivery
               , metamodel.bridge_version
               , metamodel.bridge_resource
               , metamodel.link_resource_source
               , metamodel.link_resource_table
               , metamodel.bridge_source
               , metamodel.bridge_table
        """
    ))

    db_session.commit()

    return "core"


@fixture(scope="module")
def uni_provider_url() -> str:
    return os.getenv("UNI_PROVIDER_URL", "http://localhost:8002/api/1.0")


@fixture(scope="module")
def ceh_provider_url() -> str:
    return os.getenv("CEH_PROVIDER_URL", "http://localhost:8001/api/1.0")
