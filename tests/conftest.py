import json
import os
from dataclasses import dataclass
from datetime import datetime, timedelta
from functools import partial
from typing import (
    Any,
    Callable,
    Collection,
    Literal,
    Mapping,
    Optional,
    Sequence,
    Union,
)
from urllib.parse import urljoin

import docker
from celery import Celery
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from docker.models.containers import Container
from jose import jwt
from metaloader_rest_api import ceh_db
from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.helpers import JsonDict
from metaloader_rest_api.resource_schemas._ceh_provider import (
    Resource as CehResourceModel,
)
from metaloader_rest_api.resource_schemas._uni_provider import (
    ResourceUniRequest as UniResourceModel,
)
from metaloader_rest_api.settings import Settings, read_settings
from polyfactory.factories.pydantic_factory import ModelFactory
from psycopg2 import connect
from pytest import FixtureRequest, fixture
from redis import StrictRedis
from sqlalchemy import Engine, Row, create_engine, text
from sqlalchemy.orm import Session, scoped_session, sessionmaker
from starlette.testclient import TestClient


class CehResourceFactory(ModelFactory[CehResourceModel]):
    __model__ = CehResourceModel


class UniResourceFactory(ModelFactory[UniResourceModel]):
    __model__ = UniResourceModel


@dataclass
class SessionShortcutsWrapper:
    session: Session

    def execute_fetchall(self, query: str) -> Sequence[Row]:
        return self.session.execute(text(query)).fetchall()

    def __getattr__(self, name):
        return getattr(self.session, name)


@fixture(scope="session")
def settings() -> Settings:
    return read_settings()


@fixture(scope="session")
def db_engine(settings) -> Engine:  # type: ignore
    engine = create_engine(
        settings.database_url.unicode_string(),
        json_serializer=partial(json.dumps, default=str),
        echo=False,
    )
    yield engine
    engine.dispose()


@fixture(scope="session")
def db_session_factory(db_engine: Engine):
    session_maker = sessionmaker(bind=db_engine, autoflush=False)
    return scoped_session(session_maker)


@fixture(scope="function")
def db_session(db_session_factory) -> SessionShortcutsWrapper:  # type: ignore
    session = db_session_factory()
    yield SessionShortcutsWrapper(session)
    session.rollback()
    session.close()


@fixture(scope="function")
def enable_log_statement_all(db_engine):
    conn = db_engine.raw_connection()
    try:
        raw_conn: psycopg2.extensions.connection = conn.connection  # type: ignore[attr-defined]
        raw_conn.autocommit = True
        with raw_conn.cursor() as cursor:
            cursor.execute("ALTER SYSTEM SET log_statement = 'all';")
            cursor.execute("SELECT pg_reload_conf();")
    finally:
        conn.close()

    yield

    conn = db_engine.raw_connection()
    try:
        raw_conn: psycopg2.extensions.connection = conn.connection  # type: ignore[attr-defined]
        raw_conn.autocommit = True
        with raw_conn.cursor() as cursor:
            cursor.execute("ALTER SYSTEM RESET log_statement;")
            cursor.execute("SELECT pg_reload_conf();")
    finally:
        conn.close()


@fixture(scope="function")
def session_resource(
    db_session: Session,
) -> SessionResource:
    return SessionResource(db_session)


@fixture(scope="session")
def ceh_dsn() -> Mapping[str, Any]:
    return ceh_db.get_dsn()


@fixture(scope="session")
def ceh_connection(ceh_dsn):
    with connect(**ceh_dsn) as connection:
        yield connection


@fixture(scope="function")
def ceh_cursor(ceh_connection):
    with ceh_connection.cursor() as cursor:
        yield cursor


@fixture(scope="session")
def redis_client(settings) -> StrictRedis:  # type: ignore
    url = settings.celery_broker_url
    db = int(url.path[1:]) if url.path else "0"
    with StrictRedis(
        host=url.host,
        port=url.port,
        db=db,
    ) as client:
        yield client


@fixture(scope="session")
def celery_client(settings) -> Celery:
    with Celery("tasks", broker=settings.celery_broker_url.unicode_string()) as client:
        yield client


@fixture(scope="session")
def send_celery_task__process_tx(celery_client):
    def func(
        tx_id: str,
        dry_run: bool,
        integration_mode: Literal["incremental", "full"] = "incremental",
    ):
        task_name = (
            "dry_run_release_sources_integration_etl"
            if dry_run
            else "run_release_sources_integration_etl"
        )
        celery_client.send_task(
            name=task_name,
            kwargs=dict(
                tx_uid=tx_id,
                integration_mode=integration_mode,
            ),
            queue="sequential",
        )

    return func


@fixture(scope="module")
def app_url() -> str:
    return os.getenv("APP_URL")


@fixture(scope="module")
def app_url_factory(app_url: str) -> Callable[..., str]:
    app_url = urljoin(app_url, "v1/")

    def resolve_url(*args) -> str:
        return urljoin(app_url, "/".join(str(arg) for arg in args))

    return resolve_url


@fixture(scope="session")
def celery_container() -> Container:
    client = docker.from_env()
    container_name = os.environ["CELERY_CONTAINER_NAME"]
    container = client.containers.get(container_name)
    if not container.status == "running":
        raise RuntimeError("Celery container is not running")
    yield container


@fixture(scope="module")
def af_url(settings) -> str:
    return os.getenv("AF_URL")


@fixture(scope="module")
def docker_af_url(settings) -> str:
    return os.getenv("DOCKER_AF_URL")


@fixture(scope="module")
def af_creds(settings) -> Mapping[str, Optional[str]]:
    return {
        "username": settings.airflow_username,
        "password": settings.airflow_password,
    }


@fixture(scope="session")
def test_client(request: FixtureRequest):
    private_key, public_key = generate_rsa_key_pair()
    auth_token = generate_auth_token(private_key)

    os.environ["RS256_PUBLIC_KEY"] = public_key

    from metaloader_rest_api.app import app

    client = TestClient(app)
    client.headers.update(
        {
            "Authorization": f"Bearer {auth_token}",
        }
    )

    return client


@fixture(scope="module")
def local_file_server_url() -> str:
    return os.getenv("LOCAL_FILE_SERVER_URL")


@fixture(scope="module")
def docker_file_server_url() -> str:
    return os.getenv("DOCKER_FILE_SERVER_URL")


@fixture
def ceh_resource_json() -> JsonDict:
    resource = CehResourceFactory.build()
    return resource.model_dump(mode="json")


@fixture
def uni_resource_json() -> JsonDict:
    resource = UniResourceFactory.build()
    return resource.model_dump(mode="json")


@fixture(scope="module")
def yaml_directory_path():
    return os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        "tests",
        "docker_",
        "nginx-file-server",
        "files",
        "etl-scale",
        "core",
        "general_ledger",
    )


@fixture(scope="module")
def yaml_rdv_idl_path(yaml_directory_path):
    return os.path.join(
        yaml_directory_path,
        "rdv_idl",
        "flow_dumps",
    )


def generate_rsa_key_pair() -> tuple[str, str]:
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
        backend=default_backend(),
    )
    public_key = private_key.public_key()

    private_key_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.TraditionalOpenSSL,
        encryption_algorithm=serialization.NoEncryption(),
    )
    public_key_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo,
    )

    return private_key_pem.decode("utf-8"), public_key_pem.decode("utf-8")


def generate_auth_token(
    private_key: str,
    roles: Collection[str] = ("read_role", "modify_role"),
    expires: Union[int, datetime] = 30 * 60,
) -> str:
    payload = {
        "name": "john_doe",
        "realm_access": {"roles": roles},
        "exp": datetime.utcnow() + timedelta(seconds=expires)
        if isinstance(expires, int)
        else expires,
    }
    dummy_token = jwt.encode(
        claims=payload,
        key=private_key,
        algorithm="RS256",
    )

    return dummy_token
