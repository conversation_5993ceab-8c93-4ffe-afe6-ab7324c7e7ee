import time

import pytest
from dwh_services_utils.clients.tx_manager import TxManagerClient
from dwh_services_utils.clients.ceh_provider import CehProviderClient
from dwh_services_utils.clients.sequence_generator import SequenceGenerator<PERSON>lient
from sequence_generator.config import conf as conf_sq
from tx_manager.config import conf as conf_tx
from ceh_provider.config import conf as conf_ceh


# @pytest.mark.asyncio
@pytest.mark.skip
def test_nested_transaction():
    tx_client = TxManagerClient(base_url=conf_tx.SELF_BASE_URL)
    tx_main = tx_client.create_transaction()
    tx_nested = tx_client.create_transaction()
    url = f"http://0.0.0.0:8000/api/0.3/party/{tx_nested.tx_uid}"
    tx_client.add_party(str(tx_main.tx_uid), url)
    tx_client.commit(tx_nested.tx_uid)
    time.sleep(2)
    tx_client.commit(tx_main.tx_uid)
    time.sleep(5)

    tx_nested = tx_client.get_transaction_info(tx_nested.tx_uid)
    tx_main = tx_client.get_transaction_info(tx_main.tx_uid)

    # print(tx_main.tx_uid, tx_nested.tx_uid)

    assert tx_nested.stage.stage_no == 3
    assert tx_main.stage.stage_no == 3


@pytest.mark.skip
def test_nested_transaction_commit_rollback():
    tx_client = TxManagerClient(base_url=conf_tx.SELF_BASE_URL)
    tx_main = tx_client.create_transaction()
    tx_nested = tx_client.create_transaction()
    url = f"http://0.0.0.0:8000/api/0.3/party/{tx_nested.tx_uid}"
    tx_client.add_party(str(tx_main.tx_uid), url)
    tx_client.commit(tx_nested.tx_uid)
    time.sleep(2)
    tx_client.rollback(tx_main.tx_uid)
    time.sleep(5)

    tx_nested = tx_client.get_transaction_info(tx_nested.tx_uid)
    tx_main = tx_client.get_transaction_info(tx_main.tx_uid)

    # print(tx_main.tx_uid, tx_nested.tx_uid)

    assert tx_nested.stage.stage_no == 3
    assert tx_main.stage.stage_no == 3
    assert tx_nested.status.is_committed is True
    assert tx_main.status.is_committed is False


@pytest.mark.skip
def test_nested_transaction_rollback():
    tx_client = TxManagerClient(base_url=conf_tx.SELF_BASE_URL)
    tx_main = tx_client.create_transaction()
    tx_nested = tx_client.create_transaction()
    url = f"http://0.0.0.0:8000/api/0.3/party/{tx_nested.tx_uid}"
    tx_client.add_party(str(tx_main.tx_uid), url)
    tx_client.rollback(tx_nested.tx_uid)
    time.sleep(5)

    tx_nested = tx_client.get_transaction_info(tx_nested.tx_uid)
    tx_main = tx_client.get_transaction_info(tx_main.tx_uid)

    # print(tx_main.tx_uid, tx_nested.tx_uid)

    assert tx_nested.stage.stage_no == 3
    assert tx_main.stage.stage_no == 3
    assert tx_nested.status.is_committed is False
    assert tx_main.status.is_committed is False


@pytest.mark.skip
def test_nested_transaction_reverse_commit():
    tx_client = TxManagerClient(base_url=conf_tx.SELF_BASE_URL)
    tx_main = tx_client.create_transaction()
    tx_nested = tx_client.create_transaction()
    url = f"http://0.0.0.0:8000/api/0.3/party/{tx_nested.tx_uid}"
    tx_client.add_party(str(tx_main.tx_uid), url)
    tx_client.commit(tx_main.tx_uid)
    time.sleep(2)
    tx_client.commit(tx_nested.tx_uid)
    time.sleep(5)

    tx_nested = tx_client.get_transaction_info(tx_nested.tx_uid)
    tx_main = tx_client.get_transaction_info(tx_main.tx_uid)

    # print(tx_main.tx_uid, tx_nested.tx_uid)

    assert tx_nested.stage.stage_no == 3
    assert tx_main.stage.stage_no == 3


@pytest.mark.skip
def test_nested_transaction_resources():
    tx_client = TxManagerClient(base_url=conf_tx.SELF_BASE_URL)
    tx_main = tx_client.create_transaction()
    tx_nested = tx_client.create_transaction()
    url = f"http://0.0.0.0:8000/api/0.3/party/{tx_nested.tx_uid}"
    tx_client.add_party(str(tx_main.tx_uid), url)
    ceh_client = CehProviderClient(base_url=conf_ceh.SELF_BASE_REMOTE_URL)
    sq_client = SequenceGeneratorClient(base_url=conf_sq.SELF_BASE_REMOTE_URL)

    sq = sq_client.increment('res')
    ceh_client.create_resource(
        {
            "resource_desc": "string",
            "tags": [],
            "features": {},
            "resource_cd": str(sq.value),
            "is_readonly": False
        }
    )
    sq2 = sq_client.increment('res')
    ceh_client.create_resource(
        {
            "resource_desc": "string",
            "tags": [],
            "features": {},
            "resource_cd": str(sq2.value),
            "is_readonly": False
        }
    )

    ceh_client.update_resource_state(resource_cd=str(sq.value), tx_uid=str(tx_nested.tx_uid))
    ceh_client.update_resource_state(resource_cd=str(sq2.value), tx_uid=str(tx_main.tx_uid))

    tx_client.commit(tx_nested.tx_uid)
    time.sleep(2)
    tx_client.commit(tx_main.tx_uid)
    time.sleep(5)

    tx_nested = tx_client.get_transaction_info(tx_nested.tx_uid)
    tx_main = tx_client.get_transaction_info(tx_main.tx_uid)

    # print(str(sq.value), str(sq2.value))
    # print(tx_nested.tx_uid, tx_main.tx_uid)

    state1 = ceh_client.get_resource_state(resource_cd=str(sq.value))
    state2 = ceh_client.get_resource_state(resource_cd=str(sq2.value))

    assert tx_nested.stage.stage_no == 3
    assert tx_main.stage.stage_no == 3
    assert state1.is_locked is False
    assert state2.is_locked is False


@pytest.mark.skip
def test_nested_transaction_resources_double():
    tx_client = TxManagerClient(base_url=conf_tx.SELF_BASE_URL)
    tx_main = tx_client.create_transaction()
    tx_nested = tx_client.create_transaction()
    tx_nested_nested = tx_client.create_transaction()
    url = f"http://0.0.0.0:8000/api/0.3/party/{tx_nested.tx_uid}"
    url2 = f"http://0.0.0.0:8000/api/0.3/party/{tx_nested_nested.tx_uid}"
    tx_client.add_party(str(tx_main.tx_uid), url)
    tx_client.add_party(str(tx_main.tx_uid), url2)
    tx_client.add_party(str(tx_nested_nested.tx_uid), url2)
    ceh_client = CehProviderClient(base_url=conf_ceh.SELF_BASE_REMOTE_URL)
    sq_client = SequenceGeneratorClient(base_url=conf_sq.SELF_BASE_REMOTE_URL)

    sq = sq_client.increment('res')
    ceh_client.create_resource(
        {
            "resource_desc": "string",
            "tags": [],
            "features": {},
            "resource_cd": str(sq.value),
            "is_readonly": False
        }
    )
    sq2 = sq_client.increment('res')
    ceh_client.create_resource(
        {
            "resource_desc": "string",
            "tags": [],
            "features": {},
            "resource_cd": str(sq2.value),
            "is_readonly": False
        }
    )
    sq3 = sq_client.increment('res')
    ceh_client.create_resource(
        {
            "resource_desc": "string",
            "tags": [],
            "features": {},
            "resource_cd": str(sq3.value),
            "is_readonly": False
        }
    )

    ceh_client.update_resource_state(resource_cd=str(sq3.value), tx_uid=str(tx_nested_nested.tx_uid))
    ceh_client.update_resource_state(resource_cd=str(sq.value), tx_uid=str(tx_nested.tx_uid))
    ceh_client.update_resource_state(resource_cd=str(sq2.value), tx_uid=str(tx_main.tx_uid))

    tx_client.commit(tx_nested_nested.tx_uid)
    time.sleep(5)
    tx_client.commit(tx_nested.tx_uid)
    time.sleep(5)
    tx_client.commit(tx_main.tx_uid)
    time.sleep(5)

    tx_nested_nested = tx_client.get_transaction_info(tx_nested_nested.tx_uid)
    tx_nested = tx_client.get_transaction_info(tx_nested.tx_uid)
    tx_main = tx_client.get_transaction_info(tx_main.tx_uid)

    # print(str(sq.value), str(sq2.value))
    # print(tx_nested.tx_uid, tx_main.tx_uid)

    state1 = ceh_client.get_resource_state(resource_cd=str(sq.value))
    state2 = ceh_client.get_resource_state(resource_cd=str(sq2.value))
    state3 = ceh_client.get_resource_state(resource_cd=str(sq3.value))

    assert tx_nested_nested.stage.stage_no == 3
    assert tx_nested.stage.stage_no == 3
    assert tx_main.stage.stage_no == 3
    assert state1.is_locked is False
    assert state2.is_locked is False
    assert state3.is_locked is False
