import uuid
import pytest

from yarl import URL

from fastapi import status
from pydantic.json import timedelta_isoformat
from pydantic.datetime_parse import parse_duration

from tx_manager import app
from tx_manager.core.models import Transaction

BASE_URL = URL(f"http://localhost:8000/api/0.3/transactions")


@pytest.mark.asyncio
async def test_create_transaction(aiohttp_session) -> None:
    """ создание транзакции
    """
    async with aiohttp_session.post(
        BASE_URL,
        json={
            "commit_timeout": "PT30M"
        }
    ) as resp:
        assert resp.status == status.HTTP_201_CREATED
        data = await resp.json()
        assert data['tx_uid']
        assert data['tx_token']


@pytest.mark.asyncio
async def test_create_transaction_invalid_timeout(aiohttp_session) -> None:
    """ создание транзакции с невалидным таймаутом
    """
    async with aiohttp_session.post(
        BASE_URL,
        json={
            "commit_timeout": "42s"
        }
    ) as resp:
        assert resp.status == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_get_transactions(aiohttp_session, tx, finished_tx) -> None:
    """ получение списка транзакций
    """
    async with aiohttp_session.get(BASE_URL) as resp:
        assert resp.status == status.HTTP_200_OK

        transactions = await resp.json()
        # assert len(transactions[0]) == 1    # problem occurred because there no possibility to delete topics
        assert uuid.UUID(transactions[-1]['tx_uid']).hex == uuid.UUID(tx.tx_uid).hex
        assert uuid.UUID(transactions[0]['tx_uid']).hex != uuid.UUID(finished_tx.tx_uid).hex


@pytest.mark.asyncio
async def test_get_transaction(aiohttp_session, tx) -> None:
    """ получение транзакции по uid-у
    """
    async with aiohttp_session.get(BASE_URL) as resp:
        transaction = await resp.json()

    async with aiohttp_session.get(f"{BASE_URL}/{tx.tx_uid}") as resp:
        assert resp.status == status.HTTP_200_OK
        data = await resp.json()
        assert uuid.UUID(data['tx_uid']).hex == uuid.UUID(tx.tx_uid).hex


@pytest.mark.asyncio
async def test_get_transaction_not_found(aiohttp_session) -> None:
    """ получение транзакции по uid-у
        транзакция не найдена - 404
    """
    random_uuid = uuid.uuid4()
    async with aiohttp_session.get(f"{BASE_URL}/{random_uuid}") as resp:
        assert resp.status == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_update_transaction(aiohttp_session, tx) -> None:
    """ изменение таймаута транзакции
    """
    async with aiohttp_session.put(
        f"{BASE_URL}/{tx.tx_uid}",
        json={
            "commit_timeout": "PT42M",
            "tx_token": tx.tx_token
        }
    ) as resp:
        assert resp.status == status.HTTP_200_OK
        data = await resp.json()
        assert uuid.UUID(data['tx_uid']).hex == uuid.UUID(tx.tx_uid).hex
        assert data['commit_timeout'] == timedelta_isoformat(parse_duration("PT42M"))


@pytest.mark.asyncio
async def test_update_transaction_invalid_timeout(aiohttp_session, tx) -> None:
    """ изменение таймаута транзакции
        невалидный таймаут
    """
    async with aiohttp_session.put(
        f"{BASE_URL}/{tx.tx_uid}",
        json={
            "commit_timeout": "42s"
        }
    ) as resp:
        assert resp.status == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
@pytest.mark.skip
async def test_update_transaction_invalid_token(aiohttp_session, tx) -> None:
    """ изменение таймаута транзакции
        невалидный токен
    """
    async with aiohttp_session.put(
        f"{BASE_URL}/{tx.tx_uid}",
        json={
            "commit_timeout": "PT42M",
            "tx_token": "ololo"
        }
    ) as resp:
        assert resp.status == status.HTTP_401_UNAUTHORIZED


@pytest.mark.asyncio
async def test_update_transaction_not_prepare_phase(aiohttp_session, tx_ready_to_commit_phase) -> None:
    """ изменение таймаута транзакции
        не в фазе подготовки
    """
    async with aiohttp_session.put(
        f"{BASE_URL}/{tx_ready_to_commit_phase.tx_uid}",
        json={
            "commit_timeout": "PT42M",
            "tx_token": tx_ready_to_commit_phase.tx_token
        }
    ) as resp:
        assert resp.status == status.HTTP_403_FORBIDDEN


@pytest.mark.asyncio
async def test_add_party(aiohttp_session, tx):
    """ добавление участника
    """
    url = "http://example.com"
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/party",
        json={
            "url": url,
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_200_OK
    tx = await app.get_transaction(tx.tx_uid)
    tx_tx = Transaction(**tx.asdict())
    parties = await tx_tx.get_parties()
    assert parties[0].url == url


@pytest.mark.asyncio
async def test_add_party_invalid_url(aiohttp_session, tx):
    """ добавление участника, невалидный урл
    """
    url = "example.com"
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/party",
        json={
            "url": url,
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_422_UNPROCESSABLE_ENTITY


#@pytest.mark.asyncio
#async def test_add_party_invalid_token(aiohttp_session, tx):
#    """ добавление участника
#    """
#    url = "http://example.com"
#    async with aiohttp_session.post(
#        f"{BASE_URL}/{tx.tx_uid}/party",
#        json={
#            "url": url,
#            "tx_token": "ololo",
#        }
#    ) as resp:
#        assert resp.status == status.HTTP_401_UNAUTHORIZED


@pytest.mark.asyncio
async def test_add_party_already_added(aiohttp_session, tx):
    """ добавление участника, дублирование участника
    """
    url = "http://example.com"
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/party",
        json={
            "url": url,
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_200_OK

    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/party",
        json={
            "url": url,
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == 208


@pytest.mark.asyncio
async def test_add_party_not_prepare_phase(aiohttp_session, tx_ready_to_commit_phase):
    """ добавление участника, не фаза подготовки
    """
    url = "http://example.com"
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx_ready_to_commit_phase.tx_uid}/party",
        json={
            "url": url,
            "tx_token": tx_ready_to_commit_phase.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_403_FORBIDDEN


@pytest.mark.asyncio
async def test_commit(aiohttp_session, tx):
    """ коммит транзакции
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/commit",
        json={
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_202_ACCEPTED

    tx = await app.get_transaction(tx.tx_uid)
    assert tx.client_committed


@pytest.mark.asyncio
async def test_commit_already_reported(aiohttp_session, tx):
    """ коммит транзакции, команда уже была
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/commit",
        json={
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_202_ACCEPTED

    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/commit",
        json={
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == 208


@pytest.mark.asyncio
async def test_commit_not_prepare_phase(aiohttp_session, tx_ready_to_commit_phase):
    """ коммит транзакции не в фазе подготовки
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx_ready_to_commit_phase.tx_uid}/commit",
        json={
            "tx_token": tx_ready_to_commit_phase.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_403_FORBIDDEN


@pytest.mark.asyncio
async def test_commit_already_rollback(aiohttp_session, tx_not_consented_to_commit):
    """ коммит, когда уже решено что транзакция отменяется
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx_not_consented_to_commit.tx_uid}/commit",
        json={
            "tx_token": tx_not_consented_to_commit.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_409_CONFLICT


@pytest.mark.asyncio
@pytest.mark.skip
async def test_commit_invalid_token(aiohttp_session, tx):
    """ коммит, невалидный токен
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/commit",
        json={
            "tx_token": "ololo",
        }
    ) as resp:
        assert resp.status == status.HTTP_401_UNAUTHORIZED


@pytest.mark.asyncio
async def test_rollback(aiohttp_session, tx):
    """ отмена транзакции
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/rollback",
        json={
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_200_OK

    tx = await app.get_transaction(tx.tx_uid)
    assert tx.client_committed is False


@pytest.mark.asyncio
async def test_rollback_already_reported(aiohttp_session, tx):
    """ отмена транзакции, команда уже была
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/rollback",
        json={
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_200_OK

    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/rollback",
        json={
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == 208


@pytest.mark.asyncio
async def test_rollback_not_consented_to_commit(aiohttp_session, tx_not_consented_to_commit):
    """ отмена транзакции, когда уже решено что транзакция отменяется
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx_not_consented_to_commit.tx_uid}/rollback",
        json={
            "tx_token": tx_not_consented_to_commit.tx_token,
        }
    ) as resp:
        assert resp.status == 208


@pytest.mark.asyncio
async def test_rollback_not_prepare_phase(aiohttp_session, tx_ready_to_rollback_phase):
    """ отмена транзакции не в фазе подготовки
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx_ready_to_rollback_phase.tx_uid}/rollback",
        json={
            "tx_token": tx_ready_to_rollback_phase.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_403_FORBIDDEN


@pytest.mark.asyncio
async def test_rollback_consented_to_commit(aiohttp_session, tx):
    """ отмена транзакции не может быть выполнена, так как уже была команда на коммит
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/commit",
        json={
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_202_ACCEPTED

    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/rollback",
        json={
            "tx_token": tx.tx_token,
        }
    ) as resp:
        assert resp.status == status.HTTP_409_CONFLICT


@pytest.mark.asyncio
@pytest.mark.skip
async def test_rollback_invalid_token(aiohttp_session, tx):
    """ отмена транзакции, невалидный токен
    """
    async with aiohttp_session.post(
        f"{BASE_URL}/{tx.tx_uid}/rollback",
        json={
            "tx_token": "ololo",
        }
    ) as resp:
        assert resp.status == status.HTTP_401_UNAUTHORIZED
