from ceh_provider.utils.metric_actualizer import MetricActualizer
from lib.kafka.models.ceh_models import Resource, ResourceState
import pytest


ABC_METRIC_ID = "abc_actual_dttm"  # метрика для проверки сортировки
WAYN_METRIC_ID = "ceh.rdv.mart_account_account_agg_wayn:wayn_actual_dttm"  # метрика для проверки значений
WAYN_METRIC_RES = '2022-10-24T09:39:19.022196'
RESOURCE = Resource(
    **{
        "resource_desc": "Table ceh.rdv.mart_account_account_agg_wayn",
        "tags": [
            "WAYN"
        ],
        "features": {
            "domain": "RDV",
            "source_system": "WAYN"
        },
        "metrics": {
            "wayn_actual_dttm": {
                "id": WAYN_METRIC_ID,
                "query": "[.last_sources[].conf.by_src[]?.wayn_actual_dttm | values] | min",
                "default": "default_value"
            },
            "abc_actual_dttm": {
                "id": "abc_actual_dttm",
                "query": "[.last_sources[].conf.by_src[]?.abc_actual_dttm | values] | min",
                "default": "default_value"
            }
        },
        "resource_cd": "ceh.rdv.mart_account_account_agg_wayn",
        "is_readonly": False,
    }
)

RESOURCE_STATE = ResourceState(
    **{
        "resource_cd": "ceh.rdv.mart_account_account_agg_wayn",
        "is_locked": False,
        "locked_by": -1,
        "as_of_dttm": "2022-10-24T09:11:07.370819",
        "version": {
            "version_id": 307,
            "tx_uid": "ee144a13-c2f9-4875-98b1-6aec46a35e41",
            "operations": [
                {
                    "origin": {
                        "algorithm_uid": "2.WAYN.ZI10.35",
                        "engine": "airflow",
                        "airflow_run_id": "manual__2022-10-24T12:01:15+03:00",
                        "airflow_dag_id": "wf_gl_wayn_ods_rdv_mart_account_account_agg_wayn",
                        "airflow_run_uid": "5ni7r4oijvhx3ch6gntmqr",
                        "airflow_task_id": "reg_delta_mart_acc_acc_agg_wayn",
                        "author": "wf_gl_wayn_ods_rdv_mart_account_account_agg_wayn"
                    },
                    "source": {
                        "conf": {
                            "by_src": {
                                "ods.W4_SOH.H2_ACCOUNT_AGG": {
                                    "as_of_dttm": "2022-10-24T08:58:34.514803",
                                    "is_locked": False,
                                    "locked_by": -1,
                                    "dataset_max_date_to": "2021-11-18T00:00:00",
                                    "wf_max_date_to": "1900-01-02",
                                    "wayn_actual_dttm": "2022-10-24T09:39:19.022196",
                                    "wf_dataset_max_date_to": "2021-11-17T00:00:00"
                                }
                            },
                            "common": {}
                        },
                        "results": {
                            "by_src": {
                                "ods.W4_SOH.H2_ACCOUNT_AGG": {
                                    "wf_dataset_max_date_to": "2021-11-17T07:43:49"
                                }
                            },
                            "common": {}
                        }
                    },
                    "statement": "INSERT INTO rdv.mart_account_account_agg_wayn (account_rk, hash_diff, src_cd, invalid_id, version_id, deleted_flg, valid_flg, effective_date, account_number, deal_rk, code, currency_rk, account_type_config_rk, account_name, account_template_rk, date_open, date_close, gl_number, banking_date, id) SELECT rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.account_rk, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.hash_diff, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.src_cd, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.invalid_id, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.version_id, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.deleted_flg, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.valid_flg, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.effective_date, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.account_number, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.deal_rk, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.code, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.currency_rk, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.account_type_config_rk, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.account_name, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.account_template_rk, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.date_open, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.date_close, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.gl_number, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.banking_date, rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.id \nFROM rdv_dlt.v307_mart_acc_acc_agg_wayn_n1 \nWHERE rdv_dlt.v307_mart_acc_acc_agg_wayn_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
                    "delta": {
                        "table": "v307_mart_acc_acc_agg_wayn_n1"
                    },
                    "target": {
                        "resource_cd": "ceh.rdv.mart_account_account_agg_wayn",
                        "table": "mart_account_account_agg_wayn",
                        "schema_name": None
                    },
                    "status": {
                        "start_dttm": "2022-10-24T09:09:24.900832",
                        "finish_dttm": None,
                        "is_active": False,
                        "is_committed": False
                    }
                }
            ],
            "status": {
                "start_dttm": "2022-10-24T09:09:24.900832",
                "finish_dttm": "2022-10-24T09:11:07.258882",
                "is_active": False,
                "is_committed": True
            }
        },
        "last_sources": {
            "2.WAYN.ZI10.35": {
                "conf": {
                    "by_src": {
                        "ods.W4_SOH.H2_ACCOUNT_AGG": {
                            "as_of_dttm": "2022-10-24T08:58:34.514803",
                            "is_locked": False,
                            "locked_by": -1,
                            "dataset_max_date_to": "2021-11-18T00:00:00",
                            "wf_max_date_to": "1900-01-02",
                            "wayn_actual_dttm": "2022-10-24T09:39:19.022196",
                            "wf_dataset_max_date_to": "2021-11-17T00:00:00"
                        }
                    },
                    "common": {}
                },
                "results": {
                    "by_src": {
                        "ods.W4_SOH.H2_ACCOUNT_AGG": {
                            "wf_dataset_max_date_to": "2021-11-17T07:43:49"
                        }
                    },
                    "common": {}
                }
            },
            "transition": {
                "conf": {},
                "results": {
                    "by_src": {
                        "ods.W4_SOH.H2_ACCOUNT_AGG": {
                            "ods_wf_max_date_to": "2021-11-16T01:35:15",
                            "wayn_actual_dttm": "2022-10-23T09:39:19.022196",
                        }
                    }
                }
            }
        }
    }
)


def test_init():
    """Примитивный тест класса"""
    metric_actualizer = MetricActualizer(
        resource=RESOURCE,
        resource_state=RESOURCE_STATE,
    )
    assert isinstance(metric_actualizer, MetricActualizer)


@pytest.mark.asyncio
async def test_actualize():
    """Тест простого расчета jq-метрик"""
    metric_actualizer = MetricActualizer(
        resource=RESOURCE,
        resource_state=RESOURCE_STATE,
    )
    res = await metric_actualizer.actualize()
    res.pop(ABC_METRIC_ID)
    assert {'wayn_actual_dttm': WAYN_METRIC_RES} == res


@pytest.mark.asyncio
async def test_actualize_model_sort():
    """Тест сложного (таргет вариант) расчета jq-метрик
    Тест сортировки
    """
    metric_actualizer = MetricActualizer(
        resource=RESOURCE,
        resource_state=RESOURCE_STATE,
    )
    res = await metric_actualizer.get_resource_metrics()
    assert res.metrics[0].id == ABC_METRIC_ID
    assert res.metrics[1].id == WAYN_METRIC_ID


@pytest.mark.asyncio
async def test_actualize_model_calc():
    """Тест сложного (таргет вариант) расчета jq-метрик
    Тест расчета значений
    """
    metric_actualizer = MetricActualizer(
        resource=RESOURCE,
        resource_state=RESOURCE_STATE,
    )
    res = await metric_actualizer.get_resource_metrics()
    assert res.metrics[1].id == WAYN_METRIC_ID
    assert res.metrics[1].value == WAYN_METRIC_RES


@pytest.mark.asyncio
async def test_actualize_model_limit_offset():
    """Тест сложного (таргет вариант) расчета jq-метрик
    Тест полей offset и limit
    """
    metric_actualizer = MetricActualizer(
        resource=RESOURCE,
        resource_state=RESOURCE_STATE,
        limit=1, offset=0
    )
    res = await metric_actualizer.get_resource_metrics()
    assert res.metrics[0].id == ABC_METRIC_ID
    assert res.nextContinuationToken == 1

    metric_actualizer = MetricActualizer(
        resource=RESOURCE,
        resource_state=RESOURCE_STATE,
        limit=1, offset=1
    )
    res = await metric_actualizer.get_resource_metrics()
    assert res.metrics[0].id == WAYN_METRIC_ID


@pytest.mark.asyncio
async def test_actualize_model_jq_filter():
    """Тест сложного (таргет вариант) расчета jq-метрик
    Тест jq-фильтра
    """
    metric_actualizer = MetricActualizer(
        resource=RESOURCE,
        resource_state=RESOURCE_STATE,
        jq_filter=f'select(.id == "{WAYN_METRIC_ID}")'
    )
    res = await metric_actualizer.get_resource_metrics()
    assert len(res.metrics) == 1
    assert res.metrics[0].id == WAYN_METRIC_ID
    assert res.metrics[0].value == WAYN_METRIC_RES
