# Настройки

## БД

В качестве target (DR) БД я использовал dev_dr (d5dtpl-apc014lk), ТУЗ dev_babenko
В качестве source (main) БД я использовал dev_dwh (d5dtpl-apc014lk), ТУЗ dev_babenko

Пароли в репо закидывать не буду

## Настройки сети и сервисов

Пример docker-compose файла есть в s3_repl_docker-compose.yaml. Скопируй настройки
ceh_provider_back, ceh_provider_front, delta_manager и delta_manager1 в основной docker-compose (пароли НЕ заполнены).

Что касается сети:

Я подключал банковский vpn через check point на host системе (из которой запускается виртуалка).

В настройках network linux виртуалки ставил NAT на все адаптеры.
Если пересобираешь образы и ловишь ошибки при обращении к внешним источникам - поставь 1 адаптер на bridge.
Когда соберешь image - переключи адаптеры обратно на NAT, иначе контейнеры не подключатся к БД в dev.corp.

Можешь запускать сервисы:
```bash
sudo docker-compose rm -fsv && \
sudo docker-compose down -v --remove-orphans && \
sudo docker-compose up -d ceh_provider_back ceh_provider_front tx_manager delta_manager delta_manager1
```

# Режимы

Далее идут примеры создаваемых таблиц, ресурсов и json'ов на обновление состояния. insert SQL для таблиц лежат рядом
с этим md-файлом в отдельных SQL-скриптах.

## Append only

### Структуры

Создайте rdv.account на source БД и target БД. Создайте public.account_qerasdfzxcv на source

```SQL
CREATE TABLE rdv.account (
	account_rk int8 NOT NULL,
	effective_from_date date NOT NULL,
	effective_to_date date NOT NULL,
	account_close_date date NULL,
	account_desc varchar NULL,
	account_function_cd varchar NULL,
	account_id varchar NULL,
	account_mode_cd varchar NULL,
	account_num varchar NULL,
	account_open_date date NULL,
	account_plan_cd varchar NULL,
	account_status_cd varchar NULL,
	account_subtype_cd varchar NULL,
	account_svod_flg bool NULL,
	account_technical_flg bool NULL,
	account_type_cd varchar NULL,
	counterparty_rk int8 NULL,
	currency_rk int8 NULL,
	department_rk int8 NULL,
	employee_rk int8 NULL,
	gl_account_num varchar NULL,
	account_valodity_date date NULL,
	src_cd varchar NOT NULL,
	version_id int8 NOT NULL,
	deleted_flg bool NOT NULL,
	hash_diff bpchar(32) NOT NULL,
	stock_point_department_rk int8 NULL,
	account_analytical_flg bool NULL,
	account_ledger_flg bool NULL,
	json_col jsonb NULL,
	json_col2 jsonb NULL
)
DISTRIBUTED BY (account_rk);

CREATE TABLE public.account_qerasdfzxcv (
	account_rk int8 NOT NULL,
	effective_from_date date NOT NULL,
	effective_to_date date NOT NULL,
	account_close_date date NULL,
	account_desc varchar NULL,
	account_function_cd varchar NULL,
	account_id varchar NULL,
	account_mode_cd varchar NULL,
	account_num varchar NULL,
	account_open_date date NULL,
	account_plan_cd varchar NULL,
	account_status_cd varchar NULL,
	account_subtype_cd varchar NULL,
	account_svod_flg bool NULL,
	account_technical_flg bool NULL,
	account_type_cd varchar NULL,
	counterparty_rk int8 NULL,
	currency_rk int8 NULL,
	department_rk int8 NULL,
	employee_rk int8 NULL,
	gl_account_num varchar NULL,
	account_valodity_date date NULL,
	src_cd varchar NOT NULL,
	version_id int8 NOT NULL,
	deleted_flg bool NOT NULL,
	hash_diff bpchar(32) NOT NULL,
	stock_point_department_rk int8 NULL,
	account_analytical_flg bool NULL,
	account_ledger_flg bool NULL,
	record_mode text NULL,
	delta_record_mode text NULL,
	json_col jsonb NULL DEFAULT '{"qwer": 1}'::jsonb,
	json_col2 jsonb NULL DEFAULT '{"qwer": 1}'::jsonb
)
DISTRIBUTED BY (account_rk);
```

### Ресурс

```json
{
  "resource_desc": "",
  "tags": [],
  "features": {},
  "metrics": {},
  "resource_cd": "string",
  "is_readonly": false,
  "is_deleted": false,
  "datasets": []
}
```

### Обновление состояния

```json
{
  "tx_uid": "string",
  "operation": {
    "type": "delta",
    "origin": {
      "algorithm_uid": "append_alg",
      "author": "babenkoda"
    },
    "source": {},
    "statement": "insert into rdv.account (json_col, json_col2, effective_from_date, effective_to_date, account_close_date, account_open_date, account_valodity_date, account_desc, account_function_cd, account_id, account_mode_cd, account_num, account_plan_cd, account_status_cd, account_subtype_cd, account_type_cd, gl_account_num, src_cd, hash_diff, account_rk, counterparty_rk, currency_rk, department_rk, employee_rk, version_id, stock_point_department_rk, account_svod_flg, account_technical_flg, deleted_flg, account_analytical_flg, account_ledger_flg) select \"json_col\", json_col2, effective_from_date, effective_to_date, account_close_date, account_open_date, account_valodity_date, account_desc, account_function_cd, account_id, account_mode_cd, account_num, account_plan_cd, account_status_cd, account_subtype_cd, account_type_cd, gl_account_num, src_cd, hash_diff, account_rk, counterparty_rk, currency_rk, department_rk, employee_rk, version_id, stock_point_department_rk, \"account_svod_flg\", account_technical_flg, deleted_flg, account_analytical_flg, account_ledger_flg from public.account_qerasdfzxcv where 1=1;",
    "delta": {
      "table": "account_qerasdfzxcv",
      "schema_name": "public"
    },
    "target": {
      "resource_cd": "string",
      "table": "account",
      "schema_name": "rdv"
    }
  }
}
```

или без statement

```json
{
  "tx_uid": "string",
  "operation": {
    "type": "delta",
    "origin": {
      "algorithm_uid": "append_alg",
      "author": "babenkoda"
    },
    "source": {},
    "delta": {
      "table": "account_qerasdfzxcv",
      "schema_name": "public"
    },
    "target": {
      "resource_cd": "string",
      "table": "account",
      "schema_name": "rdv"
    }
  }
}
```

## SCD2-classic

### Структуры

Создайте rdv.account_scd2 на source БД и target БД. Создайте public.account_scd2_qwerty на source

```SQL
CREATE TABLE rdv.account_scd2 (
	json_col jsonb NULL,
	json_col2 jsonb NULL,
	account_id text NULL,
	account_rk int8 NULL,
	account_ledger_flg bool NULL,
	version_id int8 NULL,
	to_version_id int8 NULL,
	deleted_flg bool NULL
)
DISTRIBUTED BY (account_rk);

CREATE TABLE public.account_scd2_qwerty (
	json_col jsonb NULL,
	json_col2 jsonb NULL,
	account_id text NULL,
	account_rk int8 NULL,
	account_ledger_flg bool NULL,
	delta_record_mode text NULL
)
DISTRIBUTED BY (account_rk);
```

### Ресурс

```json
{
  "resource_desc": "",
  "tags": [],
  "features": {},
  "metrics": {},
  "resource_cd": "string",
  "is_readonly": false,
  "is_deleted": false,
  "datasets": [
    {
      "name": "account_scd2",
      "schema_name": "rdv",
      "filter": "",
      "columns": [
        {
          "name": "json_col",
          "type": "jsonb",
          "primary_key": false,
          "nullable": true
        },
        {
          "name": "json_col",
          "type": "jsonb",
          "primary_key": true,
          "nullable": true
        },
        {
          "name": "account_id",
          "type": "text",
          "primary_key": false,
          "nullable": true
        },
        {
          "name": "account_rk",
          "type": "bigint",
          "primary_key": true,
          "nullable": false
        },
        {
          "name": "account_ledger_flg",
          "type": "boolean",
          "primary_key": false,
          "nullable": true
        },
        {
          "name": "version_id",
          "type": "bigint",
          "primary_key": false,
          "nullable": false
        },
        {
          "name": "to_version_id",
          "type": "bigint",
          "primary_key": false,
          "nullable": false
        },
        {
          "name": "deleted_flg",
          "type": "boolean",
          "primary_key": false,
          "nullable": false
        }
      ]
    }
  ]
}
```

### Обновление состояния

```json
{
  "tx_uid": "string",
  "operation": {
    "type": "delta",
    "origin": {
      "algorithm_uid": "scd2_append_only",
      "author": "babenkoda"
    },
    "source": {
      "conf": {},
      "results": {}
    },
    "delta": {
      "table": "account_scd2_qwerty",
      "schema_name": "public"
    },
    "target": {
      "resource_cd": "string",
      "table": "account_scd2",
      "schema_name": "rdv"
    },
    "do": {
      "target": {
        "name": "account_scd2",
        "schema_name": "rdv",
        "filter": null,
        "columns": [
          {
            "name": "json_col",
            "type": "jsonb",
            "primary_key": false,
            "nullable": true
          },
          {
            "name": "json_col2",
            "type": "jsonb",
            "primary_key": false,
            "nullable": true
          },
          {
            "name": "account_id",
            "type": "text",
            "primary_key": false,
            "nullable": true
          },
          {
            "name": "account_rk",
            "type": "bigint",
            "primary_key": true,
            "nullable": false
          },
          {
            "name": "account_ledger_flg",
            "type": "boolean",
            "primary_key": false,
            "nullable": true
          },
          {
            "name": "version_id",
            "type": "bigint",
            "primary_key": false,
            "nullable": false
          },
          {
            "name": "to_version_id",
            "type": "bigint",
            "primary_key": false,
            "nullable": false
          },
          {
            "name": "deleted_flg",
            "type": "boolean",
            "primary_key": false,
            "nullable": false
          }
        ],
        "physical_options": null
      },
      "delta": {
        "name": "account_scd2_qwerty",
        "schema_name": "public",
        "filter": null,
        "columns": [
          {
            "name": "json_col",
            "type": "jsonb",
            "primary_key": false,
            "nullable": true
          },
          {
            "name": "json_col2",
            "type": "jsonb",
            "primary_key": false,
            "nullable": true
          },
          {
            "name": "account_id",
            "type": "text",
            "primary_key": false,
            "nullable": true
          },
          {
            "name": "account_rk",
            "type": "bigint",
            "primary_key": true,
            "nullable": false
          },
          {
            "name": "account_ledger_flg",
            "type": "boolean",
            "primary_key": false,
            "nullable": true
          },
          {
            "name": "delta_record_mode",
            "type": "text",
            "primary_key": false,
            "nullable": false
          }
        ],
        "physical_options": null
      },
      "versioning": "scd2-append-only"
    }
  }
}
```
