import asyncio
import aiohttp
import uuid
import pytest

from asyncio import get_event_loop
from typing import Generator
from httpx import AsyncClient
from yarl import URL

# from ceh_provider import faust_worker
# from ceh_provider import api

BASE_CEH_URL = f'http://localhost:{8000}/api/0.3/resources'
BASE_TX_URL = URL(f'http://tx_manager:{8000}/api/0.3/transactions')


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    loop = get_event_loop()
    yield loop


# @pytest.fixture(scope="session", autouse=True)
# async def faust_app(event_loop):
#     print(f'create faust app')
#     faust_worker.set_faust_app_for_api()
#     faust_app = faust_worker.get_faust_app()
#     asyncio.create_task(faust_app.start_client())
#     # ждем ребалансировки
#     await asyncio.sleep(25)
#     yield faust_app
#     print(f'stop faust app')
#     await faust_app.stop()


# @pytest.fixture(scope="function")
# async def async_client():
#     async with AsyncClient(app=api.fastapi_app, base_url='http://localhost:8000') as ac:
#         yield ac


@pytest.fixture(scope="session", autouse=True)
def resource_cd():
    yield uuid.uuid4().hex


# @pytest.fixture(scope="session", autouse=True)
# async def tx_uid():
#     return await create_transaction()


# @pytest.fixture(scope="function", autouse=True)
# async def tx_uid_uniq():
#     tx_uid = await create_transaction()
#     yield tx_uid
#     await rollback_transaction(tx_uid)


# async def create_transaction():
#     async with aiohttp.ClientSession().post(
#             BASE_TX_URL,
#             json={
#                 "commit_timeout": 0
#             }
#     ) as resp:
#         data = await resp.json()
#         tx_uid = data["tx_uid"]
#     return tx_uid
#
#
# async def rollback_transaction(tx_uid):
#     async with aiohttp.ClientSession().post(
#             BASE_TX_URL / f"{tx_uid}/rollback",
#             json={
#             }
#     ) as resp:
#         pass


@pytest.fixture(autouse=True)
def json_body_create(resource_cd):
    yield {
        "resource_desc": f'{resource_cd}',
        "resource_cd": f'{resource_cd}',
        "is_readonly": False
    }


# @pytest.fixture(scope="function", autouse=True)    # scope=function is temporary since it is better session
# async def resource(async_client):
#     resource_cd = uuid.uuid4().hex
#     resp = await async_client.post(
#         BASE_CEH_URL,
#         json={
#             "resource_desc": resource_cd,
#             "resource_cd": resource_cd,
#             "is_readonly": False
#         }
#     )
#     data = resp.json()
#
#     yield data["resource_cd"]


# @pytest.fixture(autouse=True)
# def json_body_create_state(resource):
#     yield {
#         "origin": {
#           "additionalProp1": "additionalProp1",
#           "additionalProp2": "additionalProp2",
#           "additionalProp3": "additionalProp3"
#         },
#         "sources": [
#           {
#             "resource_cd": resource,
#             "state": {
#               "version": {
#                 "version_id": 0
#               }
#             },
#             "prev_state": {
#               "version": {
#                 "version_id": 0
#               }
#             }
#           },
#           {
#             "resource_cd": resource,
#             "state": {
#               "max_processed_dt": "2021-06-11T11:58:56.140Z",
#               "ods_wf_max_date_to": "2021-11-02T13:14:24.850Z"
#
#             },
#             "prev_state": {
#               "max_processed_dt": "2021-06-11T11:58:56.140Z",
#               "ods_wf_max_date_to": "2021-11-02T13:14:24.850Z"
#             }
#           }
#         ],
#         "statement": "insert into table1 values ('a3_4')",
#         "delta": {
#           "table": "table1"
#         },
#         "target": {
#           "resource_cd": "table1",
#           "table": "table1"
#         },
#         "status": {
#           "start_dttm": "2021-06-11T11:58:56.140Z",
#           "finish_dttm": "2021-06-11T11:58:56.140Z",
#           "is_active": True,
#           "is_committed": False
#         }
#       }


@pytest.fixture(scope="function")
def current_lock_table():
    return dict()


@pytest.fixture(scope="function")
def lock_queue_table():
    return dict()


@pytest.fixture(scope="function")
def states_queue_table():
    return dict()


@pytest.fixture(scope="function")
def faust_tables_mapping():
    faust_tables = {
        '_g_locks_t': {
            'table': dict(),
            'dist_key': 'resource_cd',
        },
        '_g_locks_q_t': {
            'table': dict(),
            'dist_key': 'resource_cd',
        },
        '_resource_states_q_t_k': {
            'table': dict(),
            'dist_key': 'resource_cd',
        },
    }
    return faust_tables
