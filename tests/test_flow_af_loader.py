from datetime import datetime
from typing import <PERSON>ple

from metaloader_rest_api.af_client import AfClient
from metaloader_rest_api.flow_af_loader import FlowAfLoader
from metaloader_rest_api.flow_repository import (
    FlowRepository,
    StageFlowRepository,
    StageMasterFlowLinkRepository,
    StageMasterFlowRepository,
)
from metaloader_rest_api.module_repository import ModuleRepository
from metaloader_rest_api.version_repository import VersionRepository
from pytest import fixture
from sqlalchemy import text
from sqlalchemy.orm import Session


def test_load(
    db_session,
    truncate_flow,
    flow_repository,
    stage_flow_repository,
    stage_master_flow_repository,
    stage_master_flow_link_repository,
    version_repository,
    module_repository,
    session_resource,
    core_module,
    effective_date,
    version,
    af_client,
    page_size,
):
    flow_loader = FlowAfLoader(
        flow_repository,
        stage_flow_repository,
        stage_master_flow_repository,
        stage_master_flow_link_repository,
        version_repository,
        module_repository,
        session_resource,
        af_client,
    )
    flow_loader.load(
        module=core_module,
        version=version,
        effective_date=effective_date,
        page_size=page_size,
        limit=7,
    )

    flows = db_session.execute(
        text("""
            SELECT *
              FROM metamodel.bridge_flow
         """)
    ).fetchall()

    assert len(flows) == 6


@fixture(scope="function")
def flow_repository(
    db_session: Session,
) -> FlowRepository:
    return FlowRepository(db_session)


@fixture(scope="function")
def stage_flow_repository(
    db_session: Session,
    table_id: str,
    page_size: int,
) -> StageFlowRepository:
    return StageFlowRepository(db_session, table_id, page_size)


@fixture(scope="function")
def stage_master_flow_repository(
    db_session: Session,
    table_id: str,
    page_size: int,
) -> StageMasterFlowRepository:
    return StageMasterFlowRepository(db_session, table_id, page_size)


@fixture(scope="function")
def stage_master_flow_link_repository(
    db_session: Session,
    table_id: str,
    page_size: int,
) -> StageMasterFlowLinkRepository:
    return StageMasterFlowLinkRepository(db_session, table_id, page_size)


@fixture(scope="function")
def version_repository(
    db_session: Session,
) -> VersionRepository:
    return VersionRepository(db_session)


@fixture(scope="function")
def module_repository(
    db_session: Session,
) -> ModuleRepository:
    return ModuleRepository(db_session)


@fixture(scope="function")
def table_id() -> str:
    return 32 * "0"


@fixture(scope="function")
def effective_date() -> datetime:
    return datetime(2024, 6, 15)


@fixture(scope="function")
def version() -> Tuple[int, int, int]:
    return 1, 2, 3


@fixture(scope="function")
def core_module(db_session) -> str:
    db_session.execute(
        text("""
        INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                          , code_delivery_cd
                                          , code_delivery_name
                                          , is_ddl_flg
                                          , is_flow_flg
                                          , is_config_flg)
        VALUES (1
              , 'core'
              , 'core'
              , FALSE
              , TRUE
              , FALSE)
    """)
    )
    db_session.commit()

    return "core"


@fixture(scope="function")
def af_client(af_url, af_creds) -> AfClient:
    return AfClient(url=af_url, **af_creds)


@fixture(scope="function")
def page_size() -> int:
    return 3


@fixture(scope="function")
def truncate_flow(db_session: Session):
    db_session.execute(text("TRUNCATE dict.dict_code_delivery"))
    db_session.execute(text("TRUNCATE metamodel.bridge_version"))
    db_session.execute(text("TRUNCATE metamodel.bridge_flow"))
    db_session.execute(text("TRUNCATE metamodel.link_flow"))
    db_session.commit()
