from metaloader_rest_api.flow_resource.flow_resource_model import (
    FlowResource,
    source_flow_resource,
    target_flow_resource,
)
from metaloader_rest_api.flow_resource.flow_resource_processor_unit_rdv import (
    RdvFlowResourceProcessorUnit,
)
from pytest import fixture, mark, param

from .conftest import load_flow_data

test_process_cases = [
    param(
        1,
        "wf_test_01",
        [
            # NOTE: пайплайн парсинга-загрузки связей поток-ресурс сделан так, что дубли удаляются
            #  позже, уже внутри БД, на этапе загрузки в стейджинг. Поэтому в тестах намеренно
            #  оставляем дублирующиеся записи.
            dup_link_1 := target_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=1,
                resource_name="test.resource.tgt.1",
                resource_schema_name="rdv",
                resource_table_name="table_1",
            ),
            source_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=1,
                resource_name="test.resource.src.1",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_2",
                flow_id=1,
                resource_name="test.resource.tgt.2",
                resource_schema_name="rdv",
                resource_table_name="table_2",
            ),
            dup_link_2 := source_flow_resource(
                algorithm="ALGORITHM_2",
                flow_id=1,
                resource_name="test.resource.src.2",
            ),
            dup_link_1,
            source_flow_resource(
                algorithm="ALGORITHM_1",
                flow_id=1,
                resource_name="test.resource.src.2",
            ),
            target_flow_resource(
                algorithm="ALGORITHM_2",
                flow_id=1,
                resource_name="test.resource.tgt.1",
                resource_schema_name="rdv",
                resource_table_name="table_1",
            ),
            dup_link_2,
        ],
        id="simple case: source and target in mappings",
    ),
    param(
        3,
        "wf_test_03",
        [
            # Target resource
            target_flow_resource(
                algorithm="ALGORITHM_CT_1",
                flow_id=3,
                resource_name="ceh.rdv.mart_personlocation_dks",
                resource_schema_name="rdv",
                resource_table_name="mart_personlocation_dks",
            ),
            # Source resources from computed table
            source_flow_resource(
                algorithm="ALGORITHM_CT_1",
                flow_id=3,
                resource_name="dapp.prod_repl_subo_dks.dks_drp_personlocation",
            ),
            source_flow_resource(
                algorithm="ALGORITHM_CT_1",
                flow_id=3,
                resource_name="dapp.prod_repl_subo_dks.dks_drp_dcapplicantquestionnaire",
            ),
        ],
        id="computed tables case: source with COMPUTED_TABLE type",
    ),
]


@mark.parametrize("flow_id, flow_name, expected", test_process_cases)
def test_process(
    flow_id,
    flow_name,
    expected,
    rdv_flow_resource_processor,
    yaml_src_rdv_path,
):
    _, _, flow_content = load_flow_data(yaml_src_rdv_path, flow_id, flow_name)
    actual = list(rdv_flow_resource_processor.process(flow_id, flow_name, flow_content))
    assert len(actual) == len(expected)

    sort_flow_resources_inplace(actual)
    sort_flow_resources_inplace(expected)
    assert actual == expected


@fixture(scope="function")
def rdv_flow_resource_processor(http_loader):
    return RdvFlowResourceProcessorUnit(http_loader)


def sort_flow_resources_inplace(flow_resources: list[FlowResource]) -> None:
    flow_resources.sort(key=lambda r: (r["resource_name"], r["algorithm"]))
