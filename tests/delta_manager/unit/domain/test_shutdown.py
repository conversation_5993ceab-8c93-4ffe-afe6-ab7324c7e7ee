import pytest
from lib.consts import ServiceMode
from delta_manager.router_service import _ready_for_shutdown


@pytest.mark.parametrize(
    'service_mode, expected',
    [
        (ServiceMode.OFFLINE.value, True),
        (None, False),
        (ServiceMode.ONLINE.value, False),
    ]
)
def test_sequence_generator_api_system__get_shutdown_status(service_mode, expected):

    assert expected == _ready_for_shutdown(
        service_mode=service_mode,
    )
