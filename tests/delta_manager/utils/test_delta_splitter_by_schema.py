
import logging

from delta_manager.utils.delta_splitter_by_schema import DeltaSplitterBySchema
from lib.kafka.models.ceh_models import OperationStatus, TopicVersion

# region version
VERSION_0_6_EMPTY_DELTA_EMPTY_QUERY = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': None,
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.rdv.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_EMPTY_QUERY_DM_IN_OPERATION = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': None,
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
            'delta': {
                'table': 'v2969_hub_application_n1',
                'schema_name': 'dm_dlt',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.rdv.hub_application',
                'table': 'hub_application_v2',
                'schema_name': 'dm_pik',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.rdv.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_DM = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': "INSERT INTO dm_pik.hub_application_v2 (application_rk, application_id, src_cd, bk_type, "
                         "invalid_id, version_id) SELECT dm_dlt.v2969_hub_application_n1.application_rk, "
                         "dm_dlt.v2969_hub_application_n1.application_id, dm_dlt.v2969_hub_application_n1.src_cd, "
                         "dm_dlt.v2969_hub_application_n1.bk_type, dm_dlt.v2969_hub_application_n1.invalid_id, "
                         "dm_dlt.v2969_hub_application_n1.version_id \nFROM dm_dlt.v2969_hub_application_n1 "
                         "\nWHERE dm_dlt.v2969_hub_application_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
            'delta': {
                'table': 'v2969_hub_application_n1',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.dm.hub_application',
                'table': 'hub_application_v2',
                'schema_name': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_dm_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_dm_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.dm.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_RDV = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': "INSERT INTO rdv.hub_application_v2 (application_rk, application_id, src_cd, bk_type, "
                         "invalid_id, version_id) SELECT rdv_dlt.v2969_hub_application_n1.application_rk, "
                         "rdv_dlt.v2969_hub_application_n1.application_id, rdv_dlt.v2969_hub_application_n1.src_cd, "
                         "rdv_dlt.v2969_hub_application_n1.bk_type, rdv_dlt.v2969_hub_application_n1.invalid_id, "
                         "rdv_dlt.v2969_hub_application_n1.version_id \nFROM rdv_dlt.v2969_hub_application_n1 "
                         "\nWHERE rdv_dlt.v2969_hub_application_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
            'delta': {
                'table': 'v2969_hub_application_n1',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.rdv.hub_application',
                'table': 'hub_application_v2',
                'schema_name': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.rdv.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_IDL = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': "INSERT INTO idl.hub_application_v2 (application_rk, application_id, src_cd, bk_type, "
                         "invalid_id, version_id) SELECT idl_dlt.v2969_hub_application_n1.application_rk, "
                         "idl_dlt.v2969_hub_application_n1.application_id, idl_dlt.v2969_hub_application_n1.src_cd, "
                         "idl_dlt.v2969_hub_application_n1.bk_type, idl_dlt.v2969_hub_application_n1.invalid_id, "
                         "idl_dlt.v2969_hub_application_n1.version_id \nFROM idl_dlt.v2969_hub_application_n1 "
                         "\nWHERE idl_dlt.v2969_hub_application_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
            'delta': {
                'table': 'v2969_hub_application_n1',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.rdv.hub_application',
                'table': 'hub_application_v2',
                'schema_name': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.rdv.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_RAWD = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': "INSERT INTO rawd.hub_application_v2 (application_rk, application_id, src_cd, bk_type, "
                         "invalid_id, version_id) SELECT rawd_dlt.v2969_hub_application_n1.application_rk, "
                         "rawd_dlt.v2969_hub_application_n1.application_id, rawd_dlt.v2969_hub_application_n1.src_cd, "
                         "rawd_dlt.v2969_hub_application_n1.bk_type, rawd_dlt.v2969_hub_application_n1.invalid_id, "
                         "rawd_dlt.v2969_hub_application_n1.version_id \nFROM rawd_dlt.v2969_hub_application_n1 "
                         "\nWHERE rawd_dlt.v2969_hub_application_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
            'delta': {
                'table': 'v2969_hub_application_n1',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.rdv.hub_application',
                'table': 'hub_application_v2',
                'schema_name': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.rdv.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_DM_SCHEMA_IN_OPERATION = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_dm_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_dm_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': "INSERT INTO dm.hub_application_v2 (application_rk, application_id, src_cd, bk_type, "
                         "invalid_id, version_id) SELECT dm_dlt.v2969_hub_application_n1.application_rk, "
                         "dm_dlt.v2969_hub_application_n1.application_id, dm_dlt.v2969_hub_application_n1.src_cd, "
                         "dm_dlt.v2969_hub_application_n1.bk_type, dm_dlt.v2969_hub_application_n1.invalid_id, "
                         "dm_dlt.v2969_hub_application_n1.version_id \nFROM dm_dlt.v2969_hub_application_n1 \nWHERE "
                         "dm_dlt.v2969_hub_application_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
            'delta': {
                'table': 'v2969_hub_application_n1',
                'schema_name': 'dm_dlt',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.dm.hub_application',
                'table': 'hub_application_v2',
                'schema_name': 'dm',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_dm_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_dm_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.dm.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_RDV_SCHEMA_IN_OPERATION = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': "INSERT INTO rdv.hub_application_v2 (application_rk, application_id, src_cd, bk_type, "
                         "invalid_id, version_id) SELECT rdv_dlt.v2969_hub_application_n1.application_rk, "
                         "rdv_dlt.v2969_hub_application_n1.application_id, rdv_dlt.v2969_hub_application_n1.src_cd, "
                         "rdv_dlt.v2969_hub_application_n1.bk_type, rdv_dlt.v2969_hub_application_n1.invalid_id, "
                         "rdv_dlt.v2969_hub_application_n1.version_id \nFROM rdv_dlt.v2969_hub_application_n1 \nWHERE "
                         "rdv_dlt.v2969_hub_application_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
            'delta': {
                'table': 'v2969_hub_application_n1',
                'schema_name': 'rdv_dlt',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.rdv.hub_application',
                'table': 'hub_application_v2',
                'schema_name': 'rdv',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.rdv.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_IDL_SCHEMA_IN_OPERATION = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': "INSERT INTO idl.hub_application_v2 (application_rk, application_id, src_cd, bk_type, "
                         "invalid_id, version_id) SELECT idl_dlt.v2969_hub_application_n1.application_rk, "
                         "idl_dlt.v2969_hub_application_n1.application_id, idl_dlt.v2969_hub_application_n1.src_cd, "
                         "idl_dlt.v2969_hub_application_n1.bk_type, idl_dlt.v2969_hub_application_n1.invalid_id, "
                         "idl_dlt.v2969_hub_application_n1.version_id \nFROM idl_dlt.v2969_hub_application_n1 \nWHERE "
                         "idl_dlt.v2969_hub_application_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
            'delta': {
                'table': 'v2969_hub_application_n1',
                'schema_name': 'idl_dlt',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.rdv.hub_application',
                'table': 'hub_application_v2',
                'schema_name': 'idl',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.rdv.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)

VERSION_0_6_RAWD_SCHEMA_IN_OPERATION = TopicVersion(
    version_id='2969',
    tx_uid='cff6717b-1219-4109-aa9e-2197a579fd41',
    status=OperationStatus(
        start_dttm='2022-10-12T11:48:25.685804',
        is_active=True,
        is_committed=True,
        finish_dttm=None,
        is_processed=None,
        is_undone=None),
    operations=[
        {
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'lock',
        },
        {
            'statement': "INSERT INTO rawd.hub_application_v2 (application_rk, application_id, src_cd, bk_type, "
                         "invalid_id, version_id) SELECT rawd_dlt.v2969_hub_application_n1.application_rk, "
                         "rawd_dlt.v2969_hub_application_n1.application_id, rawd_dlt.v2969_hub_application_n1.src_cd, "
                         "rawd_dlt.v2969_hub_application_n1.bk_type, rawd_dlt.v2969_hub_application_n1.invalid_id, "
                         "rawd_dlt.v2969_hub_application_n1.version_id \nFROM rawd_dlt.v2969_hub_application_n1 \nWHERE "
                         "rawd_dlt.v2969_hub_application_n1.delta_record_mode IN ('N', 'U', 'D', 'A')",
            'delta': {
                'table': 'v2969_hub_application_n1',
                'schema_name': 'rawd_dlt',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Delta'}
            },
            'target': {
                'resource_cd': 'ceh.rdv.hub_application',
                'table': 'hub_application_v2',
                'schema_name': 'rdv',
                '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
            },
            'status': {
                'start_dttm': '2022-10-12T11:48:25.685804',
                'is_active': True,
                'is_committed': False,
                'finish_dttm': None,
                'is_processed': None,
                'is_undone': None,
                '__faust': {'ns': 'lib.kafka.models.ceh_models.OperationStatus'}
            },
            'origin': {
                'algorithm_uid': '20.ZI6.2',
                'engine': 'airflow',
                'author': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_run_id': 'trig__2022-10-12 11:45:59.735283+00:00',
                'airflow_dag_id': 'wf_app_mscl_ods_rdv_application_lnk_v2',
                'airflow_task_id': 'reg_delta_hub_application',
                'airflow_run_uid': 'vwqju6hzqrbe5daco6xmzn'
            },
            'sources': None,
            'source': {
                'conf': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                        'ods.MSCL_SOH.H2_APPLICATION_1': {
                            'wf_max_date_from': '1899-01-01',
                            'wf_max_date_to': '5999-12-31'
                        },
                    },
                    'common': {'cftm_actual_dttm': '5999-12-31'}
                },
                'results': {
                    'by_src': {
                        'ods.MSCL_SOH.H2_APPLICATION': {'ods_max_date': 500}},
                    'common': {}}
            },
            'algos': None,
            'operation_id': None,
            'type': 'delta',
        }
    ],
    origin=None,
    sources=None,
    targets=[
        {
            'resource_cd': 'ceh.rdv.hub_application',
            'table': 'hub_application_v2',
            'schema_name': None,
            '__faust': {'ns': 'lib.kafka.models.ceh_models.Target'}
        }
    ]
)
# endregion version


def test_init():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV,
    )
    assert isinstance(dr_splitter, DeltaSplitterBySchema)


def test_default_instance_repl_schema_name_toggle_off():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.REPL_SCHEMA_NAME_TOGGLE = False
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_instance_not_set_repl_schema_name_toggle_on():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = ''
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_default_instance_repl_schema_name_toggle_on():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV,
    )
    assert dr_splitter.version_for_current_instance() is False


# region schema in query
# region default_instance (DR)
def test_dr_instance_repl_schema_name_none_all_toggle_on():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_EMPTY_DELTA_EMPTY_QUERY,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_dr_instance_dm_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_DM,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_dr_instance_rdv_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_dr_instance_idl_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_IDL,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_dr_instance_rawd_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RAWD,
    )
    assert dr_splitter.version_for_current_instance() is False
# endregion


# region rdv_instance
def test_rdv_instance_repl_schema_name_none_all_toggle_on():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_EMPTY_DELTA_EMPTY_QUERY,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rdv_instance_dm_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_DM,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rdv_instance_rdv_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_rdv_instance_idl_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_IDL,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rdv_instance_rawd_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RAWD,
    )
    assert dr_splitter.version_for_current_instance() is False
# endregion


# region idl_instance
def test_idl_instance_repl_schema_name_none_all_toggle_on():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_EMPTY_DELTA_EMPTY_QUERY,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_idl_instance_dm_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_DM,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_idl_instance_rdv_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_idl_instance_idl_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_IDL,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_idl_instance_rawd_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RAWD,
    )
    assert dr_splitter.version_for_current_instance() is False
# endregion


# region rawd_instance
def test_rawd_instance_repl_schema_name_none_all_toggle_on():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_EMPTY_DELTA_EMPTY_QUERY,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rawd_instance_dm_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_DM,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rawd_instance_rdv_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rawd_instance_idl_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_IDL,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rawd_instance_rawd_schema_in_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RAWD,
    )
    assert dr_splitter.version_for_current_instance() is True
# endregion
# endregion


# region schema in operation
# region default_instance (DR)
def test_dr_instance_repl_schema_name_none_query_toggle_on():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_EMPTY_QUERY_DM_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_dr_instance_dm_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_DM_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_dr_instance_rdv_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_dr_instance_idl_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_IDL_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_dr_instance_rawd_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'DR'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'DR'
    conf.REPL_DENY_SCHEMA_NAMES = 'rdv,idl,rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RAWD_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False
# endregion


# region rdv_instance
def test_rdv_instance_dm_schema_in_operation_none_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_EMPTY_QUERY_DM_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rdv_instance_dm_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_DM_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rdv_instance_rdv_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_rdv_instance_idl_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_IDL_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rdv_instance_rawd_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RDV'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rdv'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RAWD_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False
# endregion


# region idl_instance
def test_idl_instance_dm_schema_in_operation_none_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_EMPTY_QUERY_DM_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_idl_instance_dm_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_DM_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_idl_instance_rdv_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_idl_instance_idl_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_IDL_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is True


def test_idl_instance_rawd_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'IDL'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'idl'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RAWD_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False
# endregion


# region rawd_instance
def test_rawd_instance_dm_schema_in_operation_none_query():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_EMPTY_QUERY_DM_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rawd_instance_dm_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_DM_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rawd_instance_rdv_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RDV_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rawd_instance_idl_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_IDL_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is False


def test_rawd_instance_rawd_schema_in_operation():
    from delta_manager.config import conf
    logger = logging
    conf = conf
    conf.ID_APP = 'RAWD'
    conf.REPL_SCHEMA_NAME_TOGGLE = True
    conf.REPL_SCHEMA_NAMES = 'rawd'
    dr_splitter = DeltaSplitterBySchema(
        logger=logger,
        conf=conf,
        version=VERSION_0_6_RAWD_SCHEMA_IN_OPERATION,
    )
    assert dr_splitter.version_for_current_instance() is True
# endregion
# endregion
