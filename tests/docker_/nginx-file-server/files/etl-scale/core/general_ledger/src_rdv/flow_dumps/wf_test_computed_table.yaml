type: WORK_FLOW
targets:
  - short_name: tgt_alias_1
    resource_cd: test.resource.tgt.1
    table: table_1
    schema: rdv
sources:
  - short_name: src_alias_1
    type: DB_TABLE
    resource_cd: test.resource.src.1
  - short_name: src_alias_2
    type: DB_TABLE
    resource_cd: test.resource.src.2
  - short_name: src_alias_3
    type: COMPUTED_TABLE
    sub_sources_map:
      source1: src_alias_1
      source2: src_alias_2
mappings:
  marts:
    - algorithm_uid: ALGORITHM_1
      target: tgt_alias_1
      source: src_alias_3
    - algorithm_uid: ALGORITHM_2
      target: tgt_alias_1
      source: src_alias_1
