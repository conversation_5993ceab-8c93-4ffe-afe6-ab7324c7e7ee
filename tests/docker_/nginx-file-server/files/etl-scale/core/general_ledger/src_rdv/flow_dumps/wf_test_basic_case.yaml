targets:
  - short_name: tgt_alias_1
    resource_cd: test.resource.tgt.1
    table: table_1
    schema: rdv
  - short_name: tgt_alias_2
    resource_cd: test.resource.tgt.2
    table: table_2
    schema: rdv
  - short_name: tgt_alias_3
    resource_cd: test.resource.tgt.3
    table: table_3
    schema: rdv
  - short_name: tgt_alias_4
    resource_cd: test.resource.tgt.4
    table: table_4
    schema: rdv
sources:
  - short_name: src_alias_1
    type: DB_TABLE
    resource_cd: test.resource.src.1
  - short_name: src_alias_2
    type: DB_TABLE
    resource_cd: test.resource.src.2
mappings:
  marts:
    - algorithm_uid: ALGORITHM_1
      target: tgt_alias_1
      source: src_alias_1
  references:
    - algorithm_uid: ALGORITHM_2
      target: tgt_alias_2
      source: src_alias_2
  hub_satellites:
    - algorithm_uid: ALGORITHM_1
      target: tgt_alias_1
      source: src_alias_2
  link_satellites:
    - algorithm_uid: ALGORITHM_2
      target: tgt_alias_1
      source: src_alias_2
