targets:
  - short_name: tgt_mart
    resource_cd: test.resource.tgt.mart
    table: table_mart
    schema: rdv
  - short_name: hub_entity_dynamic
    resource_cd: test.resource.hub.entity_dynamic
    table: hub_entity_dynamic
    schema: rdv
sources:
  - short_name: src_data
    type: DB_TABLE
    resource_cd: test.resource.src.data
mappings:
  marts:
    - algorithm_uid: ALGORITHM_DYNAMIC_BK_TEST
      target: tgt_mart
      source: src_data
      hub_map:
        - target: hub_entity_dynamic
          dynamic_business_key_schema:
            map_bk_name: MAP-rdv-test-dynamic-bk
            use_default_bk: false
