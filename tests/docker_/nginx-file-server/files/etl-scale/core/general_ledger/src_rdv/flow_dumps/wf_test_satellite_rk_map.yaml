targets:
  - short_name: hub_entity1
    resource_cd: test.resource.hub.entity1
    table: hub_entity1
    schema: rdv
  - short_name: hsat_entity1
    resource_cd: test.resource.hsat.entity1
    table: hsat_entity1
    schema: rdv
  - short_name: lnk_entity1
    resource_cd: test.resource.lnk.entity1
    table: lnk_entity1
    schema: rdv
  - short_name: lsat_entity1
    resource_cd: test.resource.lsat.entity1
    table: lsat_entity1
    schema: rdv
sources:
  - short_name: src_data
    type: DB_TABLE
    resource_cd: test.resource.src.data
mappings:
  hub_satellites:
    - target: hsat_entity1
      source: src_data
      algorithm_uid: ALGORITHM_SAT_TEST_1
      delta_mode: new
      field_map:
        field1:
          type: column
          value: field1
      rk_map:
        target: hub_entity1
        business_key_schema: BK-key-schema-entity-1-TEST
  link_satellites:
    - target: lsat_entity1
      source: src_data
      algorithm_uid: ALGORITHM_SAT_TEST_2
      delta_mode: new
      field_map:
        field2:
          type: column
          value: field2
      rk_map:
        target: lnk_entity1
        rk_map:
          - rk_field: entity_rk
            target: hub_entity1
            business_key_schema: BK-key-schema-entity-1-TEST
