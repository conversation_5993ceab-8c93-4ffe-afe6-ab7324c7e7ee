<source>
  @type tail
  tag airflow.task
  refresh_interval 3
  read_from_head true
#  reserve_data true
  <parse>
    @type json
  </parse>
  pos_file_compaction_interval 50h
  path /fluentd/log/*.json
  pos_file /fluentd/log/airflow.task.files.task.pos
  path_key fullpath
</source>


<filter airflow.task>
  @type record_transformer
  enable_ruby true
  <record>
    from-type "task"
    ident "airflow-worker"
#   from ${record["fullpath"].sub(/^.*task-.*hOsB-/, '').sub(/-hOSe-.*$/, '')}-docker
#    instance ${record["fullpath"].sub(/^.*\/airflow_/, '').sub(/\/.*$/, '')}

    job_type ${record["type"]}
    job_id $record["job"]["id"]}
    logger_type ${record["job_logger"] ? "job_logger" : "ordinary"}

    execution_date ${record["job"]["execution_date"]}
    start_date ${record["job"]["start_date"]}
    run_id ${record["job"]["run_id"]}
    run_type ${record["job"]["run_type"]}
    tags ${record["job"]["tags"]}
    conf ${record["job"]["conf"]}
    parameters ${record["job"]["parameters"]}
    state ${record["job"]["state"]["state"]}
    reason ${record["job"]["state"]["reason"]}

    step_id ${record["step"]["id"]} 
    status ${record["step"]["status"]}  
    step_conf ${record["step"]["conf"]} 
    step_start_date ${record["step"]["start_date"]} 
    step_end_date ${record["step"]["end_date"]} 
    duration ${record["step"]["duration"]} 
    error ${record["step"]["error"]} 
    task_group ${record["step"]["task_group"]} 
    trigger_rule ${record["step"]["trigger_rule"]} 
    pool ${record["step"]["pool"]} 


  </record>
  remove_keys fullpath
</filter>


<match airflow.task>
  @type rewrite_tag_filter
  <rule>
    key job_type
    pattern (^.+$)
    tag $1
  </rule>
  <rule>
    key logger_type
    pattern (^.+$)
    tag $1
  </rule>
</match>


<match job>
  @type sql
  host pg_ceh
  port 5432
  database ceh
  adapter postgresql
  username default
  password default

  <table>
    table job_log
    column_mapping 'job_id:job_id,execution_date:execution_date,start_date:start_date,run_id:run_id,run_type:run_type,tags:tags,conf:conf,parameters:parameters,state:state,reason:reason'
  </table>
</match>


<match step>
  @type sql
  host pg_ceh
  port 5432
  database ceh
  adapter postgresql
  username default
  password default

  <table>
    table step_log
    column_mapping 'step_id:step_id,status:status,step_conf:conf,step_start_date:start_date,step_end_date:end_date,duration:duration,error:error,task_group:task_group,trigger_rule:trigger_rule,pool:pool,job_id:job_id,run_id:job_run_id,execution_date:job_execution_date'
  </table>
</match>


<match ordinary>
  type_name access_log
  @type elasticsearch
  suppress_type_name true
  logstash_format true
  logstash_prefix fluentd
  logstash_dateformat %Y%m%d
  request_timeout 60s
  hosts elasticsearch
  slow_flush_log_threshold 120
  port 9200
  bulk_message_request_threshold 2MB
  scheme https
  user admin
  password admin
  ssl_verify false
  <buffer>
    @type file
    path /fluentd/ordinary_buf
    flush_thread_count 5
    flush_interval 1s
    chunk_limit_size 16M
    queue_limit_length 96
  </buffer>
  reconnect_on_error true # defaults to false
  reload_on_failure true # defaults to false
  reload_connections false # defaults to true
</match>


<match job_logger>
  type_name access_log
  @type elasticsearch
  suppress_type_name true
  logstash_format true
  logstash_prefix job_logger
  logstash_dateformat %Y%m%d
  request_timeout 60s
  hosts elasticsearch
  slow_flush_log_threshold 120
  port 9200
  bulk_message_request_threshold 2MB
  scheme https
  user admin
  password admin
  ssl_verify false
  <buffer>
    @type file
    path /fluentd/job_logger_buf
    flush_thread_count 5
    flush_interval 1s
    chunk_limit_size 16M
    queue_limit_length 96
  </buffer>
  reconnect_on_error true # defaults to false
  reload_on_failure true # defaults to false
  reload_connections false # defaults to true
</match>


