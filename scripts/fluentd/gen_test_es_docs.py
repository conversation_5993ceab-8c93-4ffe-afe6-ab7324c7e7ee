import json
from datetime import datetime, timedelta
from elasticsearch import Elasticsearch
from jinja2 import Template

ES_USER = 'user'
ES_PASSWORD = 'showmelog'
ES_INDEX = 'mirhd2'


VERSION_RANGE = range(1000, 1500)

es = Elasticsearch(
    'https://d5dtpl-edc001lk.corp.dev.vtb:9200/',
    http_auth=(ES_USER, ES_PASSWORD),
    # use_ssl=True,
    verify_certs=False
)

template = Template("""{
    "id": "flow_id_{{ param }}_flow_exec_id_{{ param }}_{{ param }}", 
    "wf_status": {
        "flow_name": "flow_name_{{ param }}", 
        "flow_id": "flow_id_{{ param }}", 
        "flow_exec_id": "flow_exec_id_{{ param }}", 
        "src_sys_name": "src_sys_name_{{ param }}", 
        "src_data_sys_name": "src_data_sys_name_{{ param }}", 
        "creation_dttm": "2022-05-16T16:34:51.716020", 
        "update_dttm": "2022-05-16T16:34:51.716030", 
        "flow_status": "flow_status_{{ param }}", 
        "flow_type": "flow_type_{{ param }}", 
        "parent_flow_name": "parent_flow_name_{{ param }}", 
        "parent_flow_id": "parent_flow_id_{{ param }}", 
        "parent_exec_id": "parent_exec_id_{{ param }}"
    }, 
    "version_status": {
        "version_id": "{{ param }}", 
        "ceh_cluster_id": "ceh_cluster_id{{ param }}", 
        "version_status": 1, 
        "creation_dttm": "2022-05-16T16:34:51.716043", 
        "update_dttm": "2022-05-16T16:34:51.716044"
    }, "uow_version": {
        "uow_id": "flow_id_{{ param }}_flow_exec_id_{{ param }}_{{ param }}", 
        "version_id": "version_id_{{ param }}", 
        "flow_name": "flow_name_{{ param }}", 
        "flow_id": "flow_id_{{ param }}", 
        "flow_exec_id": "flow_exec_id_{{ param }}", 
        "creation_dttm": "2022-05-16T16:34:51.716052", 
        "update_dttm": "2022-05-16T16:34:51.716053"
    }, 
    "insert_dttm": "{{ timestamp }}", 
    "resources": [
        {
            "src_system_id": "src_system_id_1_{{ param }}", 
            "src_resource_id": "src_resource_id_1_{{ param }}", 
            "src_data_frame_start": "2022-05-16T16:34:51.716059", 
            "src_data_frame_finish": "2022-05-16T16:34:51.716060", 
            "src_selection_start": "src_selection_start_1_{{ param }}", 
            "src_selection_end": "src_selection_end_1_{{ param }}", 
            "dst_system_id": "dst_system_id_1_{{ param }}", 
            "dst_resource_id": "dst_resource_id_1_{{ param }}"
        }, 
        {
            "src_system_id": "src_system_id_2_{{ param }}", 
            "src_resource_id": "src_resource_id_2_{{ param }}", 
            "src_data_frame_start": "2022-05-16T16:34:51.716065", 
            "src_data_frame_finish": "2022-05-16T16:34:51.716066", 
            "src_selection_start": "src_selection_start_2_{{ param }}", 
            "src_selection_end": "src_selection_end_2_{{ param }}", 
            "dst_system_id": "dst_system_id_2_{{ param }}", 
            "dst_resource_id": "dst_resource_id_2_{{ param }}"
        }
    ]
}""")

# for version in VERSION_RANGE:
#     timestamp = f'{str(datetime.now() + timedelta(minutes=15*(version + 1 - min(VERSION_RANGE)))).replace(" ", "T")}'
#     resp = es.index(
#         index=ES_INDEX,
#         id=Template('flow_id_{{ param }}_flow_exec_id_{{ param }}_{{ param }}').render(param=f'{version}'),
#         body=json.loads(template.render(param=f'{version}',
#                                         timestamp=timestamp))
#     )
#     print(resp['result'])

version = 4004
timestamp = f'2022-10-03T18:07:00.000000'
resp = es.index(
    index=ES_INDEX,
    id=Template('flow_id_{{ param }}_flow_exec_id_{{ param }}_{{ param }}').render(param=f'{version}'),
    body=json.loads(template.render(param=f'{version}',
                                    timestamp=timestamp))
)
print(resp['result'])