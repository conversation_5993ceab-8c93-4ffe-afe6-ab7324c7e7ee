from dwh_garbage_collector.config import conf
from lib.logging.utils import get_logger
from gino.ext.starlette import Gino  # pylint: disable=import-error


logger = get_logger()


pg_pool = Gino(
    dsn=conf.DB_CONN_STR,
    pool_min_size=conf.DB_POOL_MIN_SIZE,
    pool_max_size=conf.DB_POOL_MAX_SIZE,
    echo=conf.DB_ECHO,
    # ssl=conf.DB_SSL,
    use_connection_for_request=conf.DB_USE_CONNECTION_FOR_REQUEST,
    retry_limit=conf.DB_RETRY_LIMIT,
    retry_interval=conf.DB_RETRY_INTERVAL,
)


async def init_db():
    logger.info('Инициализация Gino клиента postgres')

    await pg_pool.set_bind(
        conf.DB_CONN_STR,
        server_settings={
            'application_name': conf.DB_APP_NAME,
        },
    )

    logger.info('Инициализация Gino клиента postgres - Успешно')

    return pg_pool

