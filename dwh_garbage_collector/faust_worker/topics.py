from dwh_garbage_collector.faust_worker import get_faust_app
from dwh_garbage_collector.config import conf
from lib.kafka.models.common import ServiceStatus
from lib.kafka.models.dwh_garbage_collector import ObjectDeleteStatus

faust_app = get_faust_app()


# Очередь команд - топик на команды для работы с таблицами очередей
_command_queue_g = faust_app.topic(
    f'srv_dwh_garbage_collector_cmdqueue_g_v1_{conf.TOPIC_CNT}',
    internal=True,
    key_type=str,
)

# Очередь команд - топик на команды для работы с таблицами очередей
_command_queue = faust_app.topic(
    f'srv_dwh_garbage_collector_cmdqueue_local_{conf.ID_APP}_{conf.TOPIC_CNT}',
    internal=True,
    key_type=str,
)

# Очередь команд - топик на команды для работы с таблицами c одной партицией
_single_partition_command_queue = faust_app.topic(
    f'srv_dwh_garbage_collector_single_partition_cmdqueue_{conf.ID_APP}_{conf.TOPIC_CNT}',
    internal=True,
    partitions=1,
    key_type=str,
)

_service_status = faust_app.topic(
    f'srv_dwh_garbage_collector_service_status_{conf.ID_APP}_{conf.TOPIC_CNT}',
    key_type=str,
    value_type=ServiceStatus,
    internal=True,
)

# Обновление статуса удаления
_delete_status = faust_app.topic(
    f'srv_dwh_garbage_collector_delete_status_{conf.ID_APP}_{conf.TOPIC_CNT}',
    key_type=str,
    value_type=ObjectDeleteStatus,
    internal=True
)

# Запись в бд мониторинга
_logs_e = faust_app.topic(
    f'srv_tech_etl_log_{conf.TOPIC_CNT}',
    internal=True,
    key_type=str,
)

