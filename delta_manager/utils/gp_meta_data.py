from dwh_services_utils.schemas.ceh_provider.v1_0.schemas import TableCEH
from sqlalchemy import column, table as sa_table, Table
from lib.logging.custom_sink import create_log_message
from lib.logging.utils import get_logger
from delta_manager.config import conf
from time import time


logger = get_logger()


class GPMetaData:
    def __init__(self, gino_engine, datasets=None):
        self.gino_engine = gino_engine
        self.datasets = datasets

    async def get_model(self, table: TableCEH) -> Table:
        try:
            columns = await self.get_columns(table.schema_db, table.name)
            sa_columns = [column(c) for c in columns]
            return sa_table(table.name, *sa_columns, schema=table.schema_db)
        except Exception as exc:
            message = create_log_message(
                message=f"Ошибка запроса колонок {table.schema_db}.{table.name}: {exc}",
            )
            logger.error(message)
            raise

    async def get_columns(self, schema: str, table: str) -> list:
        if conf.IS_DATASETS_FROM_RES and self.datasets:
            cols = self.get_columns_from_resource(schema, table)
            if cols:
                logger.info(f'Колонки {schema}.{table} получены из ресурса')
                return cols
            logger.warning(f'Не удалось получить список колонок из ресурса для {schema}.{table}')
        return await self.get_columns_from_db(schema, table)

    def get_columns_from_resource(self, schema: str, table: str) -> list:
        try:
            dataset = [d for d in self.datasets if d.schema_name == schema and d.name == table]
            if not dataset:
                return []
            return [c.name for c in dataset[0].columns]
        except Exception as e:
            logger.error(f'Во время получения колонок для {schema}.{table} из ресурса, была получена ошибка: {e}')

    async def get_columns_from_db(self, schema: str, table: str) -> list:
        start_time = time()
        async with self.gino_engine.acquire() as conn:
            logger.info(f'Запрос метаданных {schema}.{table}')
            get_oid_sql = self.gino_engine.text(
                "SELECT c.oid\r\n"
                "FROM pg_catalog.pg_class c\r\n"
                "LEFT JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace\r\n"
                "WHERE (n.nspname = :sch) AND c.relname = :tbl AND c.relkind in ('r', 'v', 'm', 'f', 'p')"
            )
            rows = await conn.first(get_oid_sql, sch=schema, tbl=table)
            if not rows:
                logger.warning(f'Объект {schema}.{table} не найден в БД')
                return []
            oid = rows[0]
            sql = self.gino_engine.text(
                "SELECT a.attname\n"
                "FROM pg_catalog.pg_attribute a\n"
                "WHERE a.attrelid = :oid\n"
                "AND a.attnum > 0 AND NOT a.attisdropped\n"
                "ORDER BY a.attnum\n"
            )
            rows = await conn.all(sql, oid=oid)
            rows = [r[0] for r in rows]
            logger.info(f'Для объекта {schema}.{table} получено {len(rows)} колонок. '
                        f'Запросы к pg_catalog заняли {time() - start_time} сек')
            return rows


if __name__ == '__main__':
    from gino.ext.starlette import Gino  # pylint: disable=import-error
    import asyncio

    # if conf.S3_USE:
    pg_pool = Gino(
        dsn='postgresql://default:default@127.0.0.1:5433/ceh',
        pool_min_size=conf.DB_POOL_MIN_SIZE,
        pool_max_size=conf.DB_POOL_MAX_SIZE,
        echo=True,
        # ssl=conf.DB_SSL,
        use_connection_for_request=conf.DB_USE_CONNECTION_FOR_REQUEST,
        retry_limit=conf.DB_RETRY_LIMIT,
        retry_interval=conf.DB_RETRY_INTERVAL,
    )

    async def run():
        await pg_pool.set_bind(
            'postgresql://default:default@127.0.0.1:5433/ceh',
            server_settings={
                'application_name': conf.DB_APP_NAME,
            },
        )
        meta = GPMetaData(pg_pool)
        cols = await meta.get_columns('information_schema', 'tables')
        print(cols)
        model = await meta.get_model(TableCEH(schema_db='information_schema', name='tables'))
        print(model.columns)
        print(model.name)

    asyncio.run(run())
