import datetime
import json
from time import sleep


def test_resource(ceh_client_v0_5, ceh_client_v0_6, r_0_3):
    r5 = ceh_client_v0_5.create_resource(
        r_0_3
    )
    assert r5.resource_cd == r_0_3['resource_cd']
    r6 = ceh_client_v0_6.get_resource(resource_cd=r_0_3['resource_cd'])
    assert r6.resource_cd == r_0_3['resource_cd']
    r5_dict = dict(r5)
    r5_dict['metrics'] = None
    assert r5_dict == dict(r6)


def test_set_state(ceh_client_v0_5, ceh_client_v0_6, tx_client, r_0_3, operation_0_5):
    try:
        r3 = ceh_client_v0_5.create_resource(
            r_0_3
        )
    except Exception as ex:
        pass
    tx = tx_client.create_transaction()
    version_id = ceh_client_v0_5.lock(
        resource_codes=[r_0_3['resource_cd']],
        tx_uid=str(tx.tx_uid),
        attempts=3,
        attempt_timeout=3,
    )
    operation_0_5["statement"] = f"insert into table_1 values ('{r_0_3['resource_cd']}_{tx.tx_uid}')"
    s5 = ceh_client_v0_5.update_resource_state(
        resource_cd=r_0_3['resource_cd'],
        tx_uid=str(tx.tx_uid),
        operation=operation_0_5,
    )
    s5 = ceh_client_v0_5.get_resource_state(
        resource_cd=r_0_3['resource_cd']
    )
    s6 = ceh_client_v0_6.get_resource_state(
        resource_cd=r_0_3['resource_cd']
    )
    s6_json = json.dumps(
        s6,
        default=lambda o: (str(o) if isinstance(o, datetime.datetime) else dict(o))
    )
    s5_json = json.dumps(
        s5,
        default=lambda o: (str(o) if isinstance(o, datetime.datetime) else dict(o))
    )
    s6_dict = json.loads(s6_json)
    s5_dict = json.loads(s5_json)
    assert s5_dict['version']['operations'][0]['algos'] == {
        'algo3': {
            '__root__': {
                'conf': {'max_processed_dt': '2021-02-17T12:29:24.615Z'},
                'results': {
                    'by_src': {
                        'ods.soh.z#account_document': {
                            'wf_max_processed_dt': '2021-02-17T12:29:24.615Z',
                            'wf_min_processed_dt': '2021-02-10T10:00:00.000Z'
                        }
                    }
                }
            }
        }
    }
    assert s6_dict['version']['operations'][0]['source'] is None
    # ToDo: Разобраться с схемой
    # assert s5_dict['version']['operations'][0]['target']['schema_name'] == 'test_schema'
    assert s6_dict['last_sources'] == {
        "algo3": {
            "__root__": {
                "conf": {
                    "max_processed_dt": "2021-02-17T12:29:24.615Z"
                },
                "results": {
                    "by_src": {
                        "ods.soh.z#account_document": {
                            "wf_min_processed_dt": "2021-02-10T10:00:00.000Z",
                            "wf_max_processed_dt": "2021-02-17T12:29:24.615Z"
                        }
                    }
                }
            }
        }
    }
    s5_dict['version']['operations'][0].pop('algos')
    s6_dict['version']['operations'][0].pop('source')
    s6_dict['version']['operations'][0]['origin'].pop('algorithm_uid')
    s6_dict.pop('last_sources')
    assert s6_dict == s5_dict
    sleep(3)
    tx_client.commit(tx.tx_uid, '')
    sleep(7)
    state = ceh_client_v0_5.get_resource_state(resource_cd=r_0_3['resource_cd'])
    tx = tx_client.get_transaction_info(tx.tx_uid)

    s6 = ceh_client_v0_6.get_resource_state(
        resource_cd=r_0_3['resource_cd']
    )
    s5 = ceh_client_v0_5.get_resource_state(
        resource_cd=r_0_3['resource_cd']
    )
    s6_json = json.dumps(
        s6,
        default=lambda o: (str(o) if isinstance(o, datetime.datetime) else dict(o))
    )
    s5_json = json.dumps(
        s5,
        default=lambda o: (str(o) if isinstance(o, datetime.datetime) else dict(o))
    )
    s6_dict = json.loads(s6_json)
    s5_dict = json.loads(s5_json)
    assert s5_dict['version']['operations'][0]['algos'] == {
        'algo3': {
            '__root__': {
                'conf': {'max_processed_dt': '2021-02-17T12:29:24.615Z'},
                'results': {
                    'by_src': {
                        'ods.soh.z#account_document': {
                            'wf_max_processed_dt': '2021-02-17T12:29:24.615Z',
                            'wf_min_processed_dt': '2021-02-10T10:00:00.000Z'
                        }
                    }
                }
            }
        }
    }
    assert s6_dict['version']['operations'][0]['source'] is None
    # ToDo: Разобраться с схемой
    # assert s5_dict['version']['operations'][0]['target']['schema_name'] == 'test_schema'
    assert s6_dict['last_sources'] == {
        "algo3": {
            "__root__": {
                "conf": {
                    "max_processed_dt": "2021-02-17T12:29:24.615Z"
                },
                "results": {
                    "by_src": {
                        "ods.soh.z#account_document": {
                            "wf_min_processed_dt": "2021-02-10T10:00:00.000Z",
                            "wf_max_processed_dt": "2021-02-17T12:29:24.615Z"
                        }
                    }
                }
            }
        }
    }
    s5_dict['version']['operations'][0].pop('algos')
    s6_dict['version']['operations'][0].pop('source')
    s6_dict['version']['operations'][0]['origin'].pop('algorithm_uid')
    s6_dict.pop('last_sources')
    assert s6_dict == s5_dict
