import http.client
from time import sleep

import sqlalchemy.sql


def test_force_unlock_resource(
        resource_1,
        rdv_table,
        tx_client,
        ceh_client_v0_5,
        operation_2,
        db_client,
        ceh_url,
):
    tx = tx_client.create_transaction()
    version_id = ceh_client_v0_5.lock(
        resource_codes=[resource_1.resource_cd],
        tx_uid=str(tx.tx_uid),
        attempts=3,
        attempt_timeout=3,
    )
    operation_2["statement"] = f"insert into table_1 values ('{resource_1.resource_cd}_{tx.tx_uid}')"
    s1 = ceh_client_v0_5.update_resource_state(
        resource_cd=resource_1.resource_cd,
        tx_uid=str(tx.tx_uid),
        operation=operation_2,
    )
    conn = http.client.HTTPConnection(ceh_url[7:-5], int(ceh_url[-4:]))
    conn.request('PUT', f"/api/0.3/resources/{resource_1.resource_cd}/unlock")
    resp = conn.getresponse()
    sleep(7)
    state = ceh_client_v0_5.get_resource_state(resource_cd=resource_1.resource_cd)
    tx = tx_client.get_transaction_info(tx.tx_uid)

    assert state.is_locked is False
    assert state.version.version_id == version_id
    query = sqlalchemy.sql.text(f"select * from etl.version where tx_uid = '{tx.tx_uid}'")
    etl_ver_row = db_client.execute(query).fetchone()
    assert etl_ver_row['active_flg'] is False
    assert etl_ver_row['commited_flg'] is False
    assert etl_ver_row['processed_flg'] is True
    query = sqlalchemy.sql.text(f"select count(*) from table_1 where field_1 = '{resource_1.resource_cd}_{tx.tx_uid}'")
    row_count = db_client.scalar(query)
    assert row_count == 0


def test_deactivate_version(
        resource_1,
        rdv_table,
        tx_client,
        ceh_client_v0_5,
        operation_2,
        db_client,
        ceh_url,
):
    tx = tx_client.create_transaction()
    version_id = ceh_client_v0_5.lock(
        resource_codes=[resource_1.resource_cd],
        tx_uid=str(tx.tx_uid),
        attempts=3,
        attempt_timeout=3,
    )
    operation_2["statement"] = f"insert into table_1 values ('{resource_1.resource_cd}_{tx.tx_uid}')"
    s1 = ceh_client_v0_5.update_resource_state(
        resource_cd=resource_1.resource_cd,
        tx_uid=str(tx.tx_uid),
        operation=operation_2,
    )
    conn = http.client.HTTPConnection(ceh_url[7:-5], int(ceh_url[-4:]))
    conn.request('PUT', f"/api/0.3/resources/{version_id}/deactivate")
    resp = conn.getresponse()
    sleep(7)
    # state = ceh_client_v0_5.get_resource_state(resource_cd=resource1.resource_cd)
    # tx = tx_client.get_transaction_info(tx.tx_uid)

    # assert state.is_locked is False
    # assert state.version.version_id == version_id
    query = sqlalchemy.sql.text(f"select * from etl.version where tx_uid = '{tx.tx_uid}'")
    etl_ver_row = db_client.execute(query).fetchone()
    assert etl_ver_row['active_flg'] is False
    assert etl_ver_row['commited_flg'] is False
    assert etl_ver_row['processed_flg'] is True
    query = sqlalchemy.sql.text(f"select count(*) from table_1 where field_1 = '{resource_1.resource_cd}_{tx.tx_uid}'")
    row_count = db_client.scalar(query)
    assert row_count == 0
