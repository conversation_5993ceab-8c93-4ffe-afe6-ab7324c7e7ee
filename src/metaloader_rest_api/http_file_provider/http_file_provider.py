from ssl import create_default_context
from typing import Iterator, Optional

from httpx import Client, HTTPError, HTTPTransport, Response
from structlog.stdlib import Bo<PERSON><PERSON>og<PERSON>, get_logger

from metaloader_rest_api.file_provider.file_provider import (
    FileProvider,
    FileProviderCheckError,
)
from metaloader_rest_api.helpers import ClearErrorIfNotInitialized

_DEFAULT_LOGGER = get_logger(__name__)


class HttpFileProvider(FileProvider):
    default_timeout = 5.0
    default_retries = 3

    _skip_statuses = {404, 410}

    _client = ClearErrorIfNotInitialized()

    def __init__(
        self,
        url: str,
        timeout: float = default_timeout,
        retries: int = default_retries,
        chunk_size: Optional[int] = None,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        self._url = url
        self._timeout = timeout
        self._retries = retries
        self._chunk_size = chunk_size

        self._log = log.bind(actor="http_file_provider", url=url)
        self._log.info(
            "init",
            timeout=timeout,
            retries=retries,
            chunk_size=chunk_size,
        )

    def __enter__(self):
        self._log.info("begin")

        transport = HTTPTransport(
            verify=create_default_context(),
            retries=self._retries,
        )
        self._client = Client(
            transport=transport,
            base_url=self._url,
            follow_redirects=True,
            timeout=self._timeout,
        )
        self._client.__enter__()

        return self

    def __exit__(
        self,
        exc_type,
        exc_value,
        traceback,
    ):
        self._log.info("end")

        if self._client:
            self._client.__exit__(exc_type, exc_value, traceback)

    def check(self, path: str = "/") -> None:
        try:
            self._client.head(path).raise_for_status()
        except HTTPError as error:
            raise FileProviderCheckError(f"URL {self._url}/{path} not found") from error

    def get(self, path: str) -> Optional[Iterator[bytes]]:
        response = self._client.get(path)
        if self._check_status(response):
            return None
        return response.iter_bytes(self._chunk_size)

    def _check_status(self, response: Response) -> bool:
        if response.status_code in self._skip_statuses:
            return True
        response.raise_for_status()
        return False
