from pathlib import Path
from typing import Iterator, Op<PERSON>, Tu<PERSON>, Union

from structlog.stdlib import <PERSON>und<PERSON>og<PERSON>, get_logger

from metaloader_rest_api.file_provider.file_provider import FileProvider

_DEFAULT_LOGGER = get_logger(__name__)


class LocalFileProvider(FileProvider):
    def __init__(
        self,
        path: Union[Path, str],
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        if isinstance(path, str):
            self._path = Path(path)
        else:
            self._path = path

        self._log = log.bind(actor="local_file_provider", path=path)
        self._log.info("init")

    def __enter__(self):
        self._log.info("begin")
        return self

    def __exit__(
        self,
        exc_type,
        exc_value,
        traceback,
    ):
        self._log.info("end")

    def check(self, path: str = "/") -> None:
        _path, exists = self._check(path)
        if not exists:
            raise FileNotFoundError(f"{_path} does not exist")

    def get(self, path: str) -> Optional[Iterator[bytes]]:
        _path, exists = self._check(path)

        if not exists:
            return None

        return (item for item in (_path.read_bytes(),))

    def _check(self, path: str) -> Tuple[Path, bool]:
        _path = self._path / path
        return _path, _path.exists()
