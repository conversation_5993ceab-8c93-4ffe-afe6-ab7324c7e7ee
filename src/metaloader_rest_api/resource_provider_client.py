import re
from enum import Enum
from ssl import create_default_context
from typing import Any, Dict, Iterator, List, Optional
from urllib.parse import urljoin

import httpx
import structlog
from httpx import Client, HTTPTransport

from metaloader_rest_api.helpers import JsonDict

logger = structlog.stdlib.get_logger(__name__)


class ResourceProviderClient:
    _DEFAULT_PAGE_SIZE = 100

    class _paths:
        resource_list = "/resources"
        resource = "/resources/{resource_cd}"

    def __init__(
        self,
        base_url: str,
        timeout: int = 30,
        retries: int = 3,
        logger: structlog.stdlib.BoundLogger = logger,
    ):
        self._logger = logger.bind(actor="resource_provider_client", url=base_url)
        self._logger.info(
            "init",
            timeout=timeout,
            retries=retries,
        )
        if not re.match(r"/api/\d+(\.\d+)?/?$", base_url):
            self._logger.warning("base_url doesn't contain API version")

        transport = HTTPTransport(
            verify=create_default_context(),
            retries=retries,
        )
        self._http = Client(
            transport=transport,
            base_url=base_url,
            headers={"Content-Type": "application/json"},
            follow_redirects=True,
            timeout=timeout,
        )

    def __enter__(self):
        self._logger.info("begin")
        self._http.__enter__()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self._http.__exit__(exc_type, exc_value, traceback)
        self._logger.info("end")

    def list_resource_cds(self) -> Iterator[str]:
        current_page = 0
        while True:
            #     response = self._http.get(
            #         url=self._paths.resource_list,
            #         params={
            #             "limit": self._DEFAULT_PAGE_SIZE,
            #             "offset": current_page * self._DEFAULT_PAGE_SIZE,
            #         },
            #     )
            #     response.raise_for_status()
            payload = response.json()
            for item in payload:
                yield item["resource_cd"]
            if len(payload) < self._DEFAULT_PAGE_SIZE:
                break
            current_page += 1

        # current_page = 0
        # while True:
        #     response = self._http.get(
        #         url=self._paths.resource_list,
        #         params={
        #             "limit": self._DEFAULT_PAGE_SIZE,
        #             "offset": current_page * self._DEFAULT_PAGE_SIZE,
        #         },
        #     )
        #     response.raise_for_status()
        #     resources = response.json()
        #     if not resources:
        #         break
        #     for resource in resources:
        #         yield resource["resource_cd"]
        #     current_page += 1
        #
        # response = self._http.get(
        #     url=self._paths.resource_list,
        #     params={
        #         "limit": 0,
        #         "offset": 0,
        #     },
        # )
        # response.raise_for_status()
        #
        # return response.json()

    def get_resource(self, resource_cd: str) -> JsonDict:
        url = self._get_url(f"/resources/{resource_cd}")

        with httpx.Client(timeout=self._timeout) as client:
            response = client.get(url, headers=self.headers)
            response.raise_for_status()

            return response.json()


def create_ceh_client(
    base_url: str,
    api_version: str = "0.6",
    auth_token: Optional[str] = None,
    timeout: int = 30,
) -> ResourceProviderClient:
    """
    Create a client for the CEH Resource Provider API.

    Args:
        base_url: The base URL of the API.
        api_version: The API version to use (default: "0.6").
        auth_token: Optional OAuth2 token for authentication.
        timeout: Request timeout in seconds (default: 30).

    Returns:
        A ResourceProviderClient configured for the CEH API.
    """
    return ResourceProviderClient(
        base_url=base_url,
        provider_type=ProviderType.CEH,
        api_version=api_version,
        auth_token=auth_token,
        timeout=timeout,
    )


def create_uni_client(
    base_url: str,
    api_version: str = "1.0",
    auth_token: Optional[str] = None,
    timeout: int = 30,
) -> ResourceProviderClient:
    """
    Create a client for the UNI Resource Provider API.

    Args:
        base_url: The base URL of the API.
        api_version: The API version to use (default: "1.0").
        auth_token: Optional OAuth2 token for authentication.
        timeout: Request timeout in seconds (default: 30).

    Returns:
        A ResourceProviderClient configured for the UNI API.
    """
    return ResourceProviderClient(
        base_url=base_url,
        provider_type=ProviderType.UNI,
        api_version=api_version,
        auth_token=auth_token,
        timeout=timeout,
    )
