import re
from datetime import date, datetime
from io import Bytes<PERSON>
from typing import List, Any, Dict

import openpyxl
from pydantic import ConfigD<PERSON>, Field, BaseModel
from pyxlsb import open_workbook
from structlog.stdlib import BoundLogger, get_logger

from metaloader_rest_api.data_model.data_model_api import LoadDataModelParams, LoadLogicalDataModelParams

_DEFAULT_LOGGER = get_logger(__name__)
logger = get_logger(__name__)

COLUMN_MAPPING: dict[str, str] = {
    'index': 'record_no',
    'subject_area': 'domain_name',
    'model_version': 'domain_model_version_num',
    'table_owner': 'schema_name',
    'table_code': 'table_name',
    'table_name': 'table_extra_name',
    'table_stereotype': 'table_data_vault_type_cd',
    'table_comment': 'table_desc',
    'table_annotation': 'table_extra_desc',
    'columns_count': 'attribute_cnt',
    'table_isShortcut': 'is_ref_flg',
    'sub_subject_area': 'sub_domain_name',
    'sub_subject_area_additional': 'extra_sub_domain_name',
    'Axon_ID_table': 'table_axon_id',
    'Axon_URL_table': 'table_axon_url',
    'table_version': 'table_model_version_from_num',
    'table_deprecated_version': 'table_model_version_to_num',
    'column_code': 'attribute_name',
    'column_name': 'attribute_extra_name',
    'data_type': 'data_type_cd',
    'domain': 'data_domain_cd',
    'mandatory': 'is_nullable_flg',
    'technical_column': 'is_metadata_flg',
    'primary_key': 'is_primary_key_flg',
    'foreign_key': 'is_foreign_key_flg',
    'parent_table_owner': 'foreign_schema_name',
    'parent_table_code': 'foreign_table_name',
    'parent_column_code': 'foreign_attribute_name',
    'column_comment': 'attribute_desc',
    'column_annotation': 'attribute_extra_desc',
    'Axon_ID': 'attribute_axon_id',
    'Axon_URL': 'attribute_axon_url',
    'column_version': 'attribute_model_version_from_num',
    'column_deprecated_version': 'attribute_model_version_to_num'
}

COLUMN_ORDER = list(COLUMN_MAPPING.values())

BOOL_COLUMNS = [
    'is_ref_flg',
    'is_nullable_flg',
    'is_metadata_flg',
    'is_primary_key_flg',
    'is_foreign_key_flg'
]

INT_COLUMNS = ['attribute_cnt', 'record_no']

DEFAULT_VALUES = {
    'table_owner': 'bdm',
    'table_isShortcut': False,
    'mandatory': False,
    'technical_column': False
}


class LoadLogicalDataModelDemoFile(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    data_layer: str
    file_name: str
    file_io: BytesIO
    as_of: date = Field(default_factory=date.today)


def extract_date(filename: str) -> str:
    patterns = [
        (r'(\d{4})_(\d{1,2})_(\d{1,2})', 'yyyy-mm-dd'),
        (r'(\d{1,2})_(\d{1,2})_(\d{4})', 'dd-mm-yyyy')
    ]

    for pattern, date_format in patterns:
        match = re.search(pattern, filename)
        if match:
            try:
                if date_format == 'yyyy-mm-dd':
                    year, month, day = map(int, match.groups())
                else:
                    day, month, year = map(int, match.groups())
                datetime(year, month, day)
                return f"{year:04d}-{month:02d}-{day:02d}"
            except ValueError:
                continue
    return str(date.today())


def convert_excel_to_csv(file_io: BytesIO, extension: str, data_layer: str) -> List[Dict]:
    if data_layer in ("bdm", "cdm"):
        header_row = 2
        data_row = 3
    elif data_layer in ("idl", "rdv", "sdm"):
        header_row = 1
        data_row = 2
    else:
        raise ValueError(f"Unsupported data layer: {data_layer}")

    if extension == 'xlsx':
        return parse_xlsx(file_io, header_row, data_row)
    elif extension == 'xlsb':
        return parse_xlsb(file_io, header_row, data_row)
    raise ValueError("Unsupported file extension")


def parse_xlsx(file_io: BytesIO, header_row: int, data_row: int) -> List[Dict]:
    file_io.seek(0)
    wb = openpyxl.load_workbook(file_io)
    sheet = wb.active

    headers = [str(cell.value) for cell in sheet[header_row]]
    results = []

    for row in sheet.iter_rows(min_row=data_row):
        record_no = row[0].row
        row_dict = {header: cell.value for header, cell in zip(headers, row)}
        row_dict["record_no"] = record_no
        processed = process_row(row_dict)
        results.append(processed)

    return results


def parse_xlsb(file_io: BytesIO, header_row: int, data_row: int) -> List[Dict]:
    file_io.seek(0)
    with open_workbook(file_io) as wb:
        with wb.get_sheet(1) as sheet:
            rows = list(sheet.rows())

            headers = [str(cell.v) for cell in rows[header_row - 1]]
            results = []

            for idx, row in enumerate(rows[data_row - 1:], start=data_row):
                row_dict = {header: cell.v for header, cell in zip(headers, row)}
                row_dict["record_no"] = idx
                processed = process_row(row_dict)
                results.append(processed)

    return results


def process_row(row_dict: Dict) -> Dict:
    for col, default in DEFAULT_VALUES.items():
        if col not in row_dict:
            row_dict[col] = default

    renamed = {COLUMN_MAPPING.get(k, k): v for k, v in row_dict.items()}

    for bool_col in BOOL_COLUMNS:
        value = renamed.get(bool_col)
        if isinstance(value, (int, float)):
            renamed[bool_col] = bool(value)
        elif isinstance(value, str):
            renamed[bool_col] = value.lower() in {'true', '1', 'yes'}
        else:
            renamed[bool_col] = False

    if 'is_nullable_flg' in renamed:
        renamed['is_nullable_flg'] = not renamed['is_nullable_flg']

    foreign_schema = renamed.get('foreign_schema_name')
    foreign_table = renamed.get('foreign_table_name')

    if (not foreign_schema and foreign_table != '') and (not foreign_schema and foreign_table):
        renamed['foreign_schema_name'] = 'bdm'

    return {key: renamed.get(key, None) for key in COLUMN_ORDER}


def convert_csv_to_json(csv_data: List[Dict], data_layer: str, as_of: str) -> LoadLogicalDataModelParams:
    formatted_data = []

    for row in csv_data:
        converted = {
            field: convert_value(field, row.get(field))
            for field in COLUMN_ORDER
        }
        formatted_data.append(list(converted.values()))

    return LoadLogicalDataModelParams(
        layer=data_layer,
        columns=COLUMN_ORDER,
        rows=formatted_data,
        as_of=as_of
    )


def convert_value(field: str, value: Any) -> Any:
    if isinstance(value, str):
        value = value.strip()
        if value == '':
            return None

    if field in BOOL_COLUMNS:
        if isinstance(value, str):
            return value.lower() in ['true', '1', 'yes']
        return bool(value)

    if field in INT_COLUMNS:
        try:
            return int(value) if value not in [None, ''] else None
        except (ValueError, TypeError):
            return -1

    return value


def transform_excel_to_json(params: LoadLogicalDataModelDemoFile,
                            log: BoundLogger = _DEFAULT_LOGGER) -> LoadLogicalDataModelParams:
    log.info("transform_excel_to_json", params=params)
    log.info("begin")
    params.as_of = extract_date(params.file_name)

    csv_data = convert_excel_to_csv(
        params.file_io,
        params.file_name.split('.')[-1].lower(),
        params.data_layer.lower()
    )

    log_json = convert_csv_to_json(csv_data, params.data_layer, params.as_of)
    log.info("end")

    return log_json
