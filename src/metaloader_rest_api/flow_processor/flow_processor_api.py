from datetime import datetime
from typing import Any, List, Mapping
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy.orm import Session
from structlog.stdlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_logger

from metaloader_rest_api.common_param import (
    DbOptions,
    DeploymentInfo,
    FailsafeOptions,
    HttpOptions,
)
from metaloader_rest_api.flow_param.flow_param_processor import FlowParamProcessor
from metaloader_rest_api.flow_processor import flow_processor_int
from metaloader_rest_api.flow_processor.flow_processor_int import (
    FLOW_PROCESSORS_IGNORE_MISSING_FLOWS,
)
from metaloader_rest_api.flow_resource.flow_resource_processor import (
    FlowResourceProcessor,
)
from metaloader_rest_api.yaml.yaml_loader import (
    YAML_LOADER_DEFAULT,
    YamlLoader,
)

_DEFAULT_LOGGER = get_logger(__name__)


FLOW_PROCESSORS_DEFAULT = {
    "idl_parameters": FlowParamProcessor,
    "idl_resources": FlowResourceProcessor,
}


class ProcessFlowsParams(BaseModel):
    deployment: DeploymentInfo = DeploymentInfo()
    ceh_etl_src_url: str
    processors: List[str] = list(FLOW_PROCESSORS_DEFAULT.keys())
    yaml_loader: YamlLoader = YAML_LOADER_DEFAULT
    ignore_missing_flows: bool = FLOW_PROCESSORS_IGNORE_MISSING_FLOWS
    db_options: DbOptions = DbOptions()
    http_options: HttpOptions = HttpOptions()
    failsafe_options: FailsafeOptions = FailsafeOptions()


def process_flows(
    session: Session,
    load_id: UUID,
    effective_date: datetime,
    params: ProcessFlowsParams,
    log: BoundLogger = _DEFAULT_LOGGER,
) -> Mapping[str, Any]:
    return flow_processor_int.process_flows(
        session=session,
        load_id=load_id,
        module=params.deployment.module,
        version=params.deployment.release,
        effective_date=effective_date,  # params.deployment.effective_date
        ceh_etl_src_url=params.ceh_etl_src_url,
        flow_processors=params.processors,
        yaml_loader=params.yaml_loader,
        ignore_missing_flows=params.ignore_missing_flows,
        db_fetch_size=params.db_options.page_size,
        db_page_size=params.db_options.page_size,
        db_commit=params.db_options.commit,
        http_timeout=params.http_options.timeout,
        http_retries=params.http_options.retries,
        failsafe_threshold=params.failsafe_options.threshold,
        log=log,
    )


def registry_flow_processors() -> None:
    flow_processor_int.registry_flow_processors(FLOW_PROCESSORS_DEFAULT)
