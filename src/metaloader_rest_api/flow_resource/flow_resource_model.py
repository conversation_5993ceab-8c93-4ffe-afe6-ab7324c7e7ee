from enum import IntEnum
from typing import Callable, Optional, Tuple, TypedDict, Union


class FlowResourceType(IntEnum):
    UNDEFINED = -1
    TARGET = 1
    SOURCE = 2


class FlowResource(TypedDict):
    type: FlowResourceType
    algorithm: str
    flow_id: int
    resource_name: str
    resource_schema_name: Optional[str]
    resource_table_name: Optional[str]


def source_flow_resource(
    algorithm: str,
    flow_id: int,
    resource_name: str,
) -> FlowResource:
    return flow_resource(
        type=FlowResourceType.SOURCE,
        algorithm=algorithm,
        flow_id=flow_id,
        resource_name=resource_name,
    )


def target_flow_resource(
    algorithm: str,
    flow_id: int,
    resource_name: str,
    resource_schema_name: Optional[str] = None,
    resource_table_name: Optional[str] = None,
) -> FlowResource:
    return flow_resource(
        type=FlowResourceType.TARGET,
        algorithm=algorithm,
        flow_id=flow_id,
        resource_name=resource_name,
        resource_schema_name=resource_schema_name,
        resource_table_name=resource_table_name,
    )


def flow_resource(
    type: FlowResourceType,
    algorithm: str,
    flow_id: int,
    resource_name: str,
    resource_schema_name: Optional[str] = None,
    resource_table_name: Optional[str] = None,
) -> FlowResource:
    return FlowResource(
        type=type,
        algorithm=algorithm,
        flow_id=flow_id,
        resource_name=resource_name,
        resource_schema_name=resource_schema_name,
        resource_table_name=resource_table_name,
    )


SourceFlowResourceFactory = Callable[[str], FlowResource]
TargetFlowResourceFactory = Union[
    Callable[[str, Optional[str], Optional[str]], FlowResource],
    SourceFlowResourceFactory,
]


def flow_resource_factory(
    algorithm: str,
    flow_id: int,
    
) -> Tuple[TargetFlowResourceFactory, SourceFlowResourceFactory]:
    def source(
        resource_name: str,
    ) -> FlowResource:
        return source_flow_resource(
            algorithm=algorithm,
            flow_id=flow_id,
            resource_name=resource_name,
        )

    def target(
        resource_name: str,
        resource_schema_name: Optional[str] = None,
        resource_table_name: Optional[str] = None,
    ) -> FlowResource:
        return target_flow_resource(
            algorithm=algorithm,
            flow_id=flow_id,
            resource_name=resource_name,
            resource_schema_name=resource_schema_name,
            resource_table_name=resource_table_name,
        )

    return target, source
