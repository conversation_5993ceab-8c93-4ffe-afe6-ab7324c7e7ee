from datetime import datetime
from typing import Set, Type
from uuid import UUID

from sqlalchemy.orm import Session
from structlog.stdlib import Bound<PERSON>ogger, get_logger

from metaloader_rest_api.flow_processor.flow_processor import FlowProcessor
from metaloader_rest_api.flow_resource.flow_resource_processor_unit import (
    FlowResourceProcessorUnit,
)
from metaloader_rest_api.flow_resource.flow_resource_processor_unit_idl import (
    IdlFlowResourceProcessorUnit,
)
from metaloader_rest_api.flow_resource.flow_resource_processor_unit_rdv import (
    RdvFlowResourceProcessorUnit,
)
from metaloader_rest_api.flow_resource.flow_resource_repository import (
    FlowResourceRepository,
)
from metaloader_rest_api.http_file_provider.http_file_provider import HttpFileProvider
from metaloader_rest_api.yaml.yaml_loader import Yaml

_DEFAULT_LOGGER = get_logger(__name__)


class FlowResourceProcessor(FlowProcessor):
    _UNITS: Set[Type[FlowResourceProcessorUnit]] = {
        IdlFlowResourceProcessorUnit,
        RdvFlowResourceProcessorUnit,
    }

    def __init__(
        self,
        session: Session,
        file_provider: HttpFileProvider,
        load_id: UUID,
        module_id: int,
        version_id: int,
        effective_date: datetime,
        page_size: int = FlowProcessor.page_size,
        log: BoundLogger = _DEFAULT_LOGGER,
    ):
        super().__init__(
            session,
            file_provider,
            load_id,
            module_id,
            version_id,
            effective_date,
            page_size,
            log,
        )

        self._units = {unit(file_provider, log) for unit in self._UNITS}

        table_id = load_id.hex
        self._flow_resource_repository = FlowResourceRepository(
            session,
            module_id,
            version_id,
            effective_date,
            table_id,
            page_size,
            log,
        )

    def __enter__(self):
        super().__enter__()

        self._flow_resource_repository.__enter__()

        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self._flow_resource_repository.__exit__(exc_type, exc_value, traceback)

        return super().__exit__(exc_type, exc_value, traceback)

    def process(
        self,
        flow_id: int,
        flow_name: str,
        flow: Yaml,
    ) -> None:
        for unit in self._units:
            for flow_resource in unit.process(flow_id, flow_name, flow):
                self._flow_resource_repository.load_one(flow_resource)
