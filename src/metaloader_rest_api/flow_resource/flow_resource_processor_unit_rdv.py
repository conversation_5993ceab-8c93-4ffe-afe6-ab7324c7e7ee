import itertools
from functools import lru_cache
from typing import Callable, Iterable, Iterator, Optional, Sequence, TypedDict

from metaloader_rest_api.file_provider.file_provider import FileProvider
from metaloader_rest_api.flow_resource.flow_resource_model import (
    FlowResource,
    flow_resource_factory,
)
from metaloader_rest_api.flow_resource.flow_resource_processor_unit import (
    FlowResourceProcessorUnit,
)
from metaloader_rest_api.helpers import EMPTY_MAPPING
from metaloader_rest_api.yaml.bytes_iterator_yaml_stream import (
    get_bytes_iterator_yaml_stream,
)
from metaloader_rest_api.yaml.yaml_loader import Yaml, load_yaml


class _WorkFlowResource(TypedDict):
    """Класс сделан только для локальной аннотации содержимого ямлов"""

    resource_cd: str
    table: Optional[str]
    schema: Optional[str]


class RdvFlowResourceProcessorUnit(FlowResourceProcessorUnit):
    _DYNAMIC_BUSINESS_KEY_SCHEMA_PATH = (
        "etl-scale/core/general_ledger/src_rdv/schema/bk_map/"
    )

    def process(
        self,
        flow_id: int,
        flow_name: str,
        flow: Yaml,
    ) -> Iterator[FlowResource]:
        sources = _resolve_direct_and_indirect_resource_aliases(flow["sources"])
        # Предполагается, что в атрибуте "targets" никогда не бывает записей типа COMPUTED_TABLE
        # или других подобных типов, допускающих ссылку на несколько ресурсов
        targets = _resolve_direct_resource_aliases(flow["targets"])
        (
            marts_mappings,
            references_mappings,
            hub_satellites_mappings,
            link_satellites_mappings,
        ) = (
            flow["mappings"].get(key, ())
            for key in ("marts", "references", "hub_satellites", "link_satellites")
        )

        yield from _get_resources_from_mappings_top_level(
            flow_id,
            marts_mappings,
            references_mappings,
            hub_satellites_mappings,
            link_satellites_mappings,
            short_names_to_targets=targets,
            short_names_to_sources=sources,
        )

        yield from _get_resources_from_marts_hub_map(
            flow_id,
            marts_mappings,
            short_names_to_targets=targets,
            get_dynamic_bk_schema_names=self._get_dynamic_business_key_schema_names,
        )

        yield from _get_resources_from_satellites_rk_map(
            flow_id,
            hub_satellites_mappings,
            short_names_to_targets=targets,
        )

        yield from _get_resources_from_satellites_rk_map(
            flow_id,
            link_satellites_mappings,
            short_names_to_targets=targets,
        )

    @lru_cache
    def _get_dynamic_business_key_schema_names(
        self,
        mapping_file_name: str,
    ) -> tuple[Optional[str], Sequence[str]]:
        mapping_file_path = (
            f"{self._DYNAMIC_BUSINESS_KEY_SCHEMA_PATH}/{mapping_file_name}.yaml"
        )
        bk_map = _get_yaml_file_content(mapping_file_path, self._http_loader)
        if not bk_map:
            self._log.warning(
                "dynamic_business_key_schema_file_not_found", file=mapping_file_path
            )
            return None, ()

        default_schema = bk_map.get("default_entry", EMPTY_MAPPING).get("bk_schema")
        schemas = tuple(
            bk_schema  # noqa
            for e in bk_map.get("map_entries", ())
            if e and (entry := e.get("entry")) and (bk_schema := entry.get("bk_schema"))
        )
        return default_schema, schemas


def _abo


def _get_yaml_file_content(path: str, file_provider: FileProvider) -> Optional[Yaml]:
    bytes_iter = file_provider.get(path)
    if not bytes_iter:
        return None
    stream = get_bytes_iterator_yaml_stream(name=path, iterator=bytes_iter)
    # TO-DO: стоит ещё и YamlLoader добавить в класс наряду с FileProvider
    content = load_yaml(stream)
    return content


def _get_resources_from_mappings_top_level(
    flow_id: int,
    marts_mappings: Sequence[dict],
    references_mappings: Sequence[dict],
    hub_satellites_mappings: Sequence[dict],
    link_satellites_mappings: Sequence[dict],
    short_names_to_targets: dict[str, _WorkFlowResource],
    short_names_to_sources: dict[str, tuple[_WorkFlowResource, ...]],
) -> Iterator[FlowResource]:
    """Связи с ресурсами, заданные на верхнем уровне объектов податрибутов атрибута "mappings",
    и которые можно обработать одним и тем же способом
    """
    for mapping in itertools.chain(
        marts_mappings,
        references_mappings,
        hub_satellites_mappings,
        link_satellites_mappings,
    ):
        algorithm_uid = mapping["algorithm_uid"]
        source_short_name = mapping["source"]
        target_short_name = mapping["target"]
        source_resources = short_names_to_sources[source_short_name]
        target_resource = short_names_to_targets[target_short_name]

        target_flow_resource, source_flow_resource = flow_resource_factory(
            algorithm_uid, flow_id
        )

        for source_resource in source_resources:
            yield source_flow_resource(resource_name=source_resource["resource_cd"])
        yield target_flow_resource(
            resource_name=target_resource["resource_cd"],
            resource_schema_name=target_resource["schema"],
            resource_table_name=target_resource["table"],
        )


def _resolve_direct_and_indirect_resource_aliases(
    definitions: Sequence[dict],
) -> dict[str, tuple[_WorkFlowResource, ...]]:
    direct_mappings = _resolve_direct_resource_aliases(definitions)
    indirect_mappings = {
        defn["short_name"]: _resolve_indirect_resource_cd_mappings(
            defn, direct_mappings
        )
        for defn in definitions
        if "resource_cd" not in defn
    }
    resolved = indirect_mappings
    resolved.update((k, (v,)) for k, v in direct_mappings.items())
    return resolved


def _resolve_direct_resource_aliases(
    definitions: Sequence[dict],
) -> dict[str, _WorkFlowResource]:
    return {defn["short_name"]: defn for defn in definitions if "resource_cd" in defn}


def _resolve_indirect_resource_cd_mappings(
    definition: dict,
    direct_mappings: dict[str, _WorkFlowResource],
) -> tuple[_WorkFlowResource, ...]:
    if not (
        definition["type"] == "COMPUTED_TABLE"
        and (sub_sources_map := definition.get("sub_sources_map")) is not None
    ):
        raise ValueError("unknown type of resource_cd to short_name mapping")

    try:
        return tuple(
            direct_mappings[short_name] for short_name in sub_sources_map.values()
        )
    except KeyError as err:
        raise ValueError(
            f"short_name {err!r} from sub_sources_map values not found in the workflow definition"
        ) from err


def _get_resources_from_marts_hub_map(
    flow_id: int,
    marts_mappings: Sequence[dict],
    short_names_to_targets: dict[str, _WorkFlowResource],
    get_dynamic_bk_schema_names: Callable[[str], tuple[Optional[str], Sequence[str]]],
) -> Iterator[FlowResource]:
    """Связи с ресурсами, заданные в секциях `mappings.marts[*].hub_map`"""
    for mapping in marts_mappings:
        algorithm_uid = mapping["algorithm_uid"]

        target_flow_resource, _ = flow_resource_factory(algorithm_uid, flow_id)

        for hub_map_item in mapping.get("hub_map", ()):
            if not (short_name := hub_map_item.get("target")):
                continue
            if not (target_resource := short_names_to_targets.get(short_name)):
                continue
            resource_cd = target_resource["resource_cd"]
            resource_schema = target_resource["schema"]
            resource_table = target_resource["table"]

            bk_schema = hub_map_item.get("business_key_schema")
            dynamic_bk_schema = hub_map_item.get("dynamic_business_key_schema")
            bk_schemas: Sequence[str]
            if bk_schema:
                bk_schemas = (bk_schema,)
            elif dynamic_bk_schema and (
                map_name := dynamic_bk_schema.get("map_bk_name")
            ):
                default_name, names = get_dynamic_bk_schema_names(map_name)
                use_default = dynamic_bk_schema.get("use_default_bk", False)
                bk_schemas = (default_name,) if use_default else names
            else:
                continue

            for business_key_schema in bk_schemas:
                yield target_flow_resource(
                    resource_name=resource_cd,
                    business_key_schema_name=business_key_schema,
                    resource_schema_name=resource_schema,
                    resource_table_name=resource_table,
                )


def _get_resources_from_satellites_rk_map(
    flow_id: int,
    satellites_mappings: Sequence[dict],
    short_names_to_targets: dict[str, _WorkFlowResource],
) -> Iterator[FlowResource]:
    """Связи с ресурсами, заданные в секциях `mappings.hub_satellites[*].rk_map` или
    `mappings.link_satellites[*].rk_map`
    """
    for satellites_item in satellites_mappings:
        algorithm_uid = satellites_item["algorithm_uid"]

        target_flow_resource, _ = flow_resource_factory(algorithm_uid, flow_id)

        top_level_item = satellites_item.get("rk_map")
        if not top_level_item:
            continue

        # TO-DO: если в "satellites[*].rk_map" есть ещё один вложеный "rk_map" уже внутри содержащий
        # записи с "business_key_schema", то рядом с ним будет ещё один атрибут "target".
        # Надо ли связывать текущий рабочий поток с этим таргетом?

        nested_items: Iterable[dict] = top_level_item.get("rk_map") or (top_level_item,)

        for rk_map in nested_items:
            target_short_name = rk_map.get("target")
            business_key_schema = rk_map.get("business_key_schema")
            if not (target_short_name and business_key_schema):
                continue
            if not (target_resource := short_names_to_targets.get(target_short_name)):
                continue

            yield target_flow_resource(
                resource_name=target_resource["resource_cd"],
                business_key_schema_name=business_key_schema,
                resource_schema_name=target_resource["schema"],
                resource_table_name=target_resource["table"],
            )
