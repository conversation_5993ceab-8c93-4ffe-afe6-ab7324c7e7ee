from typing import (
    Any,
    Callable,
    Generic,
    Iterator,
    List,
    Mapping,
    Optional,
    Sequence,
    TypeVar,
    Union,
)
from uuid import UUID

from sqlalchemy import Result, TextClause, text
from sqlalchemy.engine.interfaces import _CoreAnyExecuteParams
from sqlalchemy.orm import Session
from structlog.stdlib import BoundLogger, get_logger
from typing_extensions import Self

from metaloader_rest_api.loggable import Loggable

logger = get_logger(__name__)


class ExecuteInSessionMixin:
    """Хелпер для уменьшения бойлерплейта при использовании `Session.execute`

    Вместо
    >>> self._session.execute(text("SELECT :foo"), params)
    можно написать просто
    >>> self._execute("SELECT :foo", params)
    """

    _session: Session

    def _execute(
        self,
        statement: Union[str, TextClause],
        params: Optional[_CoreAnyExecuteParams] = None,
    ) -> Result[Any]:
        return self._session.execute(
            statement if isinstance(statement, TextClause) else text(statement),
            params,
        )


class SessionResource(Loggable):
    def __init__(
        self,
        session: Session,
        commit: Optional[bool] = True,
        log: BoundLogger = logger,
    ):
        self._log = log
        self._commit = commit
        self._session = session

    def __call__(
        self,
        action: str,
    ) -> Self:
        self._log = self._log.bind(action=action)
        return self

    def __enter__(self):
        self._log.info("reset")
        self._session.reset()

        return self

    def __exit__(
        self,
        exc_type,
        exc_value,
        traceback,
    ):
        if exc_type is None:
            if self._commit or self._commit is None:
                self._log.info("commit")
                self._session.commit()
            else:
                self._log.warn("rollback")
                self._session.rollback()
        else:
            if self._commit is None:
                self._log.warn("commit")
                self._session.commit()
            else:
                self._log.error("rollback")
                self._session.rollback()


TableStatement = Callable[[str], str]


Record = TypeVar("Record", bound=Mapping[str, Any])


class BatchRepository(Loggable, Generic[Record]):
    PAGE_SIZE_DEFAULT = 100

    def __init__(
        self,
        session: Session,
        table: str,
        create_statement: TableStatement,
        load_statement: TableStatement,
        page_size: int = PAGE_SIZE_DEFAULT,
        log: BoundLogger = logger,
    ):
        self._log = log
        self._log = logger.bind(
            action="load_table",
            table=table,
            page_size=page_size,
        )

        self._session = session
        self._table = table
        self._create_statement = create_statement(table)
        self._load_statement = load_statement(table)
        self._page_size = page_size

    def _create_table(self) -> None:
        self._log.info("create_table")
        self._session.execute(text(self._create_statement))

    def _drop_table(self) -> None:
        self._log.info("drop_table")
        self._session.execute(text(f"DROP TABLE IF EXISTS {self._table}"))

    def _prepare_load_statement(self) -> TextClause:
        self._log.info("prepare_statement")
        statement = text(self._load_statement)
        statement = statement.execution_options(
            insertmanyvalues_page_size=self._page_size
        )
        return statement

    def __enter__(self):
        self._log.info("begin")
        self._drop_table()
        self._create_table()
        self._prepared_load_statement = self._prepare_load_statement()
        self._batch_count = 0
        self._record_count = 0
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self._log.info(
            "end",
            status="fail" if exc_type else "success",
            batch_count=self._batch_count,
            record_count=self._record_count,
        )

    @property
    def table(self) -> str:
        return self._table

    def load(
        self,
        batch: Sequence[Record],
    ) -> None:
        if len(batch) == 0:
            self._log.warn("skip_empty_batch")
            return

        log = self._log.bind(
            stage="batch",
            size=len(batch),
        )
        log.info("begin", count=self._batch_count + 1)
        self._session.execute(
            statement=self._prepared_load_statement,
            params=batch,
        )
        self._session.flush()
        self._record_count += len(batch)
        self._batch_count += 1
        log.info(
            "end",
            batch_count=self._batch_count,
            record_count=self._record_count,
        )


class StageBatchRepository(BatchRepository[Record]):
    def __init__(
        self,
        session: Session,
        table: str,
        table_id: str,
        create_statement: TableStatement,
        load_statement: TableStatement,
        page_size: int = BatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = logger,
    ):
        self._table_id = table_id
        table = f"stg.{table}_{table_id}"
        super().__init__(
            session,
            table,
            create_statement,
            load_statement,
            page_size,
            log,
        )


class BufferedStageBatchRepository(StageBatchRepository[Record]):
    def __init__(
        self,
        session: Session,
        table: str,
        table_id: str,
        create_statement: TableStatement,
        load_statement: TableStatement,
        page_size: int = BatchRepository.PAGE_SIZE_DEFAULT,
        log: BoundLogger = logger,
    ):
        super().__init__(
            session,
            table,
            table_id,
            create_statement,
            load_statement,
            page_size,
            log,
        )

    def __enter__(self):
        super().__enter__()

        self._buffer: List[Record] = []

        return self

    def __exit__(self, exc_type, exc_value, traceback):
        try:
            return super().__exit__(exc_type, exc_value, traceback)
        finally:
            if len(self._buffer) > 0:
                self._load_buffer()

    def _load_buffer(self) -> None:
        self.load(self._buffer)
        self._buffer = []

    def load_one(
        self,
        record: Record,
    ) -> None:
        if len(self._buffer) >= self._page_size:
            self._load_buffer()

        self._buffer.append(record)

    def load_all(
        self,
        records: Iterator[Record],
    ) -> None:
        with self:
            for record in records:
                self.load_one(record)


def get_table_id(load_id: UUID) -> str:
    return load_id.hex
