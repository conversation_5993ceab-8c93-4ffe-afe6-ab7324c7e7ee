from fastapi.routing import APIRouter
from lib.fastapi_versioning_mod import versioned_api_route

from ceh_provider.endpoints import resources, transaction_party, documentation

router = APIRouter(route_class=versioned_api_route(0, 5))
router.include_router(documentation.router, tags=['Docs'])
router.include_router(resources.router, tags=['ceh'])
router.include_router(transaction_party.router, tags=['transaction_party'])
