import json
import os
from typing import Any, Dict, List, Optional

from dwh_services_utils.schemas.ceh_provider.resource import Resource
from pydantic import PostgresDsn, validator

from lib.config import Settings


class CEHProviderSettings(Settings):
    IDENT: str = 'ceh-provider'
    TOPIC_CNT: str = '0'
    ID_SUFFIX: str = f'rand-{TOPIC_CNT}'
    NODE_ID: str = 'CEH_1'
    KID: str = 'K1'
    API_03_ALLOWED: int = 0
    API_05_ALLOWED: int = 0

    # Настройки консумера для мульти инстанса
    BROKER_SESSION_TIMEOUT: int = 600
    BROKER_REQUEST_TIMEOUT: int = 1200
    BROKER_HEARTBEAT_INTERVAL: int = 15
    BROKER_REBALANCE_TIMEOUT: int = 300

    # API_HOST: str = '0.0.0.0'
    # API_PORT: int = 8000
    #
    SELF_BASE_URL: str = 'http://0.0.0.0:8001/'

    SEQUENCE_GENERATOR_URL: str = 'http://0.0.0.0:8805/'
    SEQUENCE_RESOURCE_CEH: str = 'ceh'
    TX_MANAGER_BASE_URL: str = 'http://0.0.0.0:8002/'
    DELTA_MANAGER_BASE_URL: str = 'http://0.0.0.0:8810/'
    CLOSE_TRANSACTION_INTERVAL: int = 1
    DELTA_MANAGER_ALIVE_TIME_DIFF: int = 30
    DELTA_MANAGER_ALIVE_TIMEOUT: int = 30
    PARTIES_REG_ATTEMPT: int = 1
    PARTIES_REG_RETRY_INTERVAL: int = 3

    JQ_TIMEOUT_FILTER: int = 10
    JQ_TIMEOUT_METRICS: int = 10
    JQ_TIMEOUT_ATTRS: int = 10
    JQ_TIMEOUT_DEFAULT: int = 10
    JQ_NUM_WORKERS: int = 4
    METRICS_LIMIT: int = 100
    IS_ASYNC_C_JQ: int = 0
    JQ_PATH: str = '/usr/bin/jq'

    # FAUST_STORE: str = 'memory://'
    # FAUST_STORE_AEROSPIKE_NAMESPACE: str = 'test'
    # FAUST_STORE_AEROSPIKE_HOSTS: str = '127.0.0.1:3000'
    # FAUST_STORE_AEROSPIKE_USERNAME: str = None
    # FAUST_STORE_AEROSPIKE_PASSWORD: str = None
    # FAUST_STORE_OPTIONS: dict = None

    DB_DRIVER: str = 'postgresql'
    DB_HOST: str = 'pg_ceh'
    DB_PORT: int = 5432
    DB_USER: str = 'default'
    DB_PASSWORD: str = 'default'
    DB_DATABASE: str = 'ceh'
    DB_CONN_STR: Optional[PostgresDsn] = None
    DB_APP_NAME: Optional[str] = None
    DB_POOL_MIN_SIZE: int = 1
    DB_POOL_MAX_SIZE: int = 50
    DB_ECHO: bool = False
    DB_USE_CONNECTION_FOR_REQUEST: bool = True
    DB_RETRY_LIMIT: int = 1
    DB_RETRY_INTERVAL: int = 1

    # Креды к БД мониторинга
    IS_DB_SERVICE_USE: int = 0
    DB_SERVICE_DRIVER: str = 'postgresql'
    DB_SERVICE_HOST: str = 'pg_ceh'
    DB_SERVICE_PORT: int = 5432
    DB_SERVICE_USER: str = 'default'
    DB_SERVICE_PASSWORD: str = 'default'
    DB_SERVICE_DATABASE: str = 'ceh'
    DB_SERVICE_CONN_STR: Optional[PostgresDsn] = None
    DB_SERVICE_APP_NAME: Optional[str] = None
    DB_SERVICE_POOL_MIN_SIZE: int = 1
    DB_SERVICE_POOL_MAX_SIZE: int = 5
    DB_SERVICE_ECHO: bool = False
    DB_SERVICE_USE_CONNECTION_FOR_REQUEST: bool = True
    DB_SERVICE_RETRY_LIMIT: int = 1
    DB_SERVICE_RETRY_INTERVAL: int = 1

    # S3_USE: bool = False
    REPLICATION_TYPE: Optional[str] = None  # GP, S3
    REPLICATION_S3_LIMIT: int = None
    S3_BUCKET: str = 'd5-dtpl-ssum'
    WAIT_S3_REPL: int = 0
    S3_REPL_CONCURRENCY: int = 1
    S3_USE_COPY_MANAGER: bool = False
    S3_COPY_MANAGER_TOPIC: str = f'srv-copy--{TOPIC_CNT}--stage-task'
    S3_COPY_MANAGER_TABLES: List[str] = []
    S3_COPY_MANAGER_SCHEMAS: List[str] = []
    REPL_MODE_CONFIG_PATH: str = '/opt/app/ceh_provider/dr_replication_mode.json'
    REPL_MODE_CONFIG: dict = {"string": "S3"}
    USE_SEPARATE_SERVICE: int = 1

    OAUTH_USERNAME_TX: str = None
    OAUTH_PASSWORD_TX: str = None
    OAUTH_USERNAME_SQ: str = None
    OAUTH_PASSWORD_SQ: str = None

    RESOURCES_MAX_LIMIT: int = 100
    RESOURCES_LIMIT: int = 100
    RESOURCES_OFFSET: int = 0
    RESOURCES_AGENT_READ_CONCURRENCY: int = 10
    DISTRIBUTED_AGENT_READ_CONCURRENCY: int = 10
    DISTRIBUTED_AGENT_READ_TIMEOUT: int = 60
    DISTRIBUTED_AGENT_SET_TIMEOUT: int = 100

    CLOSE_TX_LOG_INTERVAL: int = 10

    DEFAULT_RESOURCES: Optional[str] = None

    RESOURCES: List[str] = ['ceh.public.example']
    RESOURCES_CFG: Optional[List[Resource]] = []

    # Валидация ресурса
    IS_VALIDATION_REQUIRED: int = 0
    IS_REGISTRY_VALIDATION_REQUIRED: int = 0
    IS_DATASET_VALIDATION_REQUIRED: int = 0
    IS_UNLOCK_ALLOWED: bool = True
    FILL_VERSION_ID_REVERSE_INDEX: bool = False

    # Запрет повторной регистрации дельты
    DENY_REGISTER_SAME_OPERATION: bool = False
    # Настройки генерации стейтмента дельты
    IS_CEH_GEN_STATEMENT_ENABLED: bool = True
    # Настройки проверки EXPLAIN стейтмента дельты
    IS_CEH_EXPLAIN_STATEMENT_ENABLED: bool = True
    # Настройки проверки дельты
    IS_VALIDATE_DELTA_ENABLED: bool = False
    # Дополнительные наименования колонок record_mode дельты
    DELTA_RECORD_MODE_FIELDS: List[str] = ['delta_record_mode', 'record_mode']
    # Наименование колонки version_id
    VERSION_ID_FIELD: str = 'version_id'
    # Наименование колонки to_version_id
    TO_VERSION_ID_FIELD: str = 'to_version_id'
    # Не падать, при наличии 'A' в дельте в классическом режиме scd2
    SCD2_DELTA_RECORD_MODE_A_STOP_ERROR: bool = False
    # Наименование колонки deleted_flg
    DELETED_FLG_FIELD: str = 'deleted_flg'
    # Список таблиц, в которых может не быть поля deleted_flg
    DELETED_FLG_SKIP_CHECK_TABLE: str = 'hub_, lnk_, rlnk_, sal_'
    DELETED_FLG_SKIP_CHECK_TABLE_LIST: List[str] = []
    # Проверка по регулярному выражению
    RE_DELTA_TABLE_CHECK: bool = False
    # Регулярное выражение проверки v{version}_
    RE_DELTA_TABLE_NAME_CHECK: str = r'^v\d+_'
    # Переименование дельта таблицы в уникальное имя
    RENAME_DELTA_TABLE_TO_UNIQUE_NAME: bool = False
    # Копирование дельта таблицы в уникальное имя
    COPY_DELTA_TABLE_TO_UNIQUE_NAME: bool = False
    DROP_DELTA_TABLE_AFTER_COPY: bool = False
    # Получение сигнатуры дельты
    IS_CEH_DELTA_SIGNATURE_ENABLED: bool = True
    # Получение оценочной сигнатуры дельты
    IS_CEH_DELTA_ESTIMATE_SIGNATURE_ENABLED: bool = True
    # Максимально допустимое колличеств строк в дельте
    CEH_DELTA_SIGNATURE_MAX_ROW_COUNT: Optional[int] = 20*1000*1000*1000
    # Наличие поля delta_table в таблице etl.version_counts
    IS_DELTA_IN_VERSION_COUNT: bool = False
    IS_DELTA_IN_VERSION_COUNT_DAYS: int = 14

    IS_DATASETS_FROM_RES: int = 0

    # Генерация запросов проверки дублей в таргете
    GENERATE_CHECK_QUERIES: bool = True
    # Выполнять расчет кастомных метрик
    IS_CEH_ACTUALIZE_METRICS_ENABLED: bool = False

    CACHE_TIMEOUT: int = 300
    MART_CACHE_TIMEOUT: int = 600
    # LOG_CONF: dict = {}

    WRITE_KAFKA_LOG = True
    WRITE_INSERT_ARENA_LOG = True
    WRITE_SELECT_ARENA_LOG = True

    # Запись истории регистрации ресурсов
    LOG_RESOURCES_HISTORY = True

    @validator('RESOURCES_CFG', pre=True)
    # pylint: disable=no-self-argument
    def load_resources(cls, value: Optional[str], values: Dict[str, Any]) -> Optional[str]:
        if value is not None:
            return value
        filename = values.get('DEFAULT_RESOURCES')
        if filename and os.path.isfile(filename):
            with open(filename) as fp:
                return json.load(fp)
        return None

    @validator('LOG_CONF', pre=True)
    # pylint: disable=no-self-argument
    def get_log_conf(cls, value: Optional[str], values: Dict[str, Any]) -> dict:
        log_conf = {}
        for conf_elem in ['ID_SUFFIX', 'ID_APP', 'TOPIC_CNT', 'NODE_ID', 'IDENT', 'INSTANCE', 'TOPIC_CNT']:
            if conf_elem in values.keys():
                log_conf[conf_elem] = values[conf_elem]
        return log_conf

    @validator('DB_CONN_STR', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme='postgresql',
            host=values['DB_HOST'],
            port=str(values['DB_PORT']),
            user=values['DB_USER'],
            password=values['DB_PASSWORD'],
            path=f'/{values["DB_DATABASE"]}',
        )

    @validator('DB_APP_NAME', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_app_name(cls, _: Optional[str], values: Dict[str, Any]) -> str:
        ident = values['IDENT']
        version = values['SERVICE_VERSION']
        instance = values['INSTANCE']
        return f'SSUM {ident} {version} {instance}'

    @validator('DELETED_FLG_SKIP_CHECK_TABLE_LIST', pre=True)
    # pylint: disable=no-self-argument
    def assemble_deleted_flg_skip_check_table_list(cls, v: Optional[list], values: Dict[str, Any]) -> Any:
        return [val.strip() for val in values['DELETED_FLG_SKIP_CHECK_TABLE'].split(',')]

    @validator('REPL_MODE_CONFIG', pre=True)
    # pylint: disable=no-self-argument
    def load_repl_config(cls, value: Optional[dict], values: Dict[str, Any]) -> Optional[dict]:
        if value:
            return value
        path_to_config = values.get('REPL_MODE_CONFIG_PATH')
        if path_to_config and os.path.isfile(path_to_config):
            with open(path_to_config) as fp:
                return json.load(fp)
        return {}

    @validator('DB_SERVICE_CONN_STR', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_service_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme='postgresql',
            host=values['DB_SERVICE_HOST'],
            port=str(values['DB_SERVICE_PORT']),
            user=values['DB_SERVICE_USER'],
            password=values['DB_SERVICE_PASSWORD'],
            path=f'/{values["DB_SERVICE_DATABASE"]}',
        )

    @validator('DB_SERVICE_APP_NAME', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_service_app_name(cls, _: Optional[str], values: Dict[str, Any]) -> str:
        ident = values['IDENT']
        version = values['SERVICE_VERSION']
        instance = values['INSTANCE']
        return f'SSUM {ident} {version} {instance}'

    class Config:
        case_sensitive = True


conf = CEHProviderSettings()

