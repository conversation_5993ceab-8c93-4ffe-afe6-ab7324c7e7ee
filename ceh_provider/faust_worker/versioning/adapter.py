from enum import Enum
from typing import Union

from dwh_services_utils.schemas.ceh_provider import resource as schemas
from dwh_services_utils.schemas.ceh_provider.v0_5 import schemas as schemas_v0_5
from dwh_services_utils.schemas.ceh_provider.v0_6 import schemas as schemas_v0_6
from dwh_services_utils.schemas.v1_0 import schemas as schemas_v1_0

ALGO_NAME = 'transition'


class ApiVersion(Enum):
    """Версии api"""
    v_0_3 = '0.3'
    v_0_5 = '0.5'
    v_0_6 = '0.6'
    v_1_0 = '1.0'


class VersionRequestAdapter:
    def __init__(self, request_body):
        self._request_body = request_body
        self._version = None

    def get_specified_version_request(self) -> Union[
        schemas.ResourceStateRequest,
        schemas_v0_5.ResourceStateRequest,
        schemas_v0_6.ResourceStateRequest,
        schemas_v1_0.ResourceCEHStateRequest,
    ]:
        if 'v' in self._request_body:
            if ApiVersion(self._request_body['v']) is ApiVersion.v_0_3:
                self._version = ApiVersion.v_0_3
                return schemas_v0_5.ResourceStateRequest(**self._request_body)
            elif ApiVersion(self._request_body['v']) is ApiVersion.v_0_5:
                self._version = ApiVersion.v_0_5
                return schemas_v0_5.ResourceStateRequest(**self._request_body)
            elif ApiVersion(self._request_body['v']) is ApiVersion.v_0_6:
                self._version = ApiVersion.v_0_6
                return schemas_v0_6.ResourceStateRequest(**self._request_body)
            elif ApiVersion(self._request_body['v']) is ApiVersion.v_1_0:
                self._version = ApiVersion.v_1_0
                return schemas_v1_0.ResourceCEHStateRequest(**self._request_body)
        else:
            if 'statement' in self._request_body:
                self._version = ApiVersion.v_0_3
                return schemas.ResourceStateRequest(**self._request_body)
            else:
                self._version = ApiVersion.v_1_0
                return schemas_v1_0.ResourceCEHStateRequest(**self._request_body)


class StateVersionAdapter:
    def __init__(self, state):
        self._state = state

    def get_state_version_request_v_0_3_to_v_0_5(self) -> schemas_v0_5.ResourceState:
        origin = None
        sources = []
        targets = []
        state = self._state.asdict()
        state['version'] = state['version'].asdict()
        state['version']['status'] = state['version']['status'].asdict()
        res_state = schemas_v0_5.ResourceState(
            resource_cd=self._state.resource_cd,
            is_locked=self._state.is_locked,
            locked_by=self._state.locked_by,
            as_of_dttm=self._state.as_of_dttm,
            version=schemas_v1_0.VersionCEH(
                version_id=state['version']['version_id'],
                tx_uid=state['version']['tx_uid'],
                origin=origin,
                sources=sources,
                status=schemas_v1_0.OperationStatusCEH(**state['version']['status']),
                targets=targets,
            )
        )
        return res_state

    def get_state_version_v_1_0(self) -> schemas_v1_0.ResourceCEHState:
        origin = None
        sources = []
        targets = []
        state = self._state.asdict()
        state['version'] = state['version'].asdict()
        origin = self._adapt_operations(origin, sources, state, targets)
        state['version']['status'] = state['version']['status'].asdict()
        res_state = schemas_v1_0.ResourceCEHState(
            resource_cd=self._state.resource_cd,
            is_locked=self._state.is_locked,
            locked_by=self._state.locked_by,
            as_of_dttm=self._state.as_of_dttm,
            version=schemas_v1_0.VersionCEH(
                version_id=state['version']['version_id'],
                tx_uid=state['version']['tx_uid'],
                origin=origin,
                sources=sources,
                status=schemas_v1_0.OperationStatusCEH(**state['version']['status']),
                targets=targets,
            )
        )
        return res_state

    def get_state_version_v_0_3(self) -> schemas.ResourceState:
        origin = None
        sources = []
        targets = []
        state = self._state.asdict()
        state['version'] = state['version'].asdict()
        origin = self._adapt_operations(origin, sources, state, targets)
        state['version']['status'] = state['version']['status'].asdict()
        res_state = schemas.ResourceState(
            resource_cd=self._state.resource_cd,
            is_locked=self._state.is_locked,
            locked_by=self._state.locked_by,
            as_of_dttm=self._state.as_of_dttm,
            version=schemas_v1_0.VersionCEH(
                version_id=state['version']['version_id'],
                tx_uid=state['version']['tx_uid'],
                origin=origin,
                sources=sources,
                status=schemas.OperationStatus(**state['version']['status']),
                targets=targets,
            )
        )
        return res_state

    def _adapt_operations(self, origin, sources, state, targets):
        for operation in state['version']['operations']:
            for source in operation['sources']:
                self._adapt_sources(source, sources)
            schema, name = self.get_table_schema_name(operation['target'])
            targets.append(
                {
                    'resource_cd': operation['target']['resource_cd'],
                    'table': {
                        'schema': schema,
                        'name': name,
                    }
                }
            )
            if 'author' in operation['origin'].keys() and not state['version']['origin']:
                origin = {'author': operation['origin']['author']}
        return origin

    def _adapt_sources(self, source, sources):
        if not source:
            return
        try:
            if source['prev_state']:
                source['scope'] = {'wf_min_version_id': source['prev_state']['version']['version_id']}
        except KeyError:
            pass
        try:
            if source['prev_state']:
                source['scope'] = {'wf_min_processed_dt': source['prev_state']['ods_wf_max_date_to']}
        except KeyError:
            pass
        sources.append(source)

    def get_table_schema_name(self, target):
        schema = None
        name = None
        tbl = str(target['table']).split('.')
        try:
            name = tbl[-1]
            schema = tbl[-2]
        except IndexError:
            pass
        return schema, name
