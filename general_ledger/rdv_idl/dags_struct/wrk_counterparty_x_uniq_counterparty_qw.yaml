workflow:
  name: <PERSON><PERSON>_COUNTERPARTY_X_UNIQ_COUNTERPARTY_QW
  depends:
  - slv_wf_name: WF_COUNTERPARTY_X_UNIQ_COUNTERPARTY_QW
    target_resource_name: ceh.idl.counterparty_x_uniq_counterparty
    target_table_name: idl.counterparty_x_uniq_counterparty
    source_resource_names:
      - ceh.rdv.rlsat_clglobal_mdmetalon_cbp
      - ceh.rdv.rlnk_clglobal_mdmetalon
      - ceh.rdv.rlsat_cbpmdm_src_client_cpb
      - ceh.rdv.rlnk_cbpmdm_src_client
      - ceh.rdv.hub_client
    prepare_db_func_name: fn_qw_counterparty_x_uniq_counterparty_stg_load
    load_db_func_name: fn_qw_counterparty_x_uniq_counterparty_dlt_load
    schema_name: idl
  keys:
    parent_dag_id: cf_counterparty_x_uniq_counterparty_qw

