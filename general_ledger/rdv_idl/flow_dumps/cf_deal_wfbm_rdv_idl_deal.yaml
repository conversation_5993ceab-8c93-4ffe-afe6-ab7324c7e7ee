schema_version: 2.0
metadata:
  author: AAstrelina
  version: '1.0'
  description: Управляющий поток IDL deal
  tags:
    - 'team: zi19'
    - 'rls: 18.1'
    - 'cf: cf_deal_wfbm_rdv_idl_deal'
    - 'wrk: wrk_deal_wfbm_rdv_idl_deal'
    - 'area: deal'
    - 'src: wfbm'
    - 'src-tbl: mart_deal_acnt_contract_wfbm'
    - 'src-tbl: mart_account_account_wfbm'
    - 'src-tbl: mart_account_scheme_deal_acc_scheme_wfbm'
    - 'prv: rdv'
    - 'tgt: idl'
    - 'tgt-tbl: bbridge_deal'
    - 'tgt-tbl: bbridge_deal_status'
    - 'tgt-tbl: blink_deal_x_product'
    - 'tgt-tbl: blink_deal_x_department'
    - 'tgt-tbl: blink_deal_x_account'
    - 'tgt-tbl: blink_deal_x_counterparty'
    - 'tgt-tbl: bbridge_deal_fee'
  imports:
    - ceh_frm_idl_utils.source_resourse_transfer
  group: deal
flows:
  - id: cf_deal_wfbm_rdv_idl_deal
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Управляющий поток
    metadata:
      - name: main_workflow_name
        type: in
        default: wrk_deal_wfbm_rdv_idl_deal
      - name: workflow_name
        type: in
        default: wrk_deal_wfbm_rdv_idl_deal
      - name: target_resource_name_bbridge_deal_fee
        type: in
        default: ceh.idl.bbridge_deal_fee.wfbm
      - name: target_table_name_bbridge_deal_fee
        type: in
        default: idl.bbridge_deal_fee
      - name: target_resource_name_sal_deal_fee
        type: in
        default: ceh.idl.sal_deal_fee.wfbm
      - name: target_table_name_sal_deal_fee
        type: in
        default: idl.sal_deal_fee
      - name: target_resource_name_bbridge_deal
        type: in
        default: ceh.idl.bbridge_deal.wfbm
      - name: target_table_name_bbridge_deal
        type: in
        default: idl.bbridge_deal
      - name: target_resource_name_blink_deal_x_counterparty
        type: in
        default: ceh.idl.blink_deal_x_counterparty.wfbm
      - name: target_table_name_blink_deal_x_counterparty
        type: in
        default: idl.blink_deal_x_counterparty
      - name: target_resource_name_blink_deal_x_account
        type: in
        default: ceh.idl.blink_deal_x_account.wfbm
      - name: target_table_name_blink_deal_x_account
        type: in
        default: idl.blink_deal_x_account
      - name: target_resource_name_blink_deal_x_product
        type: in
        default: ceh.idl.blink_deal_x_product.wfbm
      - name: target_table_name_blink_deal_x_product
        type: in
        default: idl.blink_deal_x_product
      - name: target_resource_name_sal_deal
        type: in
        default: ceh.idl.sal_deal.wfbm
      - name: target_table_name_sal_deal
        type: in
        default: idl.sal_deal
      - name: target_resource_name_sal_agreement
        type: in
        default: ceh.idl.sal_agreement.wfbm
      - name: target_table_name_sal_agreement
        type: in
        default: idl.sal_agreement
      - name: target_resource_name_bbridge_agreement
        type: in
        default: ceh.idl.bbridge_agreement.wfbm
      - name: target_table_name_bbridge_agreement
        type: in
        default: idl.bbridge_agreement
      - name: target_resource_name_sal_product
        type: in
        default: ceh.idl.sal_product.wfbm
      - name: target_table_name_sal_product
        type: in
        default: idl.sal_product
      - name: target_resource_name_bbridge_product
        type: in
        default: ceh.idl.bbridge_product.wfbm
      - name: target_table_name_bbridge_product
        type: in
        default: idl.bbridge_product
      - name: target_table_name_sal_counterparty
        type: in
        default: idl.sal_counterparty
      - name: target_resource_name_sal_counterparty
        type: in
        default: ceh.idl.sal_counterparty.wfbm
      - name: target_resource_name_bbridge_counterparty
        type: in
        default: ceh.idl.bbridge_counterparty.wfbm
      - name: target_table_name_bbridge_counterparty
        type: in
        default: idl.bbridge_counterparty
      - name: target_resource_name_bbridge_deal_status
        type: in
        default: ceh.idl.bbridge_deal_status.wfbm
      - name: target_table_name_bbridge_deal_status
        type: in
        default: idl.bbridge_deal_status
      - name: target_resource_name_blink_deal_x_department
        type: in
        default: ceh.idl.blink_deal_x_department.wfbm
      - name: target_table_name_blink_deal_x_department
        type: in
        default: idl.blink_deal_x_department
      - name: target_resource_name_sal_account
        type: in
        default: ceh.idl.sal_account.wfbm
      - name: target_table_name_sal_account
        type: in
        default: idl.sal_account
      - name: target_resource_name_account
        type: in
        default: ceh.idl.account.wfbm
      - name: target_table_name_account
        type: in
        default: idl.account
      - name: source_resource_names
        type: in
        default:
          - ceh.rdv.mart_deal_acnt_contract_wfbm
          - ceh.rdv.mart_account_account_wfbm
          - ceh.rdv.mart_account_scheme_deal_acc_scheme_wfbm
    tasks:
      - id: check_available_resources_in_ceh_provider_account
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_account}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_sal_account
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_sal_account}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_blink_deal_x_account
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_blink_deal_x_account}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_blink_deal_x_department
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_blink_deal_x_department}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_bbridge_deal_status
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_bbridge_deal_status}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_bbridge_deal
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_bbridge_deal}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_sal_deal
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_sal_deal}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_sal_deal_fee
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_sal_deal_fee}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_sal_agreement
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_sal_agreement}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_bbridge_agreement
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_bbridge_agreement}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_sal_product
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_sal_product}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_bbridge_product
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_bbridge_product}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_sal_counterparty
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_sal_counterparty}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_bbridge_counterparty
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_bbridge_counterparty}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_blink_deal_x_product
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_blink_deal_x_product}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_blink_deal_x_counterparty
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_blink_deal_x_counterparty}
          source_resource_names: ${source_resource_names}
      - id: check_available_resources_in_ceh_provider_bbridge_deal_fee
        description: Проверка доступности ресурсов(источников и приемник) в провайдере
          ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name_bbridge_deal_fee}
          source_resource_names: ${source_resource_names}
      - id: get_common_version
        description: Получение общей стабильной версии
        type: ceh_core_idl.app.operators.services.common_version_operator
        metadata:
          - name: common_version_name
            datatype: str
            type: out
          - name: common_version
            datatype: int
            type: out
        properties:
          resources: ${source_resource_names}
        sequencer:
          dependencies:
            - ref: check_available_resources_in_ceh_provider_bbridge_deal
            - ref: check_available_resources_in_ceh_provider_sal_deal
            - ref: check_available_resources_in_ceh_provider_sal_agreement
            - ref: check_available_resources_in_ceh_provider_bbridge_agreement
            - ref: check_available_resources_in_ceh_provider_sal_product
            - ref: check_available_resources_in_ceh_provider_bbridge_product
            - ref: check_available_resources_in_ceh_provider_sal_counterparty
            - ref: check_available_resources_in_ceh_provider_bbridge_counterparty
            - ref: check_available_resources_in_ceh_provider_blink_deal_x_product
            - ref: check_available_resources_in_ceh_provider_bbridge_deal_status
            - ref: check_available_resources_in_ceh_provider_blink_deal_x_department
            - ref: check_available_resources_in_ceh_provider_account
            - ref: check_available_resources_in_ceh_provider_sal_account
            - ref: check_available_resources_in_ceh_provider_blink_deal_x_account
            - ref: check_available_resources_in_ceh_provider_blink_deal_x_counterparty
            - ref: check_available_resources_in_ceh_provider_bbridge_deal_fee
            - ref: check_available_resources_in_ceh_provider_sal_deal_fee
      - id: get_resources_versions_sal_account
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.sal_account
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_sal_account}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_sal_counterparty
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.sal_counterparty
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_sal_counterparty}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_sal_product
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.sal_product
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_sal_product}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_sal_agreement
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.sal_agreement
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_sal_agreement}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_sal_deal
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.sal_deal
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_sal_deal}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_sal_deal_fee
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.sal_deal_fee
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_sal_deal_fee}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_bbridge_agreement
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.bbridge_agreement.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_bbridge_agreement}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_bbridge_product
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.bbridge_product.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_bbridge_product}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_account
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.account.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_account}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_bbridge_counterparty
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.bbridge_counterparty.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_bbridge_counterparty}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_blink_deal_x_account
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.blink_deal_x_account.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_blink_deal_x_account}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_blink_deal_x_department
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.blink_deal_x_department.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_blink_deal_x_department}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_bbridge_deal_status
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.bbridge_deal_status.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_bbridge_deal_status}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_bbridge_deal
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.bbridge_deal.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_bbridge_deal}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_blink_deal_x_product
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.blink_deal_x_product.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_blink_deal_x_product}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_blink_deal_x_counterparty
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.deal_x_counterparty.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_blink_deal_x_counterparty}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_resources_versions_bbridge_deal_fee
        description: Получение максимальной загруженной версии в целевой ресурс ceh.idl.bbridge_deal_fee.wfbm
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_bbridge_deal_fee}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: wrk_deal_wfbm_rdv_idl_deal
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_workflow_parameters
        description: Определение основных параметров рабочего потока
        type: ceh_core_idl.app.operators.basic.workflow_parameters_operator
        properties:
          version_parameters:
            - ${get_resources_versions_bbridge_deal}
            - ${get_resources_versions_blink_deal_x_product}
            - ${get_resources_versions_bbridge_deal_status}
            - ${get_resources_versions_blink_deal_x_department}
            - ${get_resources_versions_blink_deal_x_account}
            - ${get_resources_versions_blink_deal_x_counterparty}
            - ${get_resources_versions_bbridge_deal_fee}
          stable_version: ${get_common_version.common_version}
        sequencer:
          dependencies:
            - ref: get_resources_versions_bbridge_deal
            - ref: get_resources_versions_blink_deal_x_product
            - ref: get_resources_versions_bbridge_deal_status
            - ref: get_resources_versions_blink_deal_x_department
            - ref: get_resources_versions_blink_deal_x_account
            - ref: get_resources_versions_blink_deal_x_counterparty
            - ref: get_resources_versions_bbridge_deal_fee
      - id: get_metrics
        builder: ceh_core_idl.app.builders.include_flow_builder
        description: Приведение ресурсов к заданному виду для переноса в последующий
          слой
        properties:
          ref: source_resourse_transfer
          properties:
            source_resource_names: ${source_resource_names}
            target_resource_names:
              - ${target_resource_name_sal_account}
              - ${target_resource_name_sal_counterparty}
              - ${target_resource_name_sal_product}
              - ${target_resource_name_sal_agreement}
              - ${target_resource_name_sal_deal}
              - ${target_resource_name_sal_deal_fee}
              - ${target_resource_name_bbridge_agreement}
              - ${target_resource_name_bbridge_product}
              - ${target_resource_name_account}
              - ${target_resource_name_bbridge_counterparty}
              - ${target_resource_name_blink_deal_x_account}
              - ${target_resource_name_blink_deal_x_department}
              - ${target_resource_name_bbridge_deal_status}
              - ${target_resource_name_bbridge_deal}
              - ${target_resource_name_blink_deal_x_product}
              - ${target_resource_name_blink_deal_x_counterparty}
              - ${target_resource_name_bbridge_deal_fee}
            algorithm_uid: wrk_deal_wfbm_rdv_idl_deal
        sequencer:
          dependencies:
            - ref: get_workflow_parameters
      - id: trigger_wrk
        description: Запуск рабочего потока
        type: ceh_core_idl.app.operators.core.trigger_dag_operator
        properties:
          trigger_dag_id: wrk_deal_wfbm_rdv_idl_deal
          conf:
            instance_id: ${get_workflow_parameters.instance_id}
            src_version_id: ${get_workflow_parameters.src_version_id}
            stable_version_id: ${get_workflow_parameters.stable_version_id}
            init_load_flg: false
            by_src: ${get_metrics.result_data}
        sequencer:
          dependencies:
            - ref: get_metrics
