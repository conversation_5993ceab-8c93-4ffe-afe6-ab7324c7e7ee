schema_version: 2.0
metadata:
  author: asuhoveev
  version: "2.0"
  description: Управляющий поток сущности bbridge_pa_customer_flag_sdm_rkk
  tags:
    - "team: zi13"
    - "cf"
    - "area: risk"
    - "src: rkk"
    - "src-tbl: mart_pa_customer_flag_sdm_rkk"
    - "src-tbl: mart_pa_db_wave_sdm_rkk"
    - "prv: rdv"
    - "tgt: idl"
    - "tgt-tbl: bbridge_pa_customer_flag_sdm_rkk"
    - "cf: cf_risk_rkk_rdv_idl_bbr_pa_customer_flag_sdm_rkk"
    - "wrk: wrk_risk_rkk_rdv_idl_bbr_pa_customer_flag_sdm_rkk"
    - "rls: 18"
  group: risk
  imports:
    - ceh_frm_idl_utils.source_resourse_transfer
flows:
  - id: cf_risk_rkk_rdv_idl_bbr_pa_customer_flag_sdm_rkk
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Управляющий поток cf_risk_rkk_rdv_idl_bbr_pa_customer_flag_sdm_rkk
    metadata:
      - name: workflow_name
        type: in
        default: WF_RISK_RKK_RDV_IDL_BBR_PA_CUSTOMER_FLAG_SDM_RKK

      # tgt
      - name: tgt_table_name
        type: in
        default: idl.bbridge_pa_customer_flag_sdm_rkk

      # res
      - name: tgt_res_name
        type: in
        default: ceh.idl.bbridge_pa_customer_flag_sdm_rkk.sdm_rkk
   
      - name: src_res_name
        type: in
        default: # списком
          - ceh.rdv.mart_pa_customer_flag_sdm_rkk
          - ceh.rdv.mart_pa_db_wave_sdm_rkk

    tasks:
      ######### check_available_resources_in_ceh_provider #########      
      
      - id: check_available_resources_in_ceh_provider
        description: Проверка доступности ресурсов(источников и приемник) в провайдере ресурсов
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${tgt_res_name}
          source_resource_names: ${src_res_name}
     
      ######### get_common_version #########
      
      - id: get_common_version
        description: Получение общей стабильной версии
        type: ceh_core_idl.app.operators.services.common_version_operator
        metadata:
          - name: common_version_name
            datatype: str
            type: out
          - name: common_version
            datatype: int
            type: out
        properties:
          resources: ${src_res_name}
        sequencer:
          dependencies:
            - ref: check_available_resources_in_ceh_provider
      
      ######### get_resources_versions #########
      
      - id: get_resources_versions
        description: Получение максимальной загруженной версии в целевой ресурс
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
          - name: date_last_download
            datatype: str
            type: out
        properties:
          target_resource_name: ${tgt_res_name}
          source_resource_names: ${src_res_name}
          stable_version: ${get_common_version.common_version}
          algo_name: sdm_rkk         # (!)
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
     
      ######### get_workflow_parameters #########      
      
      - id: get_workflow_parameters
        description: Определение основных параметров рабочего потока
        type: ceh_core_idl.app.operators.basic.workflow_parameters_operator
        metadata:
          - name: instance_id
            datatype: int
            type: out
          - name: prev_data_load_dt
            datatype: str
            type: out
          - name: src_version_id
            datatype: int
            type: out
          - name: stable_version_id
            datatype: int
            type: out
        properties:
          workflow: ${workflow_name}                ##
          version_parameters:
            - ${get_resources_versions}
          stable_version: ${get_common_version.common_version}
        sequencer:
          dependencies:
            - ref: get_resources_versions
              condition: ${get_resources_versions.status and get_resources_versions.src_version_check_status}

      ######### get_metrics #########    

      - id: get_metrics
        description: Приведение ресурсов к заданному виду для переноса в последующий слой
        builder: ceh_core_idl.app.builders.include_flow_builder
        properties:
          ref: source_resourse_transfer
          properties:
            source_resource_names: ${src_res_name}
        sequencer:
          dependencies:
            - ref: get_workflow_parameters

      ######### trigger_wrk #########      
      
      - id: trigger_wrk
        description: Запуск рабочего потока
        type: ceh_core_idl.app.operators.core.trigger_dag_operator
        properties:
          trigger_dag_id: wrk_risk_rkk_rdv_idl_bbr_pa_customer_flag_sdm_rkk
          conf:
            instance_id: ${get_workflow_parameters.instance_id}
            src_version_id: ${get_workflow_parameters.src_version_id}
            stable_version_id: ${get_workflow_parameters.stable_version_id}
            init_load_flg: ${get_workflow_parameters.init_load_flg}
            algorithm_name: sdm_rkk
            by_src: ${get_metrics.result_data}
        sequencer:
          dependencies: 
            - ref: get_metrics