schema_version: 2.0
metadata:
  author: ZI14 NLitvinov
  version: "1.0"
  description: Поток наполнения сущности idl.blink_collateral_object_x_collateral
  tags:
    - "team: zi14"
    - "wrk"
    - "area: cltrl"
    - "src: kih_ti"
    - "src-tbl: mart_collateral_ref_collateral_h_kih"
    - "src-tbl: mart_collateral_object_ref_collateral_object_h_kih"
    - "prv: rdv"
    - "tgt: idl"
    - "tgt-tbl: blink_collateral_object_x_collateral"
    - "tgt-tbl: sal_collateral"
    - "tgt-tbl: sal_collateral_object"
    - "rls: 23"
    - "entity: collateral"
  group: cltrl # указать бизнес-область таблицы
  imports:
    - ceh_frm_idl_utils.get_data
    - ceh_frm_idl_utils.source_resourse_transfer
flows:
  - id: wrk_cltrl_kih_ti_rdv_idl_blink_collateral_object_x_collateral
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Управляющий поток wrk_cltrl_kih_ti_rdv_idl_blink_collateral_object_x_collateral
    metadata:
      - name: target_resource_name_blink_collateral_object_x_collateral
        default: ceh.idl.blink_collateral_object_x_collateral.kih_ti
      - name: target_resource_name_sal_collateral
        default: ceh.idl.sal_collateral.kih_ti
      - name: target_resource_name_sal_collateral_object
        default: ceh.idl.sal_collateral_object.kih_ti
      - name: target_resource_name_bbridge_collateral
        default: ceh.idl.bbridge_collateral.kih_ti
      - name: target_resource_name_bbridge_collateral_object
        default: ceh.idl.bbridge_collateral_object.kih_ti
      - name: target_table_name_blink_collateral_object_x_collateral
        default: idl.blink_collateral_object_x_collateral
      - name: target_table_name_sal_collateral
        default: idl.sal_collateral
      - name: target_table_name_sal_collateral_object
        default: idl.sal_collateral_object
      - name: target_table_name_bbridge_collateral
        default: idl.bbridge_collateral
      - name: target_table_name_bbridge_collateral_object
        default: idl.bbridge_collateral_object
      - name: source_resource_names
        default:
          - ceh.rdv.mart_collateral_ref_collateral_h_kih
          - ceh.rdv.mart_collateral_object_ref_collateral_object_h_kih
    
      - name: src_version_id
        datatype: int
      - name: stable_version_id
        datatype: int
      - name: init_load_flg
        datatype: bool
      - name: instance_id
        datatype: int
      - name: algorithm_name
        datatype: str
      - name: kih_actual_dttm
        datatype: str
        default: '1900-01-01T00:00:00'

    tasks:
      - id: check_job_running
        type: ceh_core_idl.app.operators.core.check_job_running
        description: Проверка состояние текущего дага
      
      - id: get_load_id
        type: ceh_core_idl.app.operators.etl.generate_load_id_operator
        description: Генерирация идентификатора по целевой таблице
        properties:
          instance_id: ${instance_id}
          table_name: ${target_resource_name_blink_collateral_object_x_collateral}
        sequencer:
          dependencies:
            - ref: check_job_running
      - id: get_load_id_collateral
        type: ceh_core_idl.app.operators.etl.generate_load_id_operator
        description: Генерирация идентификатора по целевой таблице
        properties:
          instance_id: ${instance_id}
          table_name: ${target_resource_name_bbridge_collateral}
        sequencer:
          dependencies:
            - ref: check_job_running
      - id: get_load_id_collateral_object
        type: ceh_core_idl.app.operators.etl.generate_load_id_operator
        description: Генерирация идентификатора по целевой таблице
        properties:
          instance_id: ${instance_id}
          table_name: ${target_resource_name_bbridge_collateral_object}
        sequencer:
          dependencies:
            - ref: check_job_running

      # Забор данных основных витрин со слоя RDV
      - id: fn_get_mart_collateral_ref_collateral_h_kih
        type: ceh_core_idl.app.operators.etl.get_snapshot_operator
        description: Захват дельты марта со срезовой историчностью.
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        sequencer:
          dependencies:
            - ref: get_load_id
        properties:
          source_dataset:
            schema: rdv
            name: mart_collateral_ref_collateral_h_kih
            useful_columns:
             - collateral_rk
             - cllh_clo_rk
             - case when effective_from_date < to_date ('01.01.1900', 'dd.mm.yyyy') then to_date ('01.01.1900', 'dd.mm.yyyy') else effective_from_date end as effective_from_date
             - case when effective_to_date >= to_date ('31.12.2999', 'dd.mm.yyyy') then to_date ('31.12.2999', 'dd.mm.yyyy') else effective_to_date + interval '1' day end as effective_to_date
             - cllh_is_next_pawn_flg
          version_id: ${stable_version_id}
          filter_expression: cllh_source = 'S05'
          result_dataset:
            schema: stg
            name: t_st_mart_collateral_${get_load_id.load_id}
            physical_options:
              materialize_flg: true
              distribute_type: column
              distribute_keys:
                - collateral_rk

      - id: fn_get_mart_collateral_object_ref_collateral_object_h_kih 
        type: ceh_core_idl.app.operators.etl.get_increment_operator
        description: Захват дельты марта со срезовой историчностью.
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        sequencer:
          dependencies:
            - ref: get_load_id
        properties:
          source_dataset:
            schema: rdv
            name: mart_collateral_object_ref_collateral_object_h_kih 
            useful_columns:
             - collateral_object_rk
             - case when effective_from_date < to_date ('01.01.1900', 'dd.mm.yyyy') then to_date ('01.01.1900', 'dd.mm.yyyy') else effective_from_date end as effective_from_date
             - case when effective_to_date >= to_date ('31.12.2999', 'dd.mm.yyyy') then to_date ('31.12.2999', 'dd.mm.yyyy') else effective_to_date + interval '1' day end as effective_to_date
          from_version_id: ${src_version_id}
          to_version_id: ${stable_version_id}
          filter_expression: cloh_source = 'S05' AND cloh_collateral_subtype_cd NOT IN ('1803', '56', '19', '59', '35', '34', '-1', '18')
          result_dataset:
            schema: stg
            name: t_st_mart_object_${get_load_id.load_id}
            physical_options:
              materialize_flg: true
              distribute_type: column
              distribute_keys:
                - collateral_object_rk

      # Соединение витрин со слоя RDV с полной историчностью
      - id: join_marts 
        description: Соединение таблиц
        type: ceh_core_idl.app.operators.etl.join_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        sequencer:
          dependencies:
            - ref: fn_get_mart_collateral_ref_collateral_h_kih
            - ref: fn_get_mart_collateral_object_ref_collateral_object_h_kih
        properties:
          main_dataset:
            schema: stg
            name: t_st_mart_collateral_${get_load_id.load_id}
            primary_keys:
              - collateral_rk
            useful_columns:
              - collateral_rk
              - cllh_clo_rk as collateral_object_rk
              - cllh_is_next_pawn_flg
          history_type: full
          join_dataset:
            schema: stg
            name: t_st_mart_object_${get_load_id.load_id}
            history_type: full
            main_join_columns:
              - cllh_clo_rk
            slave_join_columns:
              - collateral_object_rk
            join_type: inner
          result_dataset:
            schema: stg
            name: t_st_mart_${get_load_id.load_id}
            physical_options:
              materialize_flg: true
              distribute_type: column
              distribute_keys:
                - collateral_rk

      # Генерация ключей BDM
      - id: gen_bdm_key_collateral
        description: "Получение ключа bdm для sal_collateral для первичного ключа витрины"
        type: ceh_core_idl.app.operators.etl.get_bdm_key_operator #Функция необходима для получения ключей bdm.
        sequencer:
          dependencies:
            - ref: join_marts
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: stg
            name: t_st_mart_${get_load_id.load_id} #Имя dataset
            useful_columns: #Cписок значимых полей
              - collateral_rk
          mapping_bdm_datasets:
            source_key: collateral_rk
            same_link_dataset: #Same-as-link dataset
              schema: rdv_hub_collateral
              name: sal_collateral
          stable_version_id: ${stable_version_id}
          same_link_check_flag: true
          final_expression: "DISTINCT collateral_rk as secondary_rk, tgt_collateral_rk as primary_rk, 'KIH_TI'::TEXT as src_cd, 'rdv_hub_collateral'::TEXT as bk_schema_cd, false::BOOLEAN as deleted_flg"
          result_dataset:
            schema: stg
            name: t_st_sal_collateral_${get_load_id.load_id}
            physical_options:
              materialize_flg: true
              distribute_keys:
                - secondary_rk
              distribute_type: column

      - id: gen_bdm_key_collateral_object
        description: "Получение ключа bdm для sal_collateral_object"
        type: ceh_core_idl.app.operators.etl.get_bdm_key_operator #Функция необходима для получения ключей bdm.
        sequencer:
          dependencies:
            - ref: join_marts
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: stg
            name: t_st_mart_${get_load_id.load_id} #Имя dataset
            useful_columns: #Cписок значимых полей
              - collateral_object_rk
          mapping_bdm_datasets:
            source_key: collateral_object_rk
            same_link_dataset: #Same-as-link dataset
              schema: rdv_hub_collateral_object
              name: sal_collateral_object
          stable_version_id: ${stable_version_id}
          same_link_check_flag: true
          final_expression: "DISTINCT collateral_object_rk as secondary_rk, tgt_collateral_object_rk as primary_rk, 'KIH_TI'::TEXT as src_cd, 'rdv_hub_collateral_object'::TEXT as bk_schema_cd, false::BOOLEAN as deleted_flg"
          result_dataset:
            schema: stg
            name: t_st_sal_collateral_object_${get_load_id.load_id}
            physical_options:
              materialize_flg: true
              distribute_keys:
                - secondary_rk
              distribute_type: column

      # Соединение с салами, преобразование неключевого атрибута и добавление технических полей
      - id: task_finalization
        description: "Соединение таблиц"
        type: ceh_core_idl.app.operators.etl.create_sql_proc_operator
        sequencer:
          dependencies:
            - ref: gen_bdm_key_collateral
            - ref: gen_bdm_key_collateral_object
          condition: all
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          sql_select_statement: SELECT 
                                  sal_col.primary_rk as collateral_rk,
                                  sal_obj.primary_rk as collateral_object_rk,
                                  effective_from_date,
                                  effective_to_date,
                                  CASE 
                                    WHEN cllh_is_next_pawn_flg IN ('Y', '1')
                                      THEN true
                                    WHEN cllh_is_next_pawn_flg IN ('N', '0')
                                      THEN false
                                    ELSE NULL
                                  END::BOOLEAN as next_collateral_flg,
                                  'KIH_TI'::TEXT as src_cd,
                                  false::BOOLEAN as deleted_flg
                                FROM
                                  stg.t_st_mart_${get_load_id.load_id} mart
                                JOIN 
                                  stg.t_st_sal_collateral_${get_load_id.load_id} sal_col ON mart.collateral_rk = sal_col.secondary_rk
                                JOIN 
                                  stg.t_st_sal_collateral_object_${get_load_id.load_id} sal_obj ON mart.collateral_object_rk = sal_obj.secondary_rk
          result_dataset:
            schema: stg
            name: t_st_pre_final_${get_load_id.load_id}
            physical_options:
              distribute_keys:
                - collateral_rk
                - collateral_object_rk
              distribute_type: column
              materialize_flg: true

      # Схлопывание истории для целевой таблицы
      - id: fn_get_hist_merge
        description: схлопывание истории 
        type: ceh_core_idl.app.operators.etl.history_merge_operator
        sequencer: # поле определения последоавтельности запуска таски
          dependencies: # перечень тасок от результата которых зависит запуск текущей таски, в данном  случае все перечисленные таски должны успешно выполниться
            - ref: task_finalization
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: stg
            name: t_st_pre_final_${get_load_id.load_id}
            primary_keys:
              - collateral_rk
              - collateral_object_rk
            useful_columns:
              - next_collateral_flg
              - src_cd
              - deleted_flg
          hash_calculation_flg: true
          hash_columns:
            - collateral_rk
            - collateral_object_rk
            - next_collateral_flg
            - deleted_flg
          result_dataset:
            schema: stg
            name: t_st_final_${get_load_id.load_id}
            physical_options:
              distribute_keys:
                - collateral_rk
                - collateral_object_rk
              distribute_type: column
              materialize_flg: true

      # Загрузка sal + bbridge для collateral
      - id: open_transaction_collateral # Шаг открытия транзакции. Шаг обязателен
        description: Открытие транзакции
        type: ceh_core_idl.app.operators.services.create_tx_operator
        metadata:
          - name: tx_uid
            datatype: str
            type: out
          - name: tx_token
            datatype: str
            type: out
        sequencer:
          dependencies:
            - ref: get_load_id_collateral
            - ref: fn_get_hist_merge
      - id: block_resources_collateral # Шаг блокировки ресурса или группы ресурсов. Шаг обязателен
        description: Блокировка ресурса
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transaction_collateral.tx_uid}
          target_resource_names:
            - ${target_resource_name_bbridge_collateral}
            - ${target_resource_name_sal_collateral}
        sequencer:
          dependencies:
            - ref: open_transaction_collateral
      - id: load_delta_bbridge_collateral # Шаг загрузки данных, не обязателен, можно использовать свой шаг или набор шагов
        description: Загрузка дельты sal (функция %_dlt_load)
        type: ceh_core_idl.app.operators.txi.load_dummy_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: stg
            name: t_st_sal_collateral_${get_load_id.load_id}
            business_keys:
              - primary_rk
          target_dataset:
            schema: idl
            name: bbridge_collateral
            primary_keys:
              - collateral_rk
          load_id: ${get_load_id_collateral.load_id}
          source_code: 'KIH_TI'
          current_version_id: ${block_resources_collateral.version_id}
          stable_version_id: ${stable_version_id}
          result_dataset:
            schema: idl_dlt
            name: t_dlt_lg_${get_load_id_collateral.load_id}
        sequencer:
          dependencies:
            - ref: block_resources_collateral
      - id: load_delta_sal_collateral
        description: Загрузка дельты (функция %_dlt_load)
        type: ceh_core_idl.app.operators.txi.load_dummy_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: stg
            name: t_st_sal_collateral_${get_load_id.load_id}
            business_keys:
              - secondary_rk
          target_dataset:
            schema: idl
            name: sal_collateral
            primary_keys:
              - secondary_rk
          load_id: sal_${get_load_id_collateral.load_id}
          #source_code: 'KIH_TI'
          current_version_id: ${block_resources_collateral.version_id}
          stable_version_id: ${stable_version_id}
          same_link_flg: true
          business_key_schema: rdv_hub_collateral
          result_dataset:
            schema: idl_dlt
            name: t_dlt_lg_sal_${get_load_id_collateral.load_id}
        sequencer:
          dependencies:
            - ref: block_resources_collateral
      - id: update_resource_state_bbridge_collateral # Шаг обновления состояния у целевого ресурса. Шаг обязателен
        description: Обновление состояния целевого ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transaction_collateral.tx_uid}
          common_version_id: ${stable_version_id}
          target_resource_cd: ${target_resource_name_bbridge_collateral}
          target_dataset:
            schema: idl
            name: bbridge_collateral
          load_id: ${get_load_id_collateral.load_id}
          algorithm_uid: ${algorithm_name}
          delta_dataset: idl_dlt.t_dlt_lg_${get_load_id_collateral.load_id}
        sequencer:
          dependencies:
            - ref: load_delta_bbridge_collateral
      - id: update_resource_state_sal_collateral # Шаг обновления состояния у целевого ресурса. Шаг обязателен
        description: Обновление состояния целевого ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transaction_collateral.tx_uid}
          common_version_id: ${stable_version_id}
          target_resource_cd: ${target_resource_name_sal_collateral}
          target_dataset:
            schema: idl
            name: sal_collateral
          load_id: sal_${get_load_id_collateral.load_id}
          algorithm_uid: ${algorithm_name}
          delta_dataset: idl_dlt.t_dlt_lg_sal_${get_load_id_collateral.load_id}
        sequencer:
          dependencies:
            - ref: load_delta_sal_collateral
      - id: commit_collateral # Коммит целевого ресурса. Шаг обязателен
        description: Коммит целевого ресурса
        type: ceh_core_idl.app.operators.services.commit_transaction_operator
        properties:
          transaction_uid: ${open_transaction_collateral.tx_uid}
          transaction_token: ${open_transaction_collateral.tx_token}
        sequencer:
          dependencies:
            - ref: update_resource_state_bbridge_collateral
            - ref: update_resource_state_sal_collateral
      - id: error_occurred_collateral # Выявление ошибок выполнения дага. Шаг обязателен
        description: Любой успех
        type: airflow.operators.dummy.DummyOperator
        sequencer:
          dependencies:
            - ref: update_resource_state_bbridge_collateral
              condition: ${not update_resource_state_bbridge_collateral.status}
            - ref: update_resource_state_sal_collateral
              condition: ${not update_resource_state_sal_collateral.status}       
          condition:
            any
      - id: rollback_collateral # Выполнение роллбека транзакции. Шаг обязателен
        description: Роллбэк операция
        type: ceh_core_idl.app.operators.services.rollback_transaction_operator
        properties:
          transaction_uid: ${open_transaction.tx_uid}
          transaction_token: ${open_transaction.tx_token}
        sequencer:
          dependencies:
            - ref: error_occurred_collateral
            - ref: open_transaction_collateral
          condition:
            all

      # Загрузка sal + bbridge для collateral_object
      - id: open_transaction_collateral_object # Шаг открытия транзакции. Шаг обязателен
        description: Открытие транзакции
        type: ceh_core_idl.app.operators.services.create_tx_operator
        metadata:
          - name: tx_uid
            datatype: str
            type: out
          - name: tx_token
            datatype: str
            type: out
        sequencer:
          dependencies:
            - ref: get_load_id_collateral_object
            - ref: fn_get_hist_merge
      - id: block_resources_collateral_object # Шаг блокировки ресурса или группы ресурсов. Шаг обязателен
        description: Блокировка ресурса
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transaction_collateral_object.tx_uid}
          target_resource_names:
            - ${target_resource_name_bbridge_collateral_object}
            - ${target_resource_name_sal_collateral_object}
        sequencer:
          dependencies:
            - ref: open_transaction_collateral_object
      - id: load_delta_bbridge_collateral_object # Шаг загрузки данных, не обязателен, можно использовать свой шаг или набор шагов
        description: Загрузка дельты sal (функция %_dlt_load)
        type: ceh_core_idl.app.operators.txi.load_dummy_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: stg
            name: t_st_sal_collateral_object_${get_load_id.load_id}
            business_keys:
              - primary_rk
          target_dataset:
            schema: idl
            name: bbridge_collateral_object
            primary_keys:
              - collateral_object_rk
          load_id: ${get_load_id_collateral_object.load_id}
          source_code: 'KIH_TI'
          current_version_id: ${block_resources_collateral_object.version_id}
          stable_version_id: ${stable_version_id}
          result_dataset:
            schema: idl_dlt
            name: t_dlt_lg_${get_load_id_collateral_object.load_id}
        sequencer:
          dependencies:
            - ref: block_resources_collateral_object
      - id: load_delta_sal_collateral_object
        description: Загрузка дельты (функция %_dlt_load)
        type: ceh_core_idl.app.operators.txi.load_dummy_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: stg
            name: t_st_sal_collateral_object_${get_load_id.load_id}
            business_keys:
              - secondary_rk
          target_dataset:
            schema: idl
            name: sal_collateral_object
            primary_keys:
              - secondary_rk
          load_id: sal_${get_load_id_collateral_object.load_id}
          #source_code: 'KIH_TI'
          current_version_id: ${block_resources_collateral_object.version_id}
          stable_version_id: ${stable_version_id}
          same_link_flg: true
          business_key_schema: rdv_hub_collateral_object
          result_dataset:
            schema: idl_dlt
            name: t_dlt_lg_sal_${get_load_id_collateral_object.load_id}
        sequencer:
          dependencies:
            - ref: block_resources_collateral_object
      - id: update_resource_state_bbridge_collateral_object # Шаг обновления состояния у целевого ресурса. Шаг обязателен
        description: Обновление состояния целевого ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transaction_collateral_object.tx_uid}
          common_version_id: ${stable_version_id}
          target_resource_cd: ${target_resource_name_bbridge_collateral_object}
          target_dataset:
            schema: idl
            name: bbridge_collateral_object
          load_id: ${get_load_id_collateral_object.load_id}
          algorithm_uid: ${algorithm_name}
          delta_dataset: idl_dlt.t_dlt_lg_${get_load_id_collateral_object.load_id}
        sequencer:
          dependencies:
            - ref: load_delta_bbridge_collateral_object
      - id: update_resource_state_sal_collateral_object # Шаг обновления состояния у целевого ресурса. Шаг обязателен
        description: Обновление состояния целевого ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transaction_collateral_object.tx_uid}
          common_version_id: ${stable_version_id}
          target_resource_cd: ${target_resource_name_sal_collateral_object}
          target_dataset:
            schema: idl
            name: sal_collateral_object
          load_id: sal_${get_load_id_collateral_object.load_id}
          algorithm_uid: ${algorithm_name}
          delta_dataset: idl_dlt.t_dlt_lg_sal_${get_load_id_collateral_object.load_id}
        sequencer:
          dependencies:
            - ref: load_delta_sal_collateral_object
      - id: commit_collateral_object # Коммит целевого ресурса. Шаг обязателен
        description: Коммит целевого ресурса
        type: ceh_core_idl.app.operators.services.commit_transaction_operator
        properties:
          transaction_uid: ${open_transaction_collateral_object.tx_uid}
          transaction_token: ${open_transaction_collateral_object.tx_token}
        sequencer:
          dependencies:
            - ref: update_resource_state_bbridge_collateral_object
            - ref: update_resource_state_sal_collateral_object
      - id: error_occurred_collateral_object # Выявление ошибок выполнения дага. Шаг обязателен
        description: Любой успех
        type: airflow.operators.dummy.DummyOperator
        sequencer:
          dependencies:
            - ref: update_resource_state_bbridge_collateral_object
              condition: ${not update_resource_state_bbridge_collateral_object.status}
            - ref: update_resource_state_sal_collateral_object
              condition: ${not update_resource_state_sal_collateral_object.status}       
          condition:
            any
      - id: rollback_collateral_object # Выполнение роллбека транзакции. Шаг обязателен
        description: Роллбэк операция
        type: ceh_core_idl.app.operators.services.rollback_transaction_operator
        properties:
          transaction_uid: ${open_transaction.tx_uid}
          transaction_token: ${open_transaction.tx_token}
        sequencer:
          dependencies:
            - ref: error_occurred_collateral_object
            - ref: open_transaction_collateral_object
          condition:
            all

      # Загрузка sal + bbridge для collateral
      - id: open_transaction # Шаг открытия транзакции. Шаг обязателен
        description: Открытие транзакции
        type: ceh_core_idl.app.operators.services.create_tx_operator
        metadata:
          - name: tx_uid
            datatype: str
            type: out
          - name: tx_token
            datatype: str
            type: out
        sequencer:
          dependencies:
            - ref: fn_get_hist_merge
      - id: block_resources # Шаг блокировки ресурса или группы ресурсов. Шаг обязателен
        description: Блокировка ресурса
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transaction.tx_uid}
          target_resource_names:
            - ${target_resource_name_blink_collateral_object_x_collateral}
        sequencer:
          dependencies:
            - ref: open_transaction
      - id: load_delta # Шаг загрузки данных, не обязателен, можно использовать свой шаг или набор шагов
        description: Загрузка дельты sal (функция %_dlt_load)
        type: ceh_core_idl.app.operators.txi.load_delta_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: stg
            name: t_st_final_${get_load_id.load_id}
            primary_keys:
              - collateral_rk
              - collateral_object_rk
          target_dataset:
            schema: idl
            name: blink_collateral_object_x_collateral
          load_id: ${get_load_id.load_id}
          current_version_id: ${block_resources.version_id}
          stable_version_id: ${stable_version_id}
          init_load_flg: false
          record_mode: "record_mode"
          snapshot_flg: false
          hash_calculation_flg: false
          p_rewrite_effective_from_flg: false
          result_dataset:
            schema: idl_dlt
            name: t_dlt_lg_${get_load_id.load_id}
        sequencer:
          dependencies:
            - ref: block_resources
      - id: update_resource_state # Шаг обновления состояния у целевого ресурса. Шаг обязателен
        description: Обновление состояния целевого ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transaction.tx_uid}
          common_version_id: ${stable_version_id}
          target_resource_cd: ${target_resource_name_blink_collateral_object_x_collateral}
          target_dataset:
            schema: idl
            name: blink_collateral_object_x_collateral
          load_id: ${get_load_id.load_id}
          algorithm_uid: ${algorithm_name}
          delta_dataset: idl_dlt.t_dlt_lg_${get_load_id.load_id}
          metrics: 
            kih_actual_dttm: ${kih_actual_dttm}
        sequencer:
          dependencies:
            - ref: load_delta
      - id: commit # Коммит целевого ресурса. Шаг обязателен
        description: Коммит целевого ресурса
        type: ceh_core_idl.app.operators.services.commit_transaction_operator
        properties:
          transaction_uid: ${open_transaction.tx_uid}
          transaction_token: ${open_transaction.tx_token}
        sequencer:
          dependencies:
            - ref: update_resource_state
      - id: error_occurred # Выявление ошибок выполнения дага. Шаг обязателен
        description: Любой успех
        type: airflow.operators.dummy.DummyOperator
        sequencer:
          dependencies:
            - ref: update_resource_state
              condition: ${not update_resource_state.status}     
          condition:
            any
      - id: rollback # Выполнение роллбека транзакции. Шаг обязателен
        description: Роллбэк операция
        type: ceh_core_idl.app.operators.services.rollback_transaction_operator
        properties:
          transaction_uid: ${open_transaction.tx_uid}
          transaction_token: ${open_transaction.tx_token}
        sequencer:
          dependencies:
            - ref: error_occurred
            - ref: open_transaction
          condition:
            all


