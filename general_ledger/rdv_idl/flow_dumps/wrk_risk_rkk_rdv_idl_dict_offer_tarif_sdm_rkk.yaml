schema_version: 2.0
# DummyOperator -> EmptyOperator
metadata:
  author: fd2
  version: '1.0'
  group: general_ledger
  description: Рабочий поток wrk_risk_rkk_rdv_idl_dict_offer_tarif_sdm_rkk
  tags: 
    - 'team: fd2'
    - wf
    - 'area: risk'
    - 'src: rkk'
    - 'prv: rdv'
    - 'tgt: idl'
    - 'cf: cf_risk_rkk_rdv_idl_dict_offer_tarif_sdm_rkk'
    - 'rls: 90'
    - 'story: FD2-2107'
    - 'entity: sdm_rkk.dict_offer_tarif_sdm_rkk'
flows:
  - id: wrk_risk_rkk_rdv_idl_dict_offer_tarif_sdm_rkk
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Рабочий поток wrk_risk_rkk_rdv_idl_dict_offer_tarif_sdm_rkk
    metadata:
      - name: target_resource_name
        datatype: str
        default: ceh.idl.dict_offer_tarif_sdm_rkk
      - name: source_resource_names
        default: 
          - ceh.rdv.mart_rbsddot_sdm_rkk
      - name: target_table_name
        datatype: str
        default: idl.dict_offer_tarif_sdm_rkk
      - name: src_version_id
        datatype: int
      - name: stable_version_id
        datatype: int
      - name: init_load_flg
        datatype: bool
      - name: instance_id
        datatype: int
      - name: algorithm_uid
        datatype: str
    tasks:
      - id: check_job_running
        type: ceh_core_libs.steps.core.check_job_running_step
        description: Проверка состояния текущего дага
      - id: get_load_id
        type: ceh_core_idl.app.operators.etl.generate_load_id_operator
        description: Генерация идентификатора по целевой таблице
        properties:
          instance_id: ${instance_id}
          table_name: ${target_table_name}
        sequencer:
          dependencies:
            - ref: check_job_running
      - id: db_prepare_data
        description: ETL process
        builder: ceh_core_idl.app.builders.include_local_flow_builder
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          ref: db_prepare_data
          properties:
            load_id: ${get_load_id.load_id}
            stable_version_id: ${stable_version_id}
            src_version_id: ${src_version_id}
        sequencer:
          dependencies:
            - ref: get_load_id
      - id: dummy_step
        type: airflow.operators.empty.EmptyOperator
        description: Пустой шаг
        sequencer:
          dependencies:
            - ref: db_prepare_data
      - id: open_transaction_bbridge
        type: ceh_core_idl.app.operators.services.create_tx_operator
        description: Открытие транзакции
        metadata:
          - name: tx_uid
            datatype: str
            type: out
          - name: tx_token
            datatype: str
            type: out
        sequencer:
          dependencies:
            - ref: dummy_step
      - id: block_resources_bbridge
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        description: Блокировка ресурсов
        properties:
          transaction_uid: ${open_transaction_bbridge.tx_uid}
          target_resource_names:
            - ${target_resource_name}
        sequencer:
          dependencies:
            - ref: open_transaction_bbridge
      - id: load_delta_bbridge
        type: ceh_core_idl.app.operators.txi.load_delta_operator
        description: Загрузка дельты
        properties:
          source_dataset:
            schema: stg
            name: t_st_fin_${get_load_id.load_id}
            business_keys:
              - id
            primary_keys:
              - id
          target_dataset:
            schema: idl
            name: dict_offer_tarif_sdm_rkk
          load_id: ${get_load_id.load_id}
          current_version_id: ${block_resources_bbridge.version_id}
          stable_version_id: ${stable_version_id}
          init_load_flg: ${init_load_flg}
          snapshot_flg: false
          window_columns: null
          hash_calculation_flg: true
          window_filter_expression: null
          stage_schema: stg
          rewrite_effective_from_flg: false
          use_accessor_flg: true
          mas_dictionary_flg: false
        sequencer:
          dependencies:
            - ref: block_resources_bbridge
      - id: update_resource_state_bbridge
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        description: Обновление состояния ресурса
        properties:
          transaction_uid: ${open_transaction_bbridge.tx_uid}
          common_version_id: ${stable_version_id}
          target_resource_cd: ${target_resource_name}
          target_dataset:
            schema: idl
            name: dict_offer_tarif_sdm_rkk
          load_id: ${get_load_id.load_id}
          delta_dataset: ${load_delta_bbridge.result_dataset}
          algorithm_uid: ${algorithm_uid}
        sequencer:
          dependencies:
            - ref: load_delta_bbridge
      - id: error_occurred
        type: airflow.operators.empty.EmptyOperator
        description: Любая ошибка
        sequencer:
          dependencies:
            - ref: update_resource_state_bbridge
              condition: ${not update_resource_state_bbridge.status}
          condition: any
      - id: rollback
        type: ceh_core_idl.app.operators.services.rollback_transaction_operator
        description: Ролбэк операция
        properties:
          transaction_uid: ${open_transaction_bbridge.tx_uid}
          transaction_token: ${open_transaction_bbridge.tx_token}
        sequencer:
          dependencies:
            - ref: error_occurred
            - ref: open_transaction_bbridge
      - id: commit
        type: ceh_core_idl.app.operators.services.commit_transaction_operator
        description: Коммит операция
        properties:
          transaction_uid: ${open_transaction_bbridge.tx_uid}
          transaction_token: ${open_transaction_bbridge.tx_token}
          check_delta_flg: false
        sequencer:
          dependencies:
            - ref: update_resource_state_bbridge
      - id: check_delta
        type: ceh_core_idl.app.operators.services.delta_expectation_operator
        description: Проверка наката дельты
        properties:
          current_version_id: ${block_resources_bbridge.version_id}
        sequencer:
          dependencies:
            - ref: commit
            
  - id: db_prepare_data
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Захват инкрементов и срезов таблиц
    metadata:
      - name: load_id
        datatype: str
        type: in
      - name: stable_version_id
        datatype: int
        type: in
      - name: src_version_id
        datatype: int
        type: in
    tasks:
      - id: get_increment_mart_rbsddot_sdm_rkk
        description: Захват инкремента таблицы rdv.mart_rbsddot_sdm_rkk
        type: ceh_core_idl.app.operators.etl.get_increment_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: rdv
            name: mart_rbsddot_sdm_rkk
            useful_columns:
              - deleted_flg
              - type
              - system
              - subsys
              - src_cd
              - id_dk
          from_version_id: ${src_version_id}
          to_version_id: ${stable_version_id}
          result_dataset:
            schema: stg
            name: t_zd_mart_${load_id}
            physical_options:
              distribute_keys:
                - id_dk
              materialize_flg: true
              distribute_type: column
      - id: db_standard_data
        description: Стандартизация
        type: ceh_core_idl.app.operators.etl.create_sql_proc_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties: 
          sql_select_statement: select deleted_flg,
              type,
              system,
              subsys,
              src_cd,
              id_dk as id
              from stg.t_zd_mart_${load_id}
          result_dataset:
            schema: stg
            name: t_st_fin_${load_id}
            # physical_options:
            #   distribute_keys: []
                # - id_id
              # distribute_type: column
              # materialize_flg: false
        sequencer:
          dependencies:
            - ref: get_increment_mart_rbsddot_sdm_rkk
