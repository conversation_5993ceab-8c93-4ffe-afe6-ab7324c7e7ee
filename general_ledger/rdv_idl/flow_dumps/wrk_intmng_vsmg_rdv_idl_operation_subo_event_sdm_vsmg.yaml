schema_version: 2.0
metadata:
  author: bala<PERSON><PERSON><EMAIL>
  owner: 'Ба<PERSON><PERSON>шова Дарья Алексеевна(70184845)'
  version: "1.0"
  description: Рабочий поток IDL для загрузки в bbridge_operation_subo_event_sdm_vsmg
  tags:
    - 'team: zi22'
    - 'wrk'
    - 'area: intmng'
    - 'src: vsmg'
    - 'prv: rdv'
    - 'tgt: idl'
    - 'cf: cf_intmng_vsmg_rdv_idl_operation_subo_event_sdm_vsmg'
    - 'rls: 74'
    - 'entity: sdm.operation_subo_event_sdm_vsmg'
  group: zi22

flows:
  - id: wrk_intmng_vsmg_rdv_idl_operation_subo_event_sdm_vsmg
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Рабочий поток wrk_intmng_vsmg_rdv_idl_operation_subo_event_sdm_vsmg
    metadata:
      - name: target_resource_name_bbridge_operation_subo_event_sdm_vsmg
        type: in
        default: ceh.idl.bbridge_operation_subo_event_sdm_vsmg.vsmg
      - name: target_table_name_bbridge_operation_subo_event_sdm_vsmg
        type: in
        default: idl.bbridge_operation_subo_event_sdm_vsmg
      - name: source_resource_names
        type: in
        default:
          - ceh.rdv.mart_uasp_events_vsmg
      - name: instance_id
        datatype: int
        type: in
      - name: src_version_id
        datatype: int
        type: in
      - name: stable_version_id
        datatype: int
        type: in
      - name: algo_name
        type: in
        datatype: str
        default: wrk_intmng_vsmg_rdv_idl_operation_subo_event_sdm_vsmg
    tasks:
      - id: check_job_running
        description: Проверка состояние текущего дага
        type: ceh_core_idl.app.operators.core.check_dag_running
      - id: get_datetime
        type: ceh_core_idl.app.operators.core.sql_operator
        description: Получение текущего времени
        metadata:
          - name: result
            datatype: dict
            type: out
        properties:
          query: |-
            select current_timestamp::text as c_dttm
          result_flg: true
          result_limit: 1
        sequencer:
          dependencies:
            - ref: check_job_running
      - id: get_load_id_bbridge_operation_subo_event_sdm_vsmg
        type: ceh_core_idl.app.operators.etl.generate_load_id_operator
        description: Генерация идентификатора по целевой таблице
        properties:
          instance_id: ${instance_id}
          table_name: ${target_table_name_bbridge_operation_subo_event_sdm_vsmg + get_datetime.result.response[0].c_dttm}
        sequencer:
          dependencies:
            - ref: get_datetime
      - id: db_prepare_source_data
        description: Захват и преобразование данных источника
        builder: ceh_core_idl.app.builders.include_local_flow_builder
        properties:
          ref: db_prepare_source_data
          properties:
            load_id: ${get_load_id_bbridge_operation_subo_event_sdm_vsmg.load_id}
            stable_version_id: ${stable_version_id}
            src_version_id: ${src_version_id}
        sequencer:
          dependencies:
            - ref: get_load_id_bbridge_operation_subo_event_sdm_vsmg
      - id: db_prepare_delta
        description: Объединение дельт, выравнивание истории и докодировка типов связи договоров
        builder: ceh_core_idl.app.builders.include_local_flow_builder
        properties:
          ref: db_prepare_delta
          properties:
            load_id: ${get_load_id_bbridge_operation_subo_event_sdm_vsmg.load_id}
            stable_version_id: ${stable_version_id}
            src_version_id: ${src_version_id}
        sequencer:
          dependencies:
            - ref:  db_prepare_source_data
      - id: db_load_targets
        description: Подготовка данных используя функцию трансформаций
        builder: ceh_core_idl.app.builders.include_local_flow_builder
        properties:
          ref: db_load_targets
          properties:
            stable_version_id: ${stable_version_id}
            src_version_id: ${src_version_id}
            algo_name: ${algo_name}
            load_id: ${get_load_id_bbridge_operation_subo_event_sdm_vsmg.load_id}
            load_id_bbridge_operation_subo_event_sdm_vsmg: ${get_load_id_bbridge_operation_subo_event_sdm_vsmg.load_id}
            target_resource_name_bbridge_operation_subo_event_sdm_vsmg: ${target_resource_name_bbridge_operation_subo_event_sdm_vsmg}
            target_table_name_bbridge_operation_subo_event_sdm_vsmg: ${target_table_name_bbridge_operation_subo_event_sdm_vsmg}
            source_resource_names: ${source_resource_names}
        sequencer:
          dependencies:
            - ref: db_prepare_delta
  - id: db_prepare_source_data
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Подготовка данных mart_uasp_events_vsmg
    metadata:
      - name: load_id
        datatype: str
        type: in
      - name: stable_version_id
        datatype: int
        type: in
      - name: src_version_id
        datatype: int
        type: in
    tasks:
      - id: get_inc_rdv_mart_uasp_events_vsmg
        description: Захват инкремента таблицы rdv.mart_uasp_events_vsmg
        type: ceh_core_idl.app.operators.etl.get_increment_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          source_dataset:
            schema: rdv
            name: mart_uasp_events_vsmg
            primary_keys:
              - event_id
            useful_columns:
              - event_id
              - mdm_id
              - client_rk
              - event_dttm
              - channel
              - category
              - operation
              - online_offer
              - login
              - point
              - employee_rk
              - hdp_processed_dttm
              - employee_num
              - deleted_flg
              - effective_dttm
          from_version_id: ${src_version_id}
          to_version_id: ${stable_version_id}
          use_accessor_flg: false
          result_dataset:
            schema: stg
            name: t_inc_1_${load_id}
            physical_options:
              distribute_keys:
                - event_id
              materialize_flg: true
              distribute_type: column
  - id: db_prepare_delta
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Подготовка дельты
    metadata:
      - name: load_id
        datatype: str
        type: in
      - name: algo_name
        datatype: str
        type: in
      - name: stable_version_id
        datatype: int
        type: in
      - name: src_version_id
        datatype: int
        type: in
    tasks:
      - id: get_fin_bbridge_operation_subo_event_sdm_vsmg
        description: Формирование таблицы ключей bbridge_operation_subo_event_sdm_vsmg
        type: ceh_core_idl.app.operators.etl.create_sql_proc_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          sql_select_statement: |
            select
                t1.mdm_id::text                        AS  customer_mdm_id
              , t1.client_rk::bigint                   AS  counterparty_rk
              , t1.event_id::text                      AS  event_dk
              , t1.event_dttm::timestamp               AS  event_dttm
              , t1.channel::text                       AS  channel_name
              , t1.category::text                      AS  category_name
              , t1.operation::text                     AS  operation_name
              , t1.online_offer::boolean               AS  is_online_offer_flg
              , t1.login::text                         AS  login_name 
              , t1.point::text                         AS  internal_org_id 
              , t1.event_dttm::timestamp               AS  business_dttm 
              , t1.employee_rk::bigint                 AS  employee_rk  
              , t1.employee_num::text                  AS  employee_num 
              , 'VSMG'::text                           AS  src_cd
              , t1.effective_dttm
              , t1.deleted_flg
            from stg.t_inc_1_${load_id} t1
          result_dataset:
            schema: stg
            name: t_pre_1_${load_id}
            physical_options:
              distribute_keys:
                - event_dk
              materialize_flg: true
              distribute_type: column
  - id: db_load_targets
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Подготовка дельты
    metadata:
      - name: stable_version_id
        datatype: int
        type: in
      - name: src_version_id
        datatype: int
        type: in
      - name: algo_name
        datatype: str
        type: in
      - name: load_id
        datatype: str
        type: in
      - name: load_id_bbridge_operation_subo_event_sdm_vsmg
        datatype: str
        type: in
      - name: target_resource_name_bbridge_operation_subo_event_sdm_vsmg
        type: in
      - name: target_table_name_bbridge_operation_subo_event_sdm_vsmg
        type: in
      - name: source_resource_names
        type: in
    tasks:
      - id: open_transaction_operation_subo_event
        description: Открытие транзакции
        type: ceh_core_idl.app.operators.services.create_tx_operator
        metadata:
          - name: tx_uid
            datatype: str
            type: out
          - name: tx_token
            datatype: str
            type: out
      - id: block_resource_operation_subo_event
        description: блокировка всех ресурсов
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transaction_operation_subo_event.tx_uid}
          target_resource_names:
            - ${target_resource_name_bbridge_operation_subo_event_sdm_vsmg}
        sequencer:
          dependencies:
            - ref: open_transaction_operation_subo_event
      - id: load_delta_bbridge_operation_subo_event_sdm_vsmg
        description: Дельта bbridge
        type: ceh_core_idl.app.operators.txi.load_delta_operator
        properties:
          source_dataset:
            schema: stg
            name: t_pre_1_${load_id}
            business_keys:
              - event_dk
          target_dataset:
            schema: idl
            name: bbridge_operation_subo_event_sdm_vsmg
          load_id: ${load_id_bbridge_operation_subo_event_sdm_vsmg}
          current_version_id: ${block_resource_operation_subo_event.version_id}
          stable_version_id: ${stable_version_id}
          snapshot_flg: false
          hash_calculation_flg: true
        sequencer:
          dependencies:
            - ref: block_resource_operation_subo_event
      - id: update_resource_state_bbridge_operation_subo_event_sdm_vsmg
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        description: Обновление состояния ресурса bbridge
        properties:
          transaction_uid: ${open_transaction_operation_subo_event.tx_uid}
          stable_version_id: ${stable_version_id}
          target_resource_name: ${target_resource_name_bbridge_operation_subo_event_sdm_vsmg}
          target_table_names:
            - ${target_table_name_bbridge_operation_subo_event_sdm_vsmg}
          source_resource_names: ${source_resource_names}
          max_src_version_id: ${src_version_id}
          load_id: ${load_id_bbridge_operation_subo_event_sdm_vsmg}
          delta_dataset: ${load_delta_bbridge_operation_subo_event_sdm_vsmg.result_dataset}
          algorithm_name: ${algo_name}
        sequencer:
          dependencies:
            - ref: load_delta_bbridge_operation_subo_event_sdm_vsmg
      - id: commit_operation_subo_event
        description: Коммит операция
        type: ceh_core_idl.app.operators.services.commit_transaction_operator
        properties:
          transaction_uid: ${open_transaction_operation_subo_event.tx_uid}
          transaction_token: ${open_transaction_operation_subo_event.tx_token}
        sequencer:
          dependencies:
            - ref: update_resource_state_bbridge_operation_subo_event_sdm_vsmg
      - id: error_occurred_operation_subo_event
        description: Любой успех
        type: airflow.operators.dummy.DummyOperator
        sequencer:
          dependencies:
            - ref: update_resource_state_bbridge_operation_subo_event_sdm_vsmg
              condition: ${not update_resource_state_bbridge_operation_subo_event_sdm_vsmg.status}
          condition:
            any
      - id: rollback_application
        description: Ролбэк операция
        type: ceh_core_idl.app.operators.services.rollback_transaction_operator
        properties:
          transaction_uid: ${open_transaction_operation_subo_event.tx_uid}
          transaction_token: ${open_transaction_operation_subo_event.tx_token}
        sequencer:
          dependencies:
            - ref: error_occurred_operation_subo_event
            - ref: open_transaction_operation_subo_event
          condition:
            all