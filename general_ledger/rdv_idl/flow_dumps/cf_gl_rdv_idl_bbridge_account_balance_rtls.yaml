schema_version: 2.0
metadata:
  author: MZubko
  version: '1.0'
  group: test
  description: Управляющий поток cf_gl_rdv_idl_bbridge_account_balance_rtls
  tags:
    - 'team: zi2'
    - cf
    - 'area: gl'
    - 'src: rtls'
    - 'prv: rdv'
    - 'tgt: idl'
    - 'rls: 16'
    - 'entity: bdm.account_balance'
  imports:
    - ceh_frm_idl_utils.source_resourse_transfer
flows:
  - id: cf_gl_rdv_idl_bbridge_account_balance_rtls
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Управляющий поток cf_gl_rdv_idl_bbridge_account_balance_rtls
    metadata:
      - name: target_resource_name_bbridge_account_balance
        default: ceh.idl.bbridge_account_balance.rtls
      - name: source_resource_names
        default:
          - ceh.idl.bbridge_account_turn.rtls
          - ceh.idl.bbridge_account_start_balance.rtls
          - ceh.idl.account.rtls
    tasks:
      - id: check_available_resources_in_ceh_provider_bbridge_account_balance
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        description: Проверка доступности ресурсов (источников и приемник) в провайдере
          ресурсов
        properties:
          target_resource_name: ${target_resource_name_bbridge_account_balance}
          source_resource_names: ${source_resource_names}
      - id: get_common_version
        type: ceh_core_idl.app.operators.services.common_version_operator
        description: Получение общей стабильной версии
        properties:
          resources: ${source_resource_names}
        sequencer:
          dependencies:
            - ref: check_available_resources_in_ceh_provider_bbridge_account_balance
      - id: get_resources_versions_bbridge_account_balance
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        description: Получение максимальной загруженной версии в целевой ресурс bbridge_account_balance
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
        properties:
          target_resource_name: ${target_resource_name_bbridge_account_balance}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: zi2_bbridge_acc_bal_rtls
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_workflow_parameters
        type: ceh_core_idl.app.operators.basic.workflow_parameters_operator
        description: Определение основных параметров рабочего потока
        properties:
          version_parameters:
            - ${get_resources_versions_bbridge_account_balance}
          stable_version: ${get_common_version.common_version}
        sequencer:
          dependencies:
            - ref: get_resources_versions_bbridge_account_balance
              condition: ${get_resources_versions_bbridge_account_balance.status and
                get_resources_versions_bbridge_account_balance.src_version_check_status}
      - id: get_metrics
        builder: ceh_core_idl.app.builders.include_flow_builder
        description: Приведение ресурсов к заданному виду для переноса в последующий
          слой
        properties:
          ref: source_resourse_transfer
          properties:
            source_resource_names: ${source_resource_names}
            target_resource_names:
              - ${target_resource_name_bbridge_account_balance}
            algorithm_uid: zi2_bbridge_acc_bal_rtls
        sequencer:
          dependencies:
            - ref: get_workflow_parameters
      - id: trigger_wrk
        type: ceh_core_idl.app.operators.core.trigger_dag_operator
        description: Запуск рабочего потока
        properties:
          trigger_dag_id: wrk_gl_rdv_idl_bbridge_account_balance_rtls
          conf:
            instance_id: ${get_workflow_parameters.instance_id}
            src_version_id: ${get_workflow_parameters.src_version_id}
            stable_version_id: ${get_workflow_parameters.stable_version_id}
            init_load_flg: false
            algorithm_name: zi2_bbridge_acc_bal_rtls
            by_src: ${get_metrics.result_data}
        sequencer:
          dependencies:
            - ref: get_metrics
