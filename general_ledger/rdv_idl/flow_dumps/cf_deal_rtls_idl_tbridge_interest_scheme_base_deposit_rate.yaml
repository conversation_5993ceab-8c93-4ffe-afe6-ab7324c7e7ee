schema_version: 2.0
metadata:
  author: filatovma
  version: '1.0'
  description: Управляющий поток сущности tbridge_interest_scheme_base_deposit_rate
  tags:
    - cf
    - 'team: ZI-8 (CRM-3)'
    - 'area: deal'
    - 'src: rtls'
    - 'prv: idl'
    - 'tgt: idl'
    - 'entity: idl.tbridge_interest_scheme_base_deposit_rate'
    - 'tgt-tbl: idl.tbridge_interest_scheme_base_deposit_rate'
    - 'src-tbl: idl.tbridge_interest_scheme_deposit_rate'
    - 'src-tbl: rdv.map_rate_float_type'
    - 'cf: cf_deal_rtls_idl_tbridge_interest_scheme_base_deposit_rate'
    - 'wrk: wrk_deal_rtls_idl_tbridge_interest_scheme_base_deposit_rate'
    - 'uid: 16.RTLS.ZI8.218.1'
    - 'rls: 64'
  group: zi8
  imports:
    - ceh_frm_idl_utils.source_resourse_transfer
flows:
  - id: cf_deal_rtls_idl_tbridge_interest_scheme_base_deposit_rate
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Управляющий поток сущности tbridge_interest_scheme_base_deposit_rate
    metadata:
      - name: main_workflow_name
        type: in
        default: wrk_deal_rtls_idl_tbridge_interest_scheme_base_deposit_rate
      - name: workflow_name
        type: in
        default: wrk_deal_rtls_idl_tbridge_interest_scheme_base_deposit_rate
      - name: target_resource_name
        type: in
        default: ceh.idl.tbridge_interest_scheme_base_deposit_rate.rtls
      - name: target_table_name
        type: in
        default: tbridge_interest_scheme_base_deposit_rate
      - name: source_resource_names
        type: in
        default:
          - ceh.idl.tbridge_interest_scheme_deposit_rate.rtls
          - ceh.rdv.map_rate_float_type
      - name: algo_name
        type: in
        default: 16.RTLS.ZI8.218.1
    tasks:
      - id: cf_deal_rtls_idl_tbridge_interest_scheme_base_deposit_rate
        description: Ветка 1 (успешная)
        builder: ceh_core_idl.app.builders.include_local_flow_builder
        properties:
          ref: tbridge_interest_scheme_base_deposit_rate
          properties:
            target_resource_name: ${target_resource_name}
            target_table_name: ${target_table_name}
            source_resource_names: ${source_resource_names}
            child_workflow: ${workflow_name}
            workflow_name: ${main_workflow_name}
  - id: tbridge_interest_scheme_base_deposit_rate
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Поток cf_deal_rtls_idl_tbridge_interest_scheme_base_deposit_rate
    metadata:
      - name: target_resource_name
        datatype: str
        type: in
      - name: target_table_name
        datatype: str
        type: in
      - name: source_resource_names
        datatype: list
        type: in
      - name: workflow_name
        datatype: str
        type: in
      - name: child_workflow
        datatype: str
        type: in
    tasks:
      - id: check_available_resources_in_ceh_provider
        description: Проверим test оператор с out параметрами
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        properties:
          target_resource_name: ${target_resource_name}
          source_resource_names: ${source_resource_names}
      - id: get_common_version
        description: Получаем стабильную версию
        type: ceh_core_idl.app.operators.services.common_version_operator
        metadata:
          - name: common_version_name
            datatype: str
            type: out
          - name: common_version
            datatype: int
            type: out
        properties:
          resources: ${source_resource_names}
        sequencer:
          dependencies:
            - ref: check_available_resources_in_ceh_provider
      - id: get_max_loaded_version
        description: Проверим test оператор с out параметрами
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        metadata:
          - name: max_loaded_version
            datatype: int
            type: out
          - name: date_last_download
            datatype: str
            type: out
        properties:
          target_resource_name: ${target_resource_name}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          condition: any
          debug_mode: true
          algo_name: 16.RTLS.ZI8.218.1
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_workflow_parameters
        description: Проверим test оператор с out параметрами
        type: ceh_core_idl.app.operators.basic.workflow_parameters_operator
        metadata:
          - name: instance_id
            datatype: int
            type: out
          - name: prev_data_load_dt
            datatype: str
            type: out
          - name: src_version_id
            datatype: int
            type: out
          - name: stable_version_id
            datatype: int
            type: out
        properties:
          workflow: ${workflow_name}
          version_parameters:
            - ${get_max_loaded_version}
          stable_version: ${get_common_version.common_version}
          child_workflow: ${child_workflow}
        sequencer:
          dependencies:
            - ref: get_max_loaded_version
              condition: ${get_max_loaded_version.status and get_max_loaded_version.src_version_check_status}
      - id: get_metrics
        builder: ceh_core_idl.app.builders.include_flow_builder
        description: Приведение ресурсов к заданному виду для переноса в последующий
          слой
        properties:
          ref: source_resourse_transfer
          properties:
            source_resource_names: ${source_resource_names}
            target_resource_names:
              - ${target_resource_name}
            algorithm_uid: 16.RTLS.ZI8.218.1
        sequencer:
          dependencies:
            - ref: get_workflow_parameters
      - id: trigger_wrk
        description: Проверим test оператор с out параметрами
        type: ceh_core_idl.app.operators.core.trigger_dag_operator
        properties:
          trigger_dag_id: wrk_deal_rtls_idl_tbridge_interest_scheme_base_deposit_rate
          conf:
            algorithm_uid: 16.RTLS.ZI8.218.1
            instance_id: ${get_workflow_parameters.instance_id}
            src_version_id: ${get_workflow_parameters.src_version_id}
            stable_version_id: ${get_workflow_parameters.stable_version_id}
            init_load_flg: false
            by_src: ${get_metrics.result_data}
        sequencer:
          dependencies:
            - ref: get_metrics
