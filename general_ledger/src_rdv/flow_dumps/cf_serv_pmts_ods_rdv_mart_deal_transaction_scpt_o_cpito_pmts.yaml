schema_version: '2.0'
metadata:
  author: CKB9 AYakovlev
  version: '1.0'
  description: Управляющий поток для wf_serv_pmts_ods_rdv_mart_deal_transaction_scpt_o_cpito_pmts
  tags:
  - 'team: CKB9'
  - 'author: <PERSON><PERSON><PERSON><PERSON><PERSON>'
  - 'area: serv'
  - 'src: pmts'
  - 'prv: ods'
  - 'tgt: rdv'
  - 'cf: cf_serv_pmts_ods_rdv_mart_deal_transaction_scpt_o_cpito_pmts'
  - 'wf: wf_serv_pmts_ods_rdv_mart_deal_transaction_scpt_o_cpito_pmts'
  - 'rls: 101'
  - 'tgt-tbl: rdv.mart_deal_transaction_scpt_o_cpito_pmts'
  - 'src-tbl: PMTS_SOH.H2_SCPT_O_CPITO'
  - 'uid: 35.PMTS.CKB9.54'
  - cf
  group: serv
  imports:
  - rdv_cf_uni_template.cf_uni_template
  main_flows:
  - cf_serv_pmts_ods_rdv_mart_deal_transaction_scpt_o_cpito_pmts
flows:
- id: cf_serv_pmts_ods_rdv_mart_deal_transaction_scpt_o_cpito_pmts
  description: Управляющий поток для wf_serv_pmts_ods_rdv_mart_deal_transaction_scpt_o_cpito_pmts
  builder: ceh_core_idl.app.builders.simple_flow_builder
  tasks:
  - id: run_wf_serv_pmts_ods_rdv_mart_deal_transaction_scpt_o_cpito_pmts
    builder: ceh_core_idl.app.builders.include_flow_builder
    properties:
      ref: cf_uni_template
      properties:
        mode: ANY
        actual_dttm_prefix: pmts
        work_flow_id: wf_serv_pmts_ods_rdv_mart_deal_transaction_scpt_o_cpito_pmts
        algos_map:
          35.PMTS.CKB9.54:
          - ceh: ceh.rdv.mart_deal_transaction_scpt_o_cpito_pmts
            uni: ods.PMTS_SOH.H2_SCPT_O_CPITO
