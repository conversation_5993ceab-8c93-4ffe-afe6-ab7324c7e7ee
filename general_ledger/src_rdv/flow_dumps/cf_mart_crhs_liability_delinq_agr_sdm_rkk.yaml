schema_version: 2.0
metadata:
  author: duganov
  version: "1.0"
  description: Управляющий поток для загрузки mart_crhs_liability_delinq_agr_sdm_rkk
  tags:
    - "team: zi13"
    - "cf"
    - "area: risk"
    - "src: RKK"
    - "src-tbl: h2_crhs_liability_delinq_agr"
    - "prv: ods"
    - "tgt: rdv"
    - "tgt-tbl: mart_crhs_liability_delinq_agr_sdm_rkk"
    - "cf: cf_mart_crhs_liability_delinq_agr_sdm_rkk"
    - "rls: 16"
  group: risk
  imports:
    - rdv_cf_uni_template.cf_uni_template
  main_flows:
    - cf_mart_crhs_liability_delinq_agr_sdm_rkk
flows:
  - id: cf_mart_crhs_liability_delinq_agr_sdm_rkk
    description: Тестовый поток с использованием шаблона
    builder: ceh_core_idl.app.builders.simple_flow_builder
    tasks:
      - id: cf_mart_crhs_liability_delinq_agr_sdm_rkk
        builder: ceh_core_idl.app.builders.include_flow_builder
        properties:
          ref: cf_uni_template
          properties:
            mode: ANY  # возможные значения: ANY, ALL
            actual_dttm_prefix: 'rkk'
            work_flow_id: wf_mart_crhs_liability_delinq_agr_sdm_rkk
            algos_map:
              33.RKK.ZI13.34: 
                - ceh: ceh.rdv.mart_crhs_liability_delinq_agr_sdm_rkk
                  uni: ods.RKK2_SOH.H2_CRHS_LIABILITY_DELINQ_AGR