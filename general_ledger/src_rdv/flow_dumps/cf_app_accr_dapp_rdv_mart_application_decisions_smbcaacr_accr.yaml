schema_version: '2.0'
metadata:
  author: zi14 VPesotsky
  version: '1.0'
  description: Управляющий поток для загрузки источника accr_smbcurrentaccountapplication_decisions_smbcaacr
  tags:
  - 'team: zi14'
  - cf
  - 'area: app'
  - 'src: accr'
  - 'src-tbl: accr_smbcurrentaccountapplication_decisions_smbcaacr'
  - 'prv: dapp'
  - 'tgt: rdv'
  - 'tgt-tbl: mart_application_decisions_smbcaacr_accr'
  - 'cf: cf_app_accr_dapp_rdv_mart_application_decisions_smbcaacr_accr'
  - 'rls: 21.1'
  - 'uid: 20.ACCR.ZI14.8'
  group: app
  imports:
  - rdv_cf_uni_template.cf_uni_template
  main_flows:
  - cf_app_accr_dapp_rdv_mart_application_decisions_smbcaacr_accr
flows:
- id: cf_app_accr_dapp_rdv_mart_application_decisions_smbcaacr_accr
  description: Основной поток для mart_application_decisions_smbcaacr_accr
  builder: ceh_core_idl.app.builders.simple_flow_builder
  tasks:
  - id: run_wf_app_accr_dapp_rdv_mart_application_decisions_smbcaacr_accr
    builder: ceh_core_idl.app.builders.include_flow_builder
    properties:
      ref: cf_uni_template
      properties:
        mode: ANY
        actual_dttm_prefix: accr
        work_flow_id: wf_app_accr_dapp_rdv_mart_application_decisions_smbcaacr_accr
        algos_map:
          20.ACCR.ZI14.8:
          - ceh: ceh.rdv.mart_application_decisions_smbcaacr_accr
            uni: dapp.prod_repl_subo_accr.accr_smbcurrentaccountapplication_decisions_smbcaacr
