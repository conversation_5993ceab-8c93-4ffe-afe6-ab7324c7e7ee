name: wf_mart_opt_pd_migr_cards
type: WORK_FLOW
schema_version: '1.14'
version: 1
tags:
  - 'mart_opt_pd_migr_cards'
  - 'Цицурин'
  - 'team: zi11'
  - 'wf'
  - 'src: ods'
  - 'tgt: rdv'
orientation: TB
local_metrics:
  wf_dataset_max_date_to:
    target: stage_T_input
    query: max(ods$effective_from_dt)
    on_null: .conf.algos."map_opt_pd_migr_cards".by_src."ods.WAY4VTB_SOH.h2_opt_pd_migr_cards".wf_dataset_max_date_to
sources:
  - short_name: ods_migr_cards
    type: DB_TABLE
    resource_cd: ods.WAY4VTB_SOH.h2_opt_pd_migr_cards
    object: h2_opt_pd_migr_cards

targets:
  - short_name: mart_migr_cards
    schema: rdv
    table: mart_opt_pd_migr_cards
    resource_cd: ceh.rdv.mart_opt_pd_migr_cards

  - short_name: hub_card
    schema: rdv
    table: hub_card
    resource_cd: ceh.rdv.hub_card

history_reducers:
  ods_scd2_local:
    rankers:
      - direction: desc
        expression: ods$is_active_flg = '1'
        field_type: BOOLEAN
      - direction: desc
        expression: ods$effective_from_dt
        field_type: TIMESTAMP
      - direction: desc
        expression: ods$processed_dt
        field_type: TIMESTAMP
      - direction: desc
        expression: ods$deleted_flg = '1'
        field_type: BOOLEAN

mappings:
  marts:
      #1 1 0
    - short_name: mart1
      algorithm_uid: 'map_opt_pd_migr_cards'
      algorithm_uid_2: '1'
      target: mart_migr_cards
      source: ods_migr_cards
      delta_mode: new
      where_clause:
        engine: jq
        template: rec_comment like '%CINFO%' and ods$effective_from_dt >= '{from}' and ods$effective_from_dt <= '{to}' and ods$effective_to_dt >= '5999-12-31' 
        vars:
          from: .conf.algos."map_opt_pd_migr_cards".by_src."ods.WAY4VTB_SOH.h2_opt_pd_migr_cards".wf_dataset_max_date_to
          to: .conf.algos."map_opt_pd_migr_cards".by_src."ods.WAY4VTB_SOH.h2_opt_pd_migr_cards".dataset_max_date_to
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
        - param_source: field
          param_value: ods$effective_from_dt
        - param_source: const
          param_value: day
      metrics:
        by_src:
          - wf_dataset_max_date_to
      history_reducer: ods_scd2_local
      field_map:
        id:
          type: column
          value: id
        migr_date:
          type: column
          value: migr_date
        pf_cid:
          type: column
          value: pf_cid
        rec_status:
          type: column
          value: rec_status
        card_pan:
          type: column
          value: card_pan
        way4_client_id:
          type: column
          value: way4_client_id
        way4_contr_id:
          type: column
          value: way4_contr_id
        way4_card_contr_id:
          type: column
          value: way4_card_contr_id
        rec_comment:
          type: column
          value: rec_comment
        source_system:
          type: column
          value: source_system
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN

      ref_map: []

      hub_map:             
        - target: hub_card
          rk_field: way4_card_rk
          business_key_schema: BK-rdv-card-card_seq-WAYN_WVTB
          on_full_null: good_default
          field_map:
            card_id:
                type: sql_expression
                value: rtrim(ltrim(replace(substring(rec_comment,37),';','')))

        - target: hub_card
          rk_field: pf_card_rk
          business_key_schema: BK-rdv-card-card_seq-PROF
          on_full_null: good_default
          field_map:
            card_id:
                type: column
                value: card_pan