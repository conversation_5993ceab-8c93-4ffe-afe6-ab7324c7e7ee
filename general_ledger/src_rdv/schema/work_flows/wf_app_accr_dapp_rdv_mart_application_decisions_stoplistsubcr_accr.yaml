name: wf_app_accr_dapp_rdv_mart_application_decisions_stoplistsubcr_accr
type: WORK_FLOW
schema_version: '1.13'
version: 1
tags:
  - 'team: zi14'
  - wf
  - 'area: app'
  - 'src: accr'
  - 'src-tbl: accr_smbcurrentaccountapplication_decisions_stoplistsubcr'
  - 'prv: dapp'
  - 'tgt: rdv'
  - 'tgt-tbl: mart_application_decisions_stoplistsubcr_accr'
  - 'cf: cf_app_accr_dapp_rdv_mart_application_decisions_stoplistsubcr_accr'
  - 'rls: 21.1'
  - 'uid: 20.ACCR.ZI14.11'
orientation: TB
sources:
  - short_name: dapp_prod_repl_subo_ac
    type: DB_TABLE
    resource_cd: dapp.prod_repl_subo_accr.accr_smbcurrentaccountapplication_decisions_stoplistsubcr
    object: dapp_prod_repl_subo_accr_accr_smbcurrentaccountapplication_decisions_stoplistsubcr
targets:
  - short_name: mart_application_decis
    schema: rdv
    table: mart_application_decisions_stoplistsubcr_accr
    resource_cd: ceh.rdv.mart_application_decisions_stoplistsubcr_accr
  - short_name: hub_client
    schema: rdv
    table: hub_client
    resource_cd: ceh.rdv.hub_client
  - short_name: hub_employee
    schema: rdv
    table: hub_employee
    resource_cd: ceh.rdv.hub_employee
local_metrics:
  wf_dataset_max_date_to:
    target: stage_T_input
    query: max(date_trunc('second', hdp_processed_dttm))
    on_null: .conf.algos."20.ACCR.ZI14.11".by_src."dapp.prod_repl_subo_accr.accr_smbcurrentaccountapplication_decisions_stoplistsubcr".dataset_max_date_to
mappings:
  marts:
    - short_name: mart_application_dec_1
      algorithm_uid: 20.ACCR.ZI14.11
      algorithm_uid_2: 1
      target: mart_application_decis
      source: dapp_prod_repl_subo_ac
      where_clause:
        engine: jq
        template: hdp_processed_dttm >= '{from}' and hdp_processed_dttm <= '{to}'
          and hdp_processed_dttm >= '2023-06-29T00:00:00'
        vars:
          from: .conf.algos."20.ACCR.ZI14.11".by_src."dapp.prod_repl_subo_accr.accr_smbcurrentaccountapplication_decisions_stoplistsubcr".wf_dataset_max_date_to
          to: .conf.algos."20.ACCR.ZI14.11".by_src."dapp.prod_repl_subo_accr.accr_smbcurrentaccountapplication_decisions_stoplistsubcr".dataset_max_date_to
            | strptime("%Y-%m-%dT%H:%M:%S") | mktime + 1 | strftime("%Y-%m-%dT%H:%M:%S")
      metrics:
        by_src:
          - save_as: wf_dataset_max_date_to
            metric: wf_dataset_max_date_to
      delta_mode: new
      field_map:
        changeid_dk:
          type: column
          value: changeid
        changetype:
          type: column
          value: changetype
        changetimestamp:
          type: sql_expression
          value: changetimestamp::timestamp
          field_type: TIMESTAMP
        decisions_stoplistsubjectcheckresult_hash_dk:
          type: column
          value: decisions_stoplistsubjectcheckresult_hash
        decisions_stoplistsubjectcr_checklistdetail_hash:
          type: column
          value: decisions_stoplistsubjectcr_checklistdetail_hash
        decisions_smbcaaslcr_sscr_checkresult:
          type: column
          value: decisions_smbcaaslcr_sscr_checkresult
        decisions_smbcaaslcr_sscr_subj_participantcheckinvolved:
          type: column
          value: decisions_smbcaaslcr_sscr_subj_participantcheckinvolved
        decisions_smbcaaslcr_sscr_subj_partyroletype:
          type: column
          value: decisions_smbcaaslcr_sscr_subj_partyroletype
        decisions_smbcaaslcr_sscr_subj_party_id_id:
          type: column
          value: decisions_smbcaaslcr_sscr_subj_party_id_id
        decisions_smbcaaslcr_sscr_subj_party_id_system:
          type: column
          value: decisions_smbcaaslcr_sscr_subj_party_id_system
        decisions_smbcaaslcr_sscr_subj_party_id_version:
          type: column
          value: decisions_smbcaaslcr_sscr_subj_party_id_version
        decisions_smbcaaslcr_sscr_subj_party_id_updatetime:
          type: sql_expression
          value: decisions_smbcaaslcr_sscr_subj_party_id_updatetime::timestamp
          field_type: TIMESTAMP
        decisions_smbcaaslcr_sscr_subj_party_id_createtime:
          type: sql_expression
          value: decisions_smbcaaslcr_sscr_subj_party_id_createtime::timestamp
          field_type: TIMESTAMP
        decisions_smbcaaslcr_sscr_subj_id_id:
          type: column
          value: decisions_smbcaaslcr_sscr_subj_id_id
        decisions_smbcaaslcr_sscr_subj_id_system:
          type: column
          value: decisions_smbcaaslcr_sscr_subj_id_system
        decisions_smbcaaslcr_sscr_subj_id_version:
          type: column
          value: decisions_smbcaaslcr_sscr_subj_id_version
        decisions_smbcaaslcr_sscr_subj_id_updatetime:
          type: sql_expression
          value: decisions_smbcaaslcr_sscr_subj_id_updatetime::timestamp
          field_type: TIMESTAMP
        decisions_smbcaaslcr_sscr_subj_id_createtime:
          type: sql_expression
          value: decisions_smbcaaslcr_sscr_subj_id_createtime::timestamp
          field_type: TIMESTAMP
        hdp_processed_dttm:
          type: column
          value: hdp_processed_dttm
        deleted_flg:
          type: literal
          value: 'false'
          field_type: BOOLEAN
      ref_map: []
      hub_map:
        - target: hub_client
          rk_field: counterparty_rk
          business_key_schema: BK-rdv-client-client_seq-CSOC
          on_full_null: good_default
          field_map:
            client_id:
              type: sql_expression
              value: case when decisions_smbcaaslcr_sscr_subj_id_system in ('1498')
                then decisions_smbcaaslcr_sscr_subj_id_id end
        - target: hub_employee
          rk_field: employee_rk
          business_key_schema: BK-rdv-employee-employee_code-BSCS
          on_full_null: good_default
          field_map:
            employee_id:
              type: sql_expression
              value: case when decisions_smbcaaslcr_sscr_subj_id_system in ('1777')
                then decisions_smbcaaslcr_sscr_subj_id_id end
