name: wf_app_mssa_ods_rdv_client
type: WORK_FLOW
schema_version: '1.14'
version: 1
tags:
  - 'team: zi6'
  - wf
  - 'area: app'
  - 'src: mssa'
  - 'prv: ods'
  - 'tgt: rdv'
  - 'cf: cf_app_mssa_ods_rdv'
  - 'rls: 3'
  - 'uid: 20.ZI6.18'
  - converted_v1_to_v2
orientation: TB
transaction:
  resources_lock_attempts: 60
  operator_default_timeout: 3600
sources:
  - short_name: h2_client_mssa
    type: DB_TABLE
    object: h2_client_mssa
    resource_cd: ods.MSSA_SOH.H2_CLIENT
targets:
  - short_name: hsat_client_mssa
    resource_cd: ceh.rdv.hsat_client_mssa
    table: hsat_client_mssa
    schema: rdv
    type: HUB_SATELLITE
  - short_name: hub_client
    resource_cd: ceh.rdv.hub_client
    table: hub_client
    schema: rdv
local_metrics: {}
mappings:
  hub_satellites:
    - target: hsat_client_mssa
      source: h2_client_mssa
      short_name: hsat_client_mssa
      algorithm_uid: 20.ZI6.18
      delta_mode: new
      where_clause:
        engine: jq
        template: '''{from}'' <= ods$effective_from_dt and ods$effective_from_dt <
          ''{to}'''
        vars:
          from: .conf.algos."20.ZI6.18".by_src."ods.MSSA_SOH.H2_CLIENT".wf_dataset_max_date_to
          to: .conf.algos."20.ZI6.18".by_src."ods.MSSA_SOH.H2_CLIENT".dataset_max_date_to
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: ods$effective_from_dt
          - param_source: const
            param_value: day
      field_map:
        mdm_id:
          type: column
          value: mdm_id
        mdm_id_previous:
          type: column
          value: mdm_id_previous
        tessa_id:
          type: column
          value: tessa_id
        role_ref:
          type: column
          value: role_ref
        type_ref:
          type: column
          value: type_ref
        first_name:
          type: column
          value: first_name
        last_name:
          type: column
          value: last_name
        patronymic:
          type: column
          value: patronymic
        gender_ref:
          type: column
          value: gender_ref
        birth_date:
          type: column
          value: birth_date
        passport_series:
          type: column
          value: passport_series
        passport_number:
          type: column
          value: passport_number
        place_of_birth:
          type: column
          value: place_of_birth
        passport_issue_date:
          type: column
          value: passport_issue_date
        passport_issued_by:
          type: column
          value: passport_issued_by
        passport_organization_code:
          type: column
          value: passport_organization_code
        tin:
          type: column
          value: tin
        service_package_ref:
          type: column
          value: service_package_ref
        education_type_ref:
          type: column
          value: education_type_ref
        marital_status_ref:
          type: column
          value: marital_status_ref
        underage_children_number:
          type: column
          value: underage_children_number
        snils:
          type: column
          value: snils
        phone_number:
          type: column
          value: phone_number
        email_address:
          type: column
          value: email_address
        corporativity_category_ref:
          type: column
          value: corporativity_category_ref
        previous_last_name:
          type: column
          value: previous_last_name
        previous_first_name:
          type: column
          value: previous_first_name
        previous_patronymic:
          type: column
          value: previous_patronymic
        previous_passport_series:
          type: column
          value: previous_passport_series
        previous_passport_number:
          type: column
          value: previous_passport_number
        previous_passport_issue_date:
          type: column
          value: previous_passport_issue_date
        previous_passport_issued_by:
          type: column
          value: previous_passport_issued_by
        working_experience_ref:
          type: column
          value: working_experience_ref
        vtb_employee_category_ref:
          type: column
          value: vtb_employee_category_ref
        is_vtb_group_employee:
          type: column
          value: is_vtb_group_employee
        rcc_segment_ref:
          type: column
          value: rcc_segment_ref
        passport_tessa_id:
          type: column
          value: passport_tessa_id
        photo_tessa_id:
          type: column
          value: photo_tessa_id
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN
      metrics:
        by_src:
          - save_as: wf_dataset_max_date_to
            metric: wf_dataset_max_date_to.increment.ODS.SOH_SCD2
      history_reducer: ODS.SOH_SCD2
      rk_map:
        target: hub_client
        business_key_schema: BK_client_SUBO_seq
        field_map:
          client_id:
            type: column
            value: client_id
      strategies:
        remove_full_duplicates: skip
history_reducers: {}
