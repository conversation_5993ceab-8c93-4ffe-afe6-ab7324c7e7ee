schema_version: '1.15'
name: wf_deal_ppsb_ods_rdv_mart_deal_registry_spspia_ppsb
type: WORK_FLOW
version: 1
tags:
  - wf
  - 'team: FD3'
  - 'area: deal'
  - 'src: ppsb'
  - 'prv: ods'
  - 'tgt: rdv'
  - 'cf: cf_deal_ppsb_ods_rdv_mart_deal_registry_spspia_ppsb'
  - 'uid: 8.PPSB.FD3.3'
  - 'rls: 96'
orientation: TB
local_metrics:
  wf_dataset_max_date_to:
    query: max("ODS$EFFECTIVE_FROM_DT")
    target: stage_T_input
    on_null: .conf.algos["8.PPSB.FD3.3"].by_src["ods.PPSB_SOH.H2_SPSPIA"].wf_dataset_max_date_to
sources:
  - short_name: h2_spspia
    type: DB_TABLE
    object: h2_spspia
    resource_cd: ods.PPSB_SOH.H2_SPSPIA
targets:
  - short_name: mart_deal_r_spspia
    schema: rdv
    table: mart_deal_registry_spspia_ppsb
    resource_cd: ceh.rdv.mart_deal_registry_spspia_ppsb.ppsb
  - short_name: hub_deal_registry
    resource_cd: ceh.rdv.hub_deal_registry
    schema: rdv
    table: hub_deal_registry
  - short_name: hub_deal
    resource_cd: ceh.rdv.hub_deal
    schema: rdv
    table: hub_deal
  - short_name: hub_account
    resource_cd: ceh.rdv.hub_account
    schema: rdv
    table: hub_account
mappings:
  marts:
    - target: mart_deal_r_spspia
      source: h2_spspia
      short_name: mart_deal_r_spspia
      algorithm_uid: 8.PPSB.FD3.3
      algorithm_uid_2: '1'
      delta_mode: new
      where_clause:
        engine: jq
        template: |
          "ODS$EFFECTIVE_FROM_DT" >= '{from}' and "ODS$EFFECTIVE_FROM_DT" < '{to}'
        vars:
          from: .conf.algos."8.PPSB.FD3.3".by_src."ods.PPSB_SOH.H2_SPSPIA".wf_dataset_max_date_to
          to: .conf.algos."8.PPSB.FD3.3".by_src."ods.PPSB_SOH.H2_SPSPIA".dataset_max_date_to
      field_map:
        deal_registry_id:
          type: column
          value: ID_ID
        deleted_flg:
          type: sql_expression
          value: |
            "ODS$DELETED_FLG" = '1'
          field_type: BOOLEAN
        env_id:
          type: column
          value: ENV$ID
        env_pubtime:
          type: column
          value: ENV$PUBTIME
        env_pub_sys:
          type: column
          value: ENV$PUB_SYS
        env_pub_subsys:
          type: column
          value: ENV$PUB_SUBSYS
        env_meta_sch_id:
          type: column
          value: ENV$META_SCH_ID
        env_meta_sch_ver:
          type: column
          value: ENV$META_SCH_VER
        env_meta_sch_url:
          type: column
          value: ENV$META_SCH_URL
        proins_id_cretime:
          type: column
          value: PROINS_ID_CRETIME
        proins_id_id:
          type: column
          value: PROINS_ID_ID
        proins_id_subsys:
          type: column
          value: PROINS_ID_SUBSYS
        proins_id_sys:
          type: column
          value: PROINS_ID_SYS
        proins_id_updtime:
          type: column
          value: PROINS_ID_UPDTIME
        proins_id_ver:
          type: column
          value: PROINS_ID_VER
        acc_id_cretime:
          type: column
          value: ACC_ID_CRETIME
        acc_id_id:
          type: column
          value: ACC_ID_ID
        acc_id_subsys:
          type: column
          value: ACC_ID_SUBSYS
        acc_id_sys:
          type: column
          value: ACC_ID_SYS
        acc_id_updtime:
          type: column
          value: ACC_ID_UPDTIME
        acc_id_ver:
          type: column
          value: ACC_ID_VER
        type_dic_id:
          type: column
          value: TYPE_DIC_ID
        type_dic_subsys:
          type: column
          value: TYPE_DIC_SUBSYS
        #type_dic_sys:
          #type: column
          #value: "TYPE_DIC_SYS"
        type_key:
          type: column
          value: TYPE_KEY
        type_keyname:
          type: column
          value: TYPE_KEYNAME
        status:
          type: column
          value: STATUS
        number:
          type: column
          value: "NUMBER"
        id_cretime:
          type: column
          value: ID_CRETIME
        id_subsys:
          type: column
          value: ID_SUBSYS
        id_sys:
          type: column
          value: ID_SYS
        id_updtime:
          type: column
          value: ID_UPDTIME
        id_ver:
          type: column
          value: ID_VER

      hub_map:
        - target: hub_deal_registry
          rk_field: deal_registry_rk
          business_key_schema: BK-rdv-deal_registry-deal_registry_code-PPSB
          field_map:
            deal_registry_id:
              type: sql_expression
              value: |
                coalesce("ID_ID",'~null~'::text)
              field_type: TEXT
          on_full_null: good_default
        - target: hub_deal
          rk_field: deal_rk
          business_key_schema: BK-rdv-deal-deal_code-PPSB
          field_map:
            deal_id:
              type: sql_expression
              value: |
                coalesce("PROINS_ID_ID",'~null~'::text)
              field_type: TEXT
          on_full_null: good_default
        - target: hub_account
          rk_field: account_rk
          business_key_schema: BK-rdv-account-account_uuid-OCBA
          field_map:
            account_id:
              type: sql_expression
              value: |
                coalesce("ACC_ID_ID",'~null~'::text)
              field_type: TEXT
          on_full_null: good_default
      metrics:
        by_src:
          - wf_dataset_max_date_to
      history_reducer: ods_scd2_local
      strategies:
        replace_null: skip
        remove_full_duplicates: skip
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: "ODS$EFFECTIVE_FROM_DT"
          - param_source: const
            param_value: day
      ref_map: []
history_reducers:
  ods_scd2_local:
    rankers:
      - direction: desc
        expression: |
          "ODS$IS_ACTIVE_FLG" = '1'
        field_type: BOOLEAN
      - direction: desc
        expression: |
          "ODS$EFFECTIVE_FROM_DT"
        field_type: TIMESTAMP
      - direction: desc
        expression: |
          "ODS$PROCESSED_DT"
        field_type: TIMESTAMP
      - direction: desc
        expression: |
          "ODS$DELETED_FLG" = '1'
        field_type: BOOLEAN