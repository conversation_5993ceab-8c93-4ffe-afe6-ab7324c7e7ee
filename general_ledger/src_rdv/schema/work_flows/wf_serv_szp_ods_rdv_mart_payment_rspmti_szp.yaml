name: wf_serv_szp_ods_rdv_mart_payment_rspmti_szp
type: WORK_FLOW
schema_version: '1.16'
version: 1
tags:
- wf
- cf_serv_szp_ods_rdv_mart_payment_rspmti_szp
- wf_serv_szp_ods_rdv_mart_payment_rspmti_szp
- 'team: zi16'
- 'area: serv'
- 'src: SZP'
- 'prv: ods'
- 'tgt: rdv'
- 'rls: 97'
- zi16_rdv
- 'uid: 35.SZP.ZI16.01'
- 'tgt-tbl: rdv.mart_payment_rspmti_szp'
- 'tgt-tbl: rdv.mart_payment_rspmti_add_szp'
- 'tgt-tbl: rdv.mart_payment_rspmti_add2_szp'
- 'src-tbl: SZP_SOH.SZP_H2_RSPMTI'
orientation: TB
transaction: {}
sources:
- short_name: h2_rspmti
  type: DB_TABLE
  resource_cd: ods_odsaa.SZP_SOH.H2_RSPMTI
  object: szp_h2_rspmti
targets:
- short_name: mt_pmnt_rspmti_szp
  resource_cd: ceh.rdv.mart_payment_rspmti_szp
  table: mart_payment_rspmti_szp
  schema: rdv
- short_name: mt_pmnt_rspmti_add
  resource_cd: ceh.rdv.mart_payment_rspmti_add_szp
  table: mart_payment_rspmti_add_szp
  schema: rdv
- short_name: mt_pmnt_rspmti_add2
  resource_cd: ceh.rdv.mart_payment_rspmti_add2_szp
  table: mart_payment_rspmti_add2_szp
  schema: rdv
- short_name: hub_payment
  resource_cd: ceh.rdv.hub_payment
  table: hub_payment
  schema: rdv
- short_name: hub_client
  resource_cd: ceh.rdv.hub_client
  table: hub_client
  schema: rdv
- short_name: hub_currency
  resource_cd: ceh.rdv.hub_currency
  table: hub_currency
  schema: rdv
local_metrics:
  h2_rspmti_max_date_to:
    target: stage_T_input
    query: max("ODS$EFFECTIVE_FROM_DT")
    on_null: .conf.algos."35.SZP.ZI16.01".by_src."ods_odsaa.SZP_SOH.H2_RSPMTI".wf_dataset_max_date_to
mappings:
  marts:
  - target: mt_pmnt_rspmti_szp
    source: h2_rspmti
    short_name: h2_rspmti0
    algorithm_uid: 35.SZP.ZI16.01
    algorithm_uid_2: 1
    delta_mode: new
    synthetic_history:
      syn_history_function: rdv.syn_bussiness_date_trunc
      func_param:
      - param_source: field
        param_value: "ODS$EFFECTIVE_FROM_DT"
      - param_source: const
        param_value: day
    where_clause:
      engine: jq
      template: |
        "ODS$EFFECTIVE_FROM_DT" >= '{from}' and "ODS$EFFECTIVE_FROM_DT" < '{to}'
      vars:
        from: .conf.algos."35.SZP.ZI16.01".by_src."ods_odsaa.SZP_SOH.H2_RSPMTI".wf_dataset_max_date_to
        to: .conf.algos."35.SZP.ZI16.01".by_src."ods_odsaa.SZP_SOH.H2_RSPMTI".dataset_max_date_to
    history_reducer: ods_scd2_local
    metrics:
      by_src:
      - save_as: wf_dataset_max_date_to
        metric: h2_rspmti_max_date_to
    ref_map: []
    field_map:
      deleted_flg:
        type: sql_expression
        value: |
          "ODS$DELETED_FLG" = '1'
        field_type: BOOLEAN
      env_id:
        type: column
        value: "ENV$ID"
      env_pubtime:
        type: column
        value: "ENV$PUBTIME"
      env_pub_sys:
        type: column
        value: "ENV$PUB_SYS"
      env_pub_subsys:
        type: column
        value: "ENV$PUB_SUBSYS"
      env_meta_sch_id:
        type: column
        value: "ENV$META_SCH_ID"
      env_meta_sch_ver:
        type: column
        value: "ENV$META_SCH_VER"
      env_meta_sch_url:
        type: column
        value: "ENV$META_SCH_URL"
      budpay_budcla_dic_id:
        type: column
        value: "BUDPAY_BUDCLA_DIC_ID"
      budpay_budcla_dic_subsys:
        type: column
        value: "BUDPAY_BUDCLA_DIC_SUBSYS"
      budpay_budcla_dic_sys:
        type: column
        value: "BUDPAY_BUDCLA_DIC_SYS"
      budpay_budcla_key:
        type: column
        value: "BUDPAY_BUDCLA_KEY"
      budpay_budcla_keyname:
        type: column
        value: "BUDPAY_BUDCLA_KEYNAME"
      budpay_cuscode:
        type: column
        value: "BUDPAY_CUSCODE"
      budpay_doc_cbrtype_dic_id:
        type: column
        value: "BUDPAY_DOC_CBRTYPE_DIC_ID"
      budpay_doc_cbrtype_dic_subsys:
        type: column
        value: "BUDPAY_DOC_CBRTYPE_DIC_SUBSYS"
      budpay_doc_cbrtype_dic_sys:
        type: column
        value: "BUDPAY_DOC_CBRTYPE_DIC_SYS"
      budpay_doc_cbrtype_key:
        type: column
        value: "BUDPAY_DOC_CBRTYPE_KEY"
      budpay_doc_cbrtype_keyname:
        type: column
        value: "BUDPAY_DOC_CBRTYPE_KEYNAME"
      budpay_doc_date:
        type: column
        value: "BUDPAY_DOC_DATE"
      budpay_doc_num:
        type: column
        value: "BUDPAY_DOC_NUM"
      sou_accbank_client_id:
        type: column
        value: "SOU_ACCBANK_PAR_ID_ID"
      direction:
        type: column
        value: "DIRECTION"
      fees_amo_cur_dic_id:
        type: column
        value: "FEES_AMO_CUR_DIC_ID"
      fees_amo_cur_dic_subsys:
        type: column
        value: "FEES_AMO_CUR_DIC_SUBSYS"
      fees_amo_cur_dic_sys:
        type: column
        value: "FEES_AMO_CUR_DIC_SYS"
      fees_amo_currency_id:
        type: column
        value: "FEES_AMO_CUR_KEY"
      fees_amo_cur_keyname:
        type: column
        value: "FEES_AMO_CUR_KEYNAME"
      fees_amo_val:
        type: column
        value: "FEES_AMO_VAL"
      fees_payside:
        type: column
        value: "FEES_PAYSIDE"
      fees_tar_id_cretime:
        type: column
        value: "FEES_TAR_ID_CRETIME"
      fees_tar_id_id:
        type: column
        value: "FEES_TAR_ID_ID"
      fees_tar_id_subsys:
        type: column
        value: "FEES_TAR_ID_SUBSYS"
      fees_tar_id_sys:
        type: column
        value: "FEES_TAR_ID_SYS"
      fees_tar_id_updtime:
        type: column
        value: "FEES_TAR_ID_UPDTIME"
      fees_tar_id_ver:
        type: column
        value: "FEES_TAR_ID_VER"
      fees_type:
        type: column
        value: "FEES_TYPE"
      pri_dic_id:
        type: column
        value: "PRI_DIC_ID"
      pri_dic_subsys:
        type: column
        value: "PRI_DIC_SUBSYS"
      pri_dic_sys:
        type: column
        value: "PRI_DIC_SYS"
      pri_key:
        type: column
        value: "PRI_KEY"
      pri_keyname:
        type: column
        value: "PRI_KEYNAME"
      pur_des:
        type: column
        value: "PUR_DES"
      tar_acc_id_id:
        type: column
        value: "TAR_ACC_ID_ID"
      sou_acc_id_id:
        type: column
        value: "SOU_ACC_ID_ID"
      sou_acc_id_sys:
        type: column
        value: "SOU_ACC_ID_SYS"
      sou_ben_client_id:
        type: column
        value: "SOU_BEN_PAR_ID_ID"
      sou_ben_par_id_sys:
        type: column
        value: "SOU_BEN_PAR_ID_SYS"
      tar_accbank_client_id:
        type: column
        value: "TAR_ACCBANK_PAR_ID_ID"
      sou_accbank_par_id_sys:
        type: column
        value: "SOU_ACCBANK_PAR_ID_SYS"
      status:
        type: column
        value: "STATUS"
      tar_acc_id_cretime:
        type: column
        value: "TAR_ACC_ID_CRETIME"
      tar_acc_id_subsys:
        type: column
        value: "TAR_ACC_ID_SUBSYS"
      tar_acc_id_sys:
        type: column
        value: "TAR_ACC_ID_SYS"
      tar_acc_id_updtime:
        type: column
        value: "TAR_ACC_ID_UPDTIME"
      tar_acc_id_ver:
        type: column
        value: "TAR_ACC_ID_VER"
      tar_accidesys:
        type: column
        value: "TAR_ACCIDESYS"
      tar_amo_cur_dic_id:
        type: column
        value: "TAR_AMO_CUR_DIC_ID"
      tar_amo_cur_dic_subsys:
        type: column
        value: "TAR_AMO_CUR_DIC_SUBSYS"
      tar_amo_cur_dic_sys:
        type: column
        value: "TAR_AMO_CUR_DIC_SYS"
      tar_amo_currency_id:
        type: column
        value: "TAR_AMO_CUR_KEY"
      tar_amo_cur_keyname:
        type: column
        value: "TAR_AMO_CUR_KEYNAME"
      tar_amo_val:
        type: column
        value: "TAR_AMO_VAL"
      tar_ben_client_id:
        type: column
        value: "TAR_BEN_PAR_ID_ID"
      tar_ben_par_id_sys:
        type: column
        value: "TAR_BEN_PAR_ID_SYS"
      tar_accbank_par_id_sys:
        type: column
        value: "TAR_ACCBANK_PAR_ID_SYS"
      type_dic_id:
        type: column
        value: "TYPE_DIC_ID"
      type_dic_subsys:
        type: column
        value: "TYPE_DIC_SUBSYS"
      type_dic_sys:
        type: column
        value: "TYPE_DIC_SYS"
      type_key:
        type: column
        value: "TYPE_KEY"
      type_keyname:
        type: column
        value: "TYPE_KEYNAME"
      cretype:
        type: column
        value: "CRETYPE"
      id_cretime:
        type: column
        value: "ID_CRETIME"
      payment_id:
        type: column
        value: "ID_ID"
      id_subsys:
        type: column
        value: "ID_SUBSYS"
      id_sys:
        type: column
        value: "ID_SYS"
      id_updtime:
        type: column
        value: "ID_UPDTIME"
      budpay_unipayide:
        type: column
        value: "BUDPAY_UNIPAYIDE"
      id_ver:
        type: column
        value: "ID_VER"
    hub_map:
    - target: hub_payment
      rk_field: payment_rk
      on_full_null: good_default
      field_map:
        payment_id:
          type: column
          value: "ID_ID"
      business_key_schema: BK-rdv-payment-payment_uuid-SZP
    - target: hub_client
      rk_field: sou_accbank_client_rk
      on_full_null: good_default
      field_map:
        client_id:
          type: column
          value: "SOU_ACCBANK_PAR_ID_ID"
      business_key_schema: BK-rdv-client-client_seq-CSOC
    - target: hub_currency
      rk_field: fees_amo_currency_rk
      on_full_null: good_default
      field_map:
        currency_id:
          type: column
          value: "FEES_AMO_CUR_KEY"
      business_key_schema: BK-rdv-currency-currency_seq-SZP
    - target: hub_client
      rk_field: sou_ben_client_rk
      on_full_null: good_default
      field_map:
        client_id:
          type: column
          value: "SOU_BEN_PAR_ID_ID"
      business_key_schema: client_schema_mdmp
    - target: hub_client
      rk_field: tar_accbank_client_rk
      on_full_null: good_default
      field_map:
        client_id:
          type: column
          value: "TAR_ACCBANK_PAR_ID_ID"
      business_key_schema: BK-rdv-client-client_seq-CSOC
    - target: hub_currency
      rk_field: tar_amo_currency_rk
      on_full_null: good_default
      field_map:
        currency_id:
          type: column
          value: "TAR_AMO_CUR_KEY"
      business_key_schema: BK-rdv-currency-currency_seq-SZP
    - target: hub_client
      rk_field: tar_ben_client_rk
      on_full_null: good_default
      field_map:
        client_id:
          type: column
          value: "TAR_BEN_PAR_ID_ID"
      business_key_schema: client_schema_mdmp
  - target: mt_pmnt_rspmti_add
    source: h2_rspmti
    short_name: h2_rspmti1
    algorithm_uid: 35.SZP.ZI16.01
    algorithm_uid_2: 2
    delta_mode: new
    synthetic_history:
      syn_history_function: rdv.syn_bussiness_date_trunc
      func_param:
      - param_source: field
        param_value: "ODS$EFFECTIVE_FROM_DT"
      - param_source: const
        param_value: day
    where_clause:
      engine: jq
      template: |
        "ODS$EFFECTIVE_FROM_DT" >= '{from}' and "ODS$EFFECTIVE_FROM_DT" < '{to}'
      vars:
        from: .conf.algos."35.SZP.ZI16.01".by_src."ods_odsaa.SZP_SOH.H2_RSPMTI".wf_dataset_max_date_to
        to: .conf.algos."35.SZP.ZI16.01".by_src."ods_odsaa.SZP_SOH.H2_RSPMTI".dataset_max_date_to
    history_reducer: ods_scd2_local
    metrics:
      by_src:
      - save_as: wf_dataset_max_date_to
        metric: h2_rspmti_max_date_to
    ref_map: []
    field_map:
      deleted_flg:
        type: sql_expression
        value: |
          "ODS$DELETED_FLG" = '1'
        field_type: BOOLEAN
      payment_id:
        type: column
        value: "ID_ID"
      budpay_munter_dic_id:
        type: column
        value: "BUDPAY_MUNTER_DIC_ID"
      budpay_munter_dic_subsys:
        type: column
        value: "BUDPAY_MUNTER_DIC_SUBSYS"
      budpay_munter_dic_sys:
        type: column
        value: "BUDPAY_MUNTER_DIC_SYS"
      budpay_munter_key:
        type: column
        value: "BUDPAY_MUNTER_KEY"
      budpay_munter_keyname:
        type: column
        value: "BUDPAY_MUNTER_KEYNAME"
      budpay_paysta_dic_id:
        type: column
        value: "BUDPAY_PAYSTA_DIC_ID"
      budpay_paysta_dic_subsys:
        type: column
        value: "BUDPAY_PAYSTA_DIC_SUBSYS"
      budpay_paysta_dic_sys:
        type: column
        value: "BUDPAY_PAYSTA_DIC_SYS"
      budpay_paysta_key:
        type: column
        value: "BUDPAY_PAYSTA_KEY"
      budpay_paysta_keyname:
        type: column
        value: "BUDPAY_PAYSTA_KEYNAME"
      budpay_payrea_dic_id:
        type: column
        value: "BUDPAY_PAYREA_DIC_ID"
      budpay_payrea_dic_subsys:
        type: column
        value: "BUDPAY_PAYREA_DIC_SUBSYS"
      budpay_payrea_dic_sys:
        type: column
        value: "BUDPAY_PAYREA_DIC_SYS"
      budpay_payrea_key:
        type: column
        value: "BUDPAY_PAYREA_KEY"
      budpay_payrea_keyname:
        type: column
        value: "BUDPAY_PAYREA_KEYNAME"
      budpay_paytype:
        type: column
        value: "BUDPAY_PAYTYPE"
      budpay_taxper:
        type: column
        value: "BUDPAY_TAXPER"
      sou_acc_id_cretime:
        type: column
        value: "SOU_ACC_ID_CRETIME"
      sou_acc_id_subsys:
        type: column
        value: "SOU_ACC_ID_SUBSYS"
      sou_acc_id_updtime:
        type: column
        value: "SOU_ACC_ID_UPDTIME"
      sou_acc_id_ver:
        type: column
        value: "SOU_ACC_ID_VER"
      sou_accidesys:
        type: column
        value: "SOU_ACCIDESYS"
      sou_amo_cur_dic_id:
        type: column
        value: "SOU_AMO_CUR_DIC_ID"
      sou_amo_cur_dic_subsys:
        type: column
        value: "SOU_AMO_CUR_DIC_SUBSYS"
      sou_amo_cur_dic_sys:
        type: column
        value: "SOU_AMO_CUR_DIC_SYS"
      sou_amo_cur_key:
        type: column
        value: "SOU_AMO_CUR_KEY"
      sou_amo_cur_keyname:
        type: column
        value: "SOU_AMO_CUR_KEYNAME"
      sou_amo_val:
        type: column
        value: "SOU_AMO_VAL"
      sou_ben_ide_banksyside_bic_dic_id:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_BIC_DIC_ID"
      sou_ben_ide_banksyside_bic_dic_subsys:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_BIC_DIC_SUBSYS"
      sou_ben_ide_banksyside_bic_dic_sys:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_BIC_DIC_SYS"
      sou_ben_ide_banksyside_bic_key:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_BIC_KEY"
      sou_ben_ide_banksyside_bic_keyname:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_BIC_KEYNAME"
      sou_ben_ide_banksyside_bra:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_BRA"
      sou_ben_ide_banksyside_coracc:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_CORACC"
      sou_ben_ide_banksyside_fullname:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_FULLNAME"
      sou_ben_ide_banksyside_type:
        type: column
        value: "SOU_BEN_IDE_BANKSYSIDE_TYPE"
      sou_ben_ide_taxregreacode:
        type: column
        value: "SOU_BEN_IDE_TAXREGREACODE"
      sou_ben_ide_name:
        type: column
        value: "SOU_BEN_IDE_NAME"
      sou_ben_ide_taxidenum:
        type: column
        value: "SOU_BEN_IDE_TAXIDENUM"
      sou_ben_ide_type:
        type: column
        value: "SOU_BEN_IDE_TYPE"
      sou_ben_par_id_cretime:
        type: column
        value: "SOU_BEN_PAR_ID_CRETIME"
      sou_ben_par_id_subsys:
        type: column
        value: "SOU_BEN_PAR_ID_SUBSYS"
      sou_ben_par_id_updtime:
        type: column
        value: "SOU_BEN_PAR_ID_UPDTIME"
      sou_ben_par_id_ver:
        type: column
        value: "SOU_BEN_PAR_ID_VER"
      sou_accbank_ide_banksyside_bic_dic_id:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_BIC_DIC_ID"
      sou_accbank_ide_banksyside_bic_dic_subsys:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_BIC_DIC_SUBSYS"
      sou_accbank_ide_banksyside_bic_dic_sys:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_BIC_DIC_SYS"
      sou_accbank_ide_banksyside_bic_key:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_BIC_KEY"
      sou_accbank_ide_banksyside_bic_keyname:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_BIC_KEYNAME"
      sou_accbank_ide_banksyside_bra:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_BRA"
      sou_accbank_ide_banksyside_coracc:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_CORACC"
      sou_accbank_ide_banksyside_fullname:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_FULLNAME"
      sou_accbank_ide_banksyside_type:
        type: column
        value: "SOU_ACCBANK_IDE_BANKSYSIDE_TYPE"
      sou_accbank_ide_taxregreacode:
        type: column
        value: "SOU_ACCBANK_IDE_TAXREGREACODE"
      sou_accbank_ide_name:
        type: column
        value: "SOU_ACCBANK_IDE_NAME"
      sou_accbank_ide_taxidenum:
        type: column
        value: "SOU_ACCBANK_IDE_TAXIDENUM"
      sou_accbank_ide_type:
        type: column
        value: "SOU_ACCBANK_IDE_TYPE"
      sou_accbank_par_id_cretime:
        type: column
        value: "SOU_ACCBANK_PAR_ID_CRETIME"
      sou_accbank_par_id_subsys:
        type: column
        value: "SOU_ACCBANK_PAR_ID_SUBSYS"
      sou_accbank_par_id_updtime:
        type: column
        value: "SOU_ACCBANK_PAR_ID_UPDTIME"
      sou_accbank_par_id_ver:
        type: column
        value: "SOU_ACCBANK_PAR_ID_VER"
      sou_accnum:
        type: column
        value: "SOU_ACCNUM"
      tar_ben_ide_banksyside_bic_dic_id:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_BIC_DIC_ID"
      tar_ben_ide_banksyside_bic_dic_subsys:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_BIC_DIC_SUBSYS"
      tar_ben_ide_banksyside_bic_dic_sys:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_BIC_DIC_SYS"
      tar_ben_ide_banksyside_bic_key:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_BIC_KEY"
      tar_ben_ide_banksyside_bic_keyname:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_BIC_KEYNAME"
      tar_ben_ide_banksyside_bra:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_BRA"
      tar_ben_ide_banksyside_coracc:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_CORACC"
      tar_ben_ide_banksyside_fullname:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_FULLNAME"
      tar_ben_ide_banksyside_type:
        type: column
        value: "TAR_BEN_IDE_BANKSYSIDE_TYPE"
      tar_ben_ide_taxregreacode:
        type: column
        value: "TAR_BEN_IDE_TAXREGREACODE"
      tar_ben_ide_name:
        type: column
        value: "TAR_BEN_IDE_NAME"
      tar_ben_ide_taxidenum:
        type: column
        value: "TAR_BEN_IDE_TAXIDENUM"
      tar_ben_ide_type:
        type: column
        value: "TAR_BEN_IDE_TYPE"
      tar_ben_par_id_cretime:
        type: column
        value: "TAR_BEN_PAR_ID_CRETIME"
      tar_ben_par_id_subsys:
        type: column
        value: "TAR_BEN_PAR_ID_SUBSYS"
      tar_ben_par_id_updtime:
        type: column
        value: "TAR_BEN_PAR_ID_UPDTIME"
      tar_ben_par_id_ver:
        type: column
        value: "TAR_BEN_PAR_ID_VER"
    hub_map:
    - target: hub_payment
      rk_field: payment_rk
      on_full_null: good_default
      field_map:
        payment_id:
          type: column
          value: "ID_ID"
      business_key_schema: BK-rdv-payment-payment_uuid-SZP
    - target: hub_currency
      rk_field: sou_amo_currency_rk
      on_full_null: good_default
      field_map:
        currency_id:
          type: column
          value: "SOU_AMO_CUR_KEY"
      business_key_schema: BK-rdv-currency-currency_seq-SZP
  - target: mt_pmnt_rspmti_add2
    source: h2_rspmti
    short_name: h2_rspmti2
    algorithm_uid: 35.SZP.ZI16.01
    algorithm_uid_2: 3
    delta_mode: new
    synthetic_history:
      syn_history_function: rdv.syn_bussiness_date_trunc
      func_param:
      - param_source: field
        param_value: "ODS$EFFECTIVE_FROM_DT"
      - param_source: const
        param_value: day
    where_clause:
      engine: jq
      template: |
        "ODS$EFFECTIVE_FROM_DT" >= '{from}' and "ODS$EFFECTIVE_FROM_DT" < '{to}'
      vars:
        from: .conf.algos."35.SZP.ZI16.01".by_src."ods_odsaa.SZP_SOH.H2_RSPMTI".wf_dataset_max_date_to
        to: .conf.algos."35.SZP.ZI16.01".by_src."ods_odsaa.SZP_SOH.H2_RSPMTI".dataset_max_date_to
    history_reducer: ods_scd2_local
    metrics:
      by_src:
      - save_as: wf_dataset_max_date_to
        metric: h2_rspmti_max_date_to
    ref_map: []
    field_map:
      deleted_flg:
        type: sql_expression
        value: |
          "ODS$DELETED_FLG" = '1'
        field_type: BOOLEAN
      payment_id:
        type: column
        value: "ID_ID"
      tar_accbank_ide_banksyside_bic_dic_id:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_BIC_DIC_ID"
      tar_accbank_ide_banksyside_bic_dic_subsys:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_BIC_DIC_SUBSYS"
      tar_accbank_ide_banksyside_bic_dic_sys:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_BIC_DIC_SYS"
      tar_accbank_ide_banksyside_bic_key:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_BIC_KEY"
      tar_accbank_ide_banksyside_bic_keyname:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_BIC_KEYNAME"
      tar_accbank_ide_banksyside_bra:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_BRA"
      tar_accbank_ide_banksyside_coracc:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_CORACC"
      tar_accbank_ide_banksyside_fullname:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_FULLNAME"
      tar_accbank_ide_banksyside_type:
        type: column
        value: "TAR_ACCBANK_IDE_BANKSYSIDE_TYPE"
      tar_accbank_ide_taxregreacode:
        type: column
        value: "TAR_ACCBANK_IDE_TAXREGREACODE"
      tar_accbank_ide_name:
        type: column
        value: "TAR_ACCBANK_IDE_NAME"
      tar_accbank_ide_taxidenum:
        type: column
        value: "TAR_ACCBANK_IDE_TAXIDENUM"
      tar_accbank_ide_type:
        type: column
        value: "TAR_ACCBANK_IDE_TYPE"
      tar_accbank_par_id_cretime:
        type: column
        value: "TAR_ACCBANK_PAR_ID_CRETIME"
      tar_accbank_par_id_subsys:
        type: column
        value: "TAR_ACCBANK_PAR_ID_SUBSYS"
      tar_accbank_par_id_updtime:
        type: column
        value: "TAR_ACCBANK_PAR_ID_UPDTIME"
      tar_accbank_par_id_ver:
        type: column
        value: "TAR_ACCBANK_PAR_ID_VER"
      tar_accnum:
        type: column
        value: "TAR_ACCNUM"
    hub_map:
    - target: hub_payment
      rk_field: payment_rk
      on_full_null: good_default
      field_map:
        payment_id:
          type: column
          value: "ID_ID"
      business_key_schema: BK-rdv-payment-payment_uuid-SZP
history_reducers:
  ods_scd2_local:
    rankers:
    - direction: desc
      expression: |
        "ODS$IS_ACTIVE_FLG" = '1'
      field_type: BOOLEAN
    - direction: desc
      expression: |
        "ODS$EFFECTIVE_FROM_DT"
      field_type: TIMESTAMP
    - direction: desc
      expression: |
        "ODS$PROCESSED_DT"
      field_type: TIMESTAMP
    - direction: desc
      expression: |
        "ODS$DELETED_FLG" = '1'
      field_type: BOOLEAN
