name: wf_deal_ppkc_ods_rdv_mart_deal_fee_ccpiaf_ppkc
type: WORK_FLOW
schema_version: '1.14'
version: 1
tags:
  - 'team: ckb10'
  - cf
  - 'area: deal'
  - 'src: ppkc'
  - 'prv: ods'
  - 'tgt: rdv'
  - 'rls: 1'
  - 'uid: 8.PPKC.CKB10.6'
  - 'src-tbl: DEV_GROUP_ZI5.H2_CCPIAF'
  - 'trg-tbl: rdv.mart_deal_fee_ccpiaf_ppkc'
  - 'wf: wf_deal_ppkc_ods_rdv_mart_deal_fee_ccpiaf_ppkc'
orientation: TB
sources:
  - short_name: h2_ccpiaf
    type: DB_TABLE
    object: h2_ccpiaf
    resource_cd: ods.DEV_GROUP_ZI5.H2_CCPIAF
targets:
  - short_name: hub_deal_fee
    resource_cd: ceh.rdv.hub_deal_fee
    table: hub_deal_fee
    schema: rdv
  - short_name: hub_currency
    resource_cd: ceh.rdv.hub_currency
    table: hub_currency
    schema: rdv
  - short_name: hub_deal
    resource_cd: ceh.rdv.hub_deal
    table: hub_deal
    schema: rdv
  - short_name: mart_deal_fee_ccpiaf_1
    resource_cd: ceh.rdv.mart_deal_fee_ccpiaf_ppkc
    table: mart_deal_fee_ccpiaf_ppkc
    schema: rdv
local_metrics:
  wf_dataset_max_date_to:
    target: stage_T_input
    query: max("ODS$EFFECTIVE_FROM_DT")
    on_null: .conf.algos."8.PPKC.CKB10.6".by_src."ods.DEV_GROUP_ZI5.H2_CCPIAF".wf_dataset_max_date_to
mappings:
  marts:
    - short_name: mart_deal_fee_ccpiaf_1
      algorithm_uid: 8.PPKC.CKB10.6
      algorithm_uid_2: '1'
      target: mart_deal_fee_ccpiaf_1
      source: h2_ccpiaf
      delta_mode: new
      where_clause:
        engine: jq
        template: '''{from}'' <= "ODS$EFFECTIVE_FROM_DT" and "ODS$EFFECTIVE_FROM_DT"
          < ''{to}'''
        vars:
          from_dttm: .conf.algos."8.PPKC.CKB10.6".by_src."ods.DEV_GROUP_ZI5.H2_CCPIAF".wf_dataset_max_date_to
          to_dttm: .conf.algos."8.PPKC.CKB10.6".by_src."ods.DEV_GROUP_ZI5.H2_CCPIAF".dataset_max_date_to
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: "ODS$EFFECTIVE_FROM_DT"
          - param_source: const
            param_value: day
      field_map:
        deleted_flg:
          type: sql_expression
          value: etl.try_cast2bool(cast("ODS$DELETED_FLG" as text)
          field_type: BOOLEAN
        deal_fee_id:
          type: column
          value: "ID_ID"
        id_sys:
          type: column
          value: "ID_SYS"
        id_subsys:
          type: column
          value: "ID_SUBSYS"
        id_ver:
          type: column
          value: "ID_VER"
        id_updtime:
          type: column
          value: "ID_UPDTIME"
        id_cretime:
          type: column
          value: "ID_CRETIME"
        feetype_dic_id:
          type: column
          value: "FEETYPE_DIC_ID"
        feetype_dic_sys:
          type: column
          value: "FEETYPE_DIC_SYS"
        feetype_dic_subsys:
          type: column
          value: "FEETYPE_DIC_SUBSYS"
        feetype_key:
          type: column
          value: "FEETYPE_KEY"
        feetype_keyname:
          type: column
          value: "FEETYPE_KEYNAME"
        freofpay_dic_id:
          type: column
          value: "FREOFPAY_DIC_ID"
        freofpay_dic_sys:
          type: column
          value: "FREOFPAY_DIC_SYS"
        freofpay_dic_subsys:
          type: column
          value: "FREOFPAY_DIC_SUBSYS"
        freofpay_key:
          type: column
          value: "FREOFPAY_KEY"
        freofpay_keyname:
          type: column
          value: "FREOFPAY_KEYNAME"
        percon_dic_id:
          type: column
          value: "PERCON_DIC_ID"
        percon_dic_sys:
          type: column
          value: "PERCON_DIC_SYS"
        percon_dic_subsys:
          type: column
          value: "PERCON_DIC_SUBSYS"
        percon_key:
          type: column
          value: "PERCON_KEY"
        percon_keyname:
          type: column
          value: "PERCON_KEYNAME"
        fixsum_val:
          type: column
          value: "FIXSUM_VAL"
        fixsum_cur_dic_id:
          type: column
          value: "FIXSUM_CUR_DIC_ID"
        fixsum_cur_dic_sys:
          type: column
          value: "FIXSUM_CUR_DIC_SYS"
        fixsum_cur_dic_subsys:
          type: column
          value: "FIXSUM_CUR_DIC_SUBSYS"
        fx_currency_id:
          type: column
          value: "FIXSUM_CUR_KEY"
        fixsum_cur_keyname:
          type: column
          value: "FIXSUM_CUR_KEYNAME"
        isfeerec:
          type: sql_expression
          value: "ISFEEREC = '1'"
          field_type: BOOLEAN
        note:
          type: column
          value: "NOTE"
        feerate:
          type: column
          value: "FEERATE"
        accbase_dic_id:
          type: column
          value: "ACCBASE_DIC_ID"
        accbase_dic_sys:
          type: column
          value: "ACCBASE_DIC_SYS"
        accbase_dic_subsys:
          type: column
          value: "ACCBASE_DIC_SUBSYS"
        accbase_key:
          type: column
          value: "ACCBASE_KEY"
        accbase_keyname:
          type: column
          value: "ACCBASE_KEYNAME"
        maxsum_val:
          type: column
          value: "MAXSUM_VAL"
        maxsum_cur_dic_id:
          type: column
          value: "MAXSUM_CUR_DIC_ID"
        maxsum_cur_dic_sys:
          type: column
          value: "MAXSUM_CUR_DIC_SYS"
        maxsum_cur_dic_subsys:
          type: column
          value: "MAXSUM_CUR_DIC_SUBSYS"
        mx_currency_id:
          type: column
          value: "MAXSUM_CUR_KEY"
        maxsum_cur_keyname:
          type: column
          value: "MAXSUM_CUR_KEYNAME"
        minsum_val:
          type: column
          value: "MINSUM_VAL"
        minsum_cur_dic_id:
          type: column
          value: "MINSUM_CUR_DIC_ID"
        minsum_cur_dic_sys:
          type: column
          value: "MINSUM_CUR_DIC_SYS"
        minsum_cur_dic_subsys:
          type: column
          value: "MINSUM_CUR_DIC_SUBSYS"
        mn_currency_id:
          type: column
          value: "MINSUM_CUR_KEY"
        minsum_cur_keyname:
          type: column
          value: "MINSUM_CUR_KEYNAME"
        firpaydate:
          type: column
          value: "FIRPAYDATE"
        r_type:
          type: column
          value: "TYPE"
        sta_dic_id:
          type: column
          value: "STA_DIC_ID"
        sta_dic_sys:
          type: column
          value: "STA_DIC_SYS"
        sta_dic_subsys:
          type: column
          value: "STA_DIC_SUBSYS"
        sta_key:
          type: column
          value: "STA_KEY"
        sta_keyname:
          type: column
          value: "STA_KEYNAME"
        proins_type:
          type: column
          value: "PROINS_TYPE"
        proins_id_sys:
          type: column
          value: "PROINS_ID_SYS"
        proins_id_subsys:
          type: column
          value: "PROINS_ID_SUBSYS"
        proins_id_ver:
          type: column
          value: "PROINS_ID_VER"
        proins_id_updtime:
          type: column
          value: "PROINS_ID_UPDTIME"
        proins_id_cretime:
          type: column
          value: "PROINS_ID_CRETIME"
        lifespan_effdate:
          type: column
          value: "LIFESPAN_EFFDATE"
        lifespan_expdate:
          type: column
          value: "LIFESPAN_EXPDATE"
        lifespan_revdate:
          type: column
          value: "LIFESPAN_REVDATE"
        lifespan_revinfo_code:
          type: column
          value: "LIFESPAN_REVINFO_CODE"
        lifespan_revinfo_info:
          type: column
          value: "LIFESPAN_REVINFO_INFO"
      hub_map:
        - target: hub_deal_fee
          rk_field: deal_fee_rk
          business_key_schema: BK-rdv-deal_fee-deal_fee_uuid-PPKC
          on_full_null: good_default
          field_map:
            deal_fee_id:
              type: column
              value: "ID_ID"
        - target: hub_currency
          rk_field: fx_currency_rk
          business_key_schema: BK-rdv-currency-currency_okv_code-OSRF
          on_full_null: good_default
          field_map:
            currency_id:
              type: column
              value: "FIXSUM_CUR_KEY"
        - target: hub_currency
          rk_field: mx_currency_rk
          business_key_schema: BK-rdv-currency-currency_okv_code-OSRF
          on_full_null: good_default
          field_map:
            currency_id:
              type: column
              value: "MAXSUM_CUR_KEY"
        - target: hub_currency
          rk_field: mn_currency_rk
          business_key_schema: BK-rdv-currency-currency_okv_code-OSRF
          on_full_null: good_default
          field_map:
            currency_id:
              type: column
              value: "MINSUM_CUR_KEY"
        - target: hub_deal
          rk_field: secondary_deal_rk
          business_key_schema: BK-rdv-deal-deal_uuid-PPKC
          on_full_null: good_default
          field_map:
            deal_id:
              type: column
              value: "PROINS_ID_ID"
      metrics:
        by_src:
          - wf_dataset_max_date_to
      history_reducer: ODS.SOH_SCD2
      ref_map: []
history_reducers:
  ODS.SOH_SCD2:
    rankers:
      - direction: desc
        expression: |
          "ODS$IS_ACTIVE_FLG" = '1'
        field_type: BOOLEAN
      - direction: desc
        expression: |
          "ODS$EFFECTIVE_FROM_DT"
        field_type: TIMESTAMP
      - direction: desc
        expression: |
          "ODS$PROCESSED_DT"
        field_type: TIMESTAMP
      - direction: desc
        expression: |
          "ODS$DELETED_FLG" = '1'
        field_type: BOOLEAN
