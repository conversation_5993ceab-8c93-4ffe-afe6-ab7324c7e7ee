name: wf_intmng_scol_ods_rdv_mart_job_position_s_postn_scol
type: WORK_FLOW
schema_version: '1.14'
version: 1
tags:
  - 'team: zi10'
  - wf
  - 'area: intmng'
  - 'src: scol'
  - 'prv: ods'
  - 'tgt: rdv'
  - 'cf: cf_intmng_scol_ods_rdv_mart_job_position_s_postn_scol'
  - 'rls: 16'
  - 'uid: 3.SCOL.ZI10.5'
  - v.2
orientation: TB
transaction: {}
sources:
  - short_name: h2_s_postn
    type: DB_TABLE
    object: h2_s_postn
    resource_cd: ods.SBLCL_SOH.H2_S_POSTN
targets:
  - short_name: mt_jb_pstn_s_postn_scl
    resource_cd: ceh.rdv.mart_job_position_s_postn_scol
    table: mart_job_position_s_postn_scol
    schema: rdv
  - short_name: hub_job_position
    resource_cd: ceh.rdv.hub_job_position
    table: hub_job_position
    schema: rdv
  - short_name: hub_employee
    resource_cd: ceh.rdv.hub_employee
    table: hub_employee
    schema: rdv
local_metrics:
  wf_dataset_max_date_to:
    query: max(ods$effective_from_dt)
    target: stage_T_input
    on_null: .conf.algos["3.SCOL.ZI10.5"].by_src["ods.SBLCL_SOH.H2_S_POSTN"].wf_dataset_max_date_to

mappings:
  marts:
    - target: mt_jb_pstn_s_postn_scl
      source: h2_s_postn
      short_name: mt_jb_pstn_s_postn_scl
      algorithm_uid: 3.SCOL.ZI10.5
      delta_mode: new
      where_clause: |
        ods$effective_from_dt >= '${conf.algos["3.SCOL.ZI10.5"].by_src["ods.SBLCL_SOH.H2_S_POSTN"].wf_dataset_max_date_to}'
        and
        ods$effective_from_dt <= '${conf.algos["3.SCOL.ZI10.5"].by_src["ods.SBLCL_SOH.H2_S_POSTN"].dataset_max_date_to}'
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: ods$effective_from_dt
          - param_source: const
            param_value: day

      field_map:
        deleted_flg:
          type: sql_expression
          value: ods$deleted_flg = '1'
          field_type: BOOLEAN

        row_id:
          type: column
          value: row_id
        bu_id:
          type: column
          value: bu_id
        cmpns_curcy_cd:
          type: column
          value: cmpns_curcy_cd
        compensatable_flg:
          type: column
          value: compensatable_flg
        conflict_id:
          type: column
          value: conflict_id
        created:
          type: column
          value: created
        created_by:
          type: column
          value: created_by
        db_last_upd:
          type: column
          value: db_last_upd
        db_last_upd_src:
          type: column
          value: db_last_upd_src
        desc_text:
          type: column
          value: desc_text
        end_dt:
          type: column
          value: end_dt
        integration_id:
          type: column
          value: integration_id
        last_upd:
          type: column
          value: last_upd
        last_upd_by:
          type: column
          value: last_upd_by
        modification_num:
          type: column
          value: modification_num
        name:
          type: column
          value: name
        ou_id:
          type: column
          value: ou_id
        par_postn_id:
          type: column
          value: par_postn_id
        par_row_id:
          type: column
          value: par_row_id
        pgroup_id:
          type: column
          value: pgroup_id
        postn_type_cd:
          type: column
          value: postn_type_cd
        pr_emp_id:
          type: column
          value: pr_emp_id
        pr_postn_addr_id:
          type: column
          value: pr_postn_addr_id
        pr_postn_id:
          type: column
          value: pr_postn_id
        pr_terr_id:
          type: column
          value: pr_terr_id
        start_dt:
          type: column
          value: start_dt
        x_branch_office:
          type: column
          value: x_branch_office
        x_collect_center_code:
          type: column
          value: x_collect_center_code
        x_collect_center_codes_str:
          type: column
          value: x_collect_center_codes_str
        x_primary_collect_cent_id:
          type: column
          value: x_primary_collect_cent_id

      hub_map:
        - target: hub_job_position
          rk_field: job_position_rk
          business_key_schema: BK-rdv-job_position-s_postn_seq-SCOL
          field_map: 
            job_position_id:
              type: column
              value: row_id
          on_full_null: good_default
          
        - target: hub_job_position
          rk_field: par_postn_job_position_rk
          business_key_schema: BK-rdv-job_position-s_postn_seq-SCOL
          field_map: 
            job_position_id:
              type: column
              value: par_postn_id
          on_full_null: good_default
          
        - target: hub_employee
          rk_field: pr_emp_id_rk
          business_key_schema: BK-rdv-employee-employee_seq-SCOL
          field_map: 
            employee_id:
              type: column
              value: pr_emp_id
          on_full_null: good_default
          
      ref_map: []
      metrics:
        by_src:
          - wf_dataset_max_date_to
      history_reducer: ods_scd2_local
history_reducers:
  ods_scd2_local:
    rankers:
      - direction: desc
        expression: ods$is_active_flg = '1'
        field_type: BOOLEAN
      - direction: desc
        expression: ods$effective_from_dt
        field_type: TIMESTAMP
      - direction: desc
        expression: ods$processed_dt
        field_type: TIMESTAMP
      - direction: desc
        expression: ods$deleted_flg = '1'
        field_type: BOOLEAN