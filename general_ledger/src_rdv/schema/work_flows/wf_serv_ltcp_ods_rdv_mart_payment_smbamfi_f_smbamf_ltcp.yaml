name: wf_serv_ltcp_ods_rdv_mart_payment_smbamfi_f_smbamf_ltcp
type: WORK_FLOW
schema_version: '1.16'
version: 1
tags:
- 'team: CKB7'
- 'author: <PERSON><PERSON><PERSON><PERSON><PERSON>'
- 'area: serv'
- 'src: ltcp'
- 'prv: ods'
- 'tgt: rdv'
- 'cf: cf_serv_ltcp_ods_rdv_mart_payment_smbamfi_f_smbamf_ltcp'
- 'wf: wf_serv_ltcp_ods_rdv_mart_payment_smbamfi_f_smbamf_ltcp'
- 'rls: 98'
- 'tgt-tbl: rdv.mart_payment_smbamfi_f_smbamf_ltcp'
- 'src-tbl: LTCP_SOH.H2_SMBAMFI_F_SMBAMF'
- 'uid: 35.LTCP.CKB7.2'
- wf
orientation: TB
sources:
- short_name: smbamfi_f_smbamf2
  type: DB_TABLE
  object: ods_ltcp_soh_h2_smbamfi_f_smbamf
  resource_cd: ods.LTCP_SOH.H2_SMBAMFI_F_SMBAMF
targets:
- short_name: mart_payment_smb_1
  schema: rdv
  table: mart_payment_smbamfi_f_smbamf_ltcp
  resource_cd: ceh.rdv.mart_payment_smbamfi_f_smbamf_ltcp
- short_name: hub_payment
  schema: rdv
  table: hub_payment
  resource_cd: ceh.rdv.hub_payment
- short_name: hub_currency
  schema: rdv
  table: hub_currency
  resource_cd: ceh.rdv.hub_currency
local_metrics:
  H2_SMBAMFI_F_SMBAMF_max_date:
    target: stage_T_input
    query: max(date_trunc('second', "ODS$EFFECTIVE_FROM_DT"))
    on_null: 
      .conf.algos."35.LTCP.CKB7.2".by_src."ods.LTCP_SOH.H2_SMBAMFI_F_SMBAMF".dataset_max_date_to
history_reducers:
  ods_reducer:
    rankers:
    - direction: desc
      expression: |
        etl.try_cast2bool("ODS$IS_ACTIVE_FLG")
      field_type: BOOLEAN
    - direction: desc
      expression: |
        "ODS$EFFECTIVE_FROM_DT"
      field_type: TIMESTAMP
    - direction: desc
      expression: |
        "ODS$PROCESSED_DT"
      field_type: TIMESTAMP
    - direction: desc
      expression: |
        etl.try_cast2bool("ODS$DELETED_FLG")
      field_type: BOOLEAN
mappings:
  marts:
  - short_name: mart_pay_f_smbamf_1
    algorithm_uid: 35.LTCP.CKB7.2
    algorithm_uid_2: '1'
    target: mart_payment_smb_1
    source: smbamfi_f_smbamf2
    where_clause:
      engine: jq
      template: |
        "ODS$EFFECTIVE_FROM_DT" >= '{from}' and "ODS$EFFECTIVE_FROM_DT" < '{to}'
      vars:
        from: 
          .conf.algos."35.LTCP.CKB7.2".by_src."ods.LTCP_SOH.H2_SMBAMFI_F_SMBAMF".wf_dataset_max_date_to
        to: 
          .conf.algos."35.LTCP.CKB7.2".by_src."ods.LTCP_SOH.H2_SMBAMFI_F_SMBAMF".dataset_max_date_to
    metrics:
      by_src:
      - save_as: wf_dataset_max_date_to
        metric: H2_SMBAMFI_F_SMBAMF_max_date
    delta_mode: new
    history_reducer: ods_reducer
    field_map:
      effective_dttm:
        type: column
        value: ODS$EFFECTIVE_FROM_DT
      deleted_flg:
        type: sql_expression
        value: etl.try_cast2bool("ODS$DELETED_FLG")
        field_type: BOOLEAN
      ods_create_id:
        type: column
        value: ODS$CREATE_ID
      ods_update_id:
        type: column
        value: ODS$UPDATE_ID
      ods_effective_from_csn:
        type: column
        value: ODS$EFFECTIVE_FROM_CSN
      ods_effective_to_csn:
        type: column
        value: ODS$EFFECTIVE_TO_CSN
      ods_effective_to_dt:
        type: column
        value: ODS$EFFECTIVE_TO_DT
      ods_processed_dt:
        type: column
        value: ODS$PROCESSED_DT
      amo_cur_dic_id:
        type: column
        value: AMO_CUR_DIC_ID
      amo_cur_dic_subsys:
        type: column
        value: AMO_CUR_DIC_SUBSYS
      amo_cur_dic_sys:
        type: column
        value: AMO_CUR_DIC_SYS
      amo_currency_id:
        type: column
        value: AMO_CUR_KEY
      amo_cur_keyname:
        type: column
        value: AMO_CUR_KEYNAME
      amo_val:
        type: column
        value: AMO_VAL
      tar_id_cretime:
        type: column
        value: TAR_ID_CRETIME
      tar_id_id:
        type: column
        value: TAR_ID_ID
      tar_id_subsys:
        type: column
        value: TAR_ID_SUBSYS
      tar_id_sys:
        type: column
        value: TAR_ID_SYS
      tar_id_updtime:
        type: column
        value: TAR_ID_UPDTIME
      tar_id_ver:
        type: column
        value: TAR_ID_VER
      payment_id:
        type: column
        value: PK_SMBAMFI_ID_ID
      pk_hash_smbamfi_f_smbamf_dk:
        type: column
        value: PK_HASH_SMBAMFI_F_SMBAMF
    ref_map: []
    hub_map:
    - target: hub_payment
      rk_field: payment_rk
      business_key_schema: BK-rdv-payment-payment_uuid-LTCP
      on_full_null: new_rk
      field_map:
        payment_id:
          type: column
          value: PK_SMBAMFI_ID_ID
    - target: hub_currency
      rk_field: amo_currency_rk
      business_key_schema: BK-rdv-currency-currency_okv_code-OSRF
      on_full_null: good_default
      field_map:
        currency_id:
          type: column
          value: AMO_CUR_KEY
