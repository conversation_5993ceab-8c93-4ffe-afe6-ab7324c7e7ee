schema_version: '1.1'
type: DB_TABLE
name: savcsa_retailomssellapplication_participants
data_source_type: GENERIC
source_system: SAVC
data_capture_mode: increment

columns:
  - name: changeid
    type: TEXT
  - name: partyroletype
    type: TEXT
  - name: changetimestamp
    type: TEXT
  - name: bankrepresentative_employee_id_id
    type: TEXT
  - name: party_id_id
    type: TEXT
  - name: clientrepresentatives_party_id_id
    type: TEXT
  - name: hdp_processed_dttm
    type: TIMESTAMP
  - name: participants_hash
    type: TEXT
  - name: bankrepresentative_employee_id_createtime
    type: TEXT
  - name: bankrepresentative_employee_id_subsystem
    type: TEXT
  - name: bankrepresentative_employee_id_system
    type: TEXT
  - name: bankrepresentative_employee_id_updatetime
    type: TEXT
  - name: bankrepresentative_employee_id_version
    type: TEXT
  - name: party_id_createtime
    type: TEXT
  - name: party_id_subsystem
    type: TEXT
  - name: party_id_system
    type: TEXT
  - name: party_id_updatetime
    type: TEXT
  - name: party_id_version
    type: TEXT
  - name: clientrepresentatives_party_id_createtime
    type: TEXT
  - name: clientrepresentatives_party_id_subsystem
    type: TEXT
  - name: clientrepresentatives_party_id_system
    type: TEXT
  - name: clientrepresentatives_party_id_updatetime
    type: TEXT
  - name: clientrepresentatives_party_id_version
    type: TEXT