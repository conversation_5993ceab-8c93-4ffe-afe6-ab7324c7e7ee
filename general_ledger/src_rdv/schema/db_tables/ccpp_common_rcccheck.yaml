schema_version: '1.1'
name: ccpp_common_information
columns:
- name: guid
  type: TEXT
- name: commit_ts
  type: TIMESTAMP
- name: op_csn
  type: TEXT
- name: op_seq
  type: TEXT
- name: op_type
  type: TEXT
- name: processed_dt
  type: TIMESTAMP
- name: dte
  type: TEXT
- name: parent_guid
  type: TEXT
- name: object_guid
  type: TEXT
- name: type
  type: TEXT
- name: check_id_id
  type: TEXT
- name: check_id_system
  type: TEXT
- name: check_id_subsystem
  type: TEXT
- name: check_id_version
  type: INTEGER
- name: check_id_updatetime
  type: TIMESTAMP
- name: check_id_createtime
  type: TIMESTAMP
- name: metaclass
  type: TEXT
data_source_type: GENERIC
source_system: CCPP_CRCA
data_capture_mode: increment
type: DB_TABLE