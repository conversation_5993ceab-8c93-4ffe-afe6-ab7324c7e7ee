schema_version: '1.1'
type: DB_TABLE
name: dko_deal
data_source_type: GENERIC
source_system: DKO
data_capture_mode: increment

columns:

  - name: commit_ts
    type: BIGINT
  - name: mdm_id
    type: TEXT
  - name: deal_id
    type: TEXT
  - name: created_date
    type: TEXT
  - name: biscuit_branch
    type: TEXT
  - name: deal_type
    type: TEXT
  - name: deal_status
    type: TEXT
  - name: account_type
    type: TEXT
  - name: source_channel
    type: TEXT
  - name: sales_channel
    type: TEXT
  - name: personnel_number
    type: TEXT
  - name: department_code
    type: TEXT
  - name: user_login
    type: TEXT
  - name: service_package
    type: TEXT
  - name: notification_package_id
    type: TEXT
  - name: bonus
    type: TEXT
  - name: account_number
    type: TEXT
  - name: dko_number
    type: TEXT
  - name: dko_open_date
    type: TEXT
  - name: client_id
    type: TEXT
  - name: confidant_id
    type: TEXT
  - name: poa_number
    type: TEXT
  - name: dko_deal_date
    type: TEXT
  - name: single_phased
    type: TEXT
  - name: sign_type
    type: TEXT
  - name: rezident_flag
    type: TEXT
  - name: op_type
    type: TEXT
  - name: hdp_processed_dttm
    type: TIMESTAMP