schema_version: '1.0'
name: h2_client_tr_system
columns:
  - name: ods$effective_from_dt
    type: TIMESTAMP
  - name: ods$effective_to_dt
    type: TIMESTAMP
  - name: ods$effective_from_csn
    type: DECIMAL
  - name: ods$effective_to_csn
    type: DECIMAL
  - name: ods$is_active_flg
    type: CHAR
    params:
      length: 1
  - name: ods$deleted_flg
    type: CHAR
    params:
      length: 1
  - name: ods$create_id
    type: DECIMAL
    params:
      precision: 38
      scale: 0
  - name: ods$update_id
    type: DECIMAL
    params:
      precision: 38
      scale: 0
  - name: ods$processed_dt
    type: TIMESTAMP
  - name: client_code
    type: TEXT
  - name: system_name
    type: TEXT
  - name: cert_id
    type: TEXT
  - name: last_update
    type: TIMESTAMP
  - name: login_mt
    type: TEXT
data_source_type: GENERIC
source_system: ZFNT
data_capture_mode: increment
type: DB_TABLE
