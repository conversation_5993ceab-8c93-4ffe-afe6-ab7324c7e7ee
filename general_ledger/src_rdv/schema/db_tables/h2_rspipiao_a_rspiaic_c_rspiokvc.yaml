schema_version: '1.1'
name: h2_rspipiao_a_rspiaic_c_rspiokvc
columns:
  - name: "ODS$EFFECTIVE_FROM_DT"
    type: TIMESTAMP
  - name: "ODS$EFFECTIVE_TO_DT"
    type: TIMESTAMP
  - name: "ODS$EFFECTIVE_FROM_CSN"
    type: DECIMAL
  - name: "ODS$EFFECTIVE_TO_CSN"
    type: DECIMAL
  - name: "ODS$IS_ACTIVE_FLG"
    type: TEXT
  - name: "ODS$DELETED_FLG"
    type: TEXT
  - name: "ODS$CREATE_ID"
    type: DECIMAL
    params:
      precision: 38
      scale: 0
  - name: "ODS$UPDATE_ID"
    type: DECIMAL
    params:
      precision: 38
      scale: 0
  - name: "ODS$PROCESSED_DT"
    type: TIMESTAMP
  - name: "PK_RSPIPIAO_ID_ID"
    type: TEXT
  - name: "CUR_DIC_ID"
    type: TEXT
  - name: "CUR_DIC_SUBSYS"
    type: TEXT
  - name: "CUR_DIC_SYS"
    type: TEXT
  - name: "CUR_KEY"
    type: TEXT
  - name: "CUR_KEYNAME"
    type: TEXT
  - name: "TYPE"
    type: TEXT
data_source_type: GENERIC
source_system: PPRS
data_capture_mode: increment
type: DB_TABLE
