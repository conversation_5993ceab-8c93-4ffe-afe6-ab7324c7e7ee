schema_version: "1.1"
type: DB_TABLE
name: grnt_kmb_kmbguaranteeseller
data_source_type: GENERIC
source_system: GRNT
data_capture_mode: increment
#where_clause: "deleted_flg = '0'"
columns:
  - name: application_id_id
    type: TEXT
  - name: commit_ts
    type: TIMESTAMP
  - name: op_csn
    type: TEXT
  - name: op_seq
    type: INTEGER
  - name: op_type
    type: TEXT
  - name: processed_dt
    type: TIMESTAMP
  - name: dte
    type: TEXT
  - name: guid
    type: TEXT
  - name: parent_guid
    type: TEXT
  - name: object_guid
    type: TEXT
  - name: application_id_system
    type: TEXT
  - name: application_id_version
    type: INTEGER
  - name: application_id_updatetime
    type: TIMESTAMP
  - name: application_id_createtime
    type: TIMESTAMP
  - name: application_type
    type: TEXT
