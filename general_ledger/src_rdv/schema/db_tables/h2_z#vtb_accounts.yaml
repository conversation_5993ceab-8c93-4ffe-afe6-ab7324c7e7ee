schema_version: '1.1'
name: h2_z#vtb_accounts
columns:
  - name: ods$effective_from_dt
    type: TIMESTAMP
  - name: ods$effective_to_dt
    type: TIMESTAMP
  - name: ods$effective_from_csn
    type: DECIMAL
  - name: ods$effective_to_csn
    type: DECIMAL
  - name: ods$is_active_flg
    type: CHAR
    params:
      length: '1'
  - name: ods$deleted_flg
    type: CHAR
    params:
      length: '1'
  - name: ods$create_id
    type: DECIMAL
    params:
      precision: '38'
      scale: 0
  - name: ods$update_id
    type: DECIMAL
    params:
      precision: '38'
      scale: 0
  - name: ods$processed_dt
    type: TIMESTAMP
  - name: id
    type: DECIMAL
  - name: c_acc_type
    type: DECIMAL
  - name: c_balance
    type: DECIMAL
  - name: c_balance_socpay
    type: DECIMAL
  - name: c_block_amount
    type: DECIMAL
  - name: c_client
    type: DECIMAL
  - name: c_currency
    type: DECIMAL
  - name: c_date_update
    type: TIMESTAMP
  - name: c_expiration_date
    type: TIMESTAMP
  - name: c_filial
    type: DECIMAL
  - name: c_filial_code
    type: TEXT
  - name: c_is_arrested
    type: TEXT
  - name: c_may_credit
    type: TEXT
  - name: c_may_debit
    type: TEXT
  - name: c_num
    type: TEXT
  - name: c_pan
    type: TEXT
  - name: c_prod_type
    type: DECIMAL
  - name: c_status
    type: DECIMAL
  - name: c_system
    type: DECIMAL
  - name: sn
    type: DECIMAL
  - name: su
    type: DECIMAL
data_source_type: GENERIC
source_system: RTLL
data_capture_mode: increment
type: DB_TABLE
