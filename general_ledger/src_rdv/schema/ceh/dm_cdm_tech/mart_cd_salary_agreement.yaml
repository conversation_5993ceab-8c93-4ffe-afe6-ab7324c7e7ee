schema_version: '1.0'
type: MART
name: mart_cd_salary_agreement
schema: dm_cdm_tech
columns:
  - name: agreement_rk
    type: DECIMAL
  - name: contract_id
    type: TEXT
  - name: contract_type_cd
    type: TEXT
  - name: z_code_cd
    type: TEXT
  - name: customer_global_rk
    type: INTEGER
  - name: contract_status_cd
    type: TEXT
  - name: open_dt
    type: TEXT
  - name: close_dt
    type: TEXT
  - name: activate_dt
    type: TEXT
  - name: contract_num
    type: TEXT
  - name: tax_payer_num
    type: TEXT
  - name: migrate_from_info
    type: TEXT
  - name: comission_type_cd
    type: TEXT
  - name: fix_comission_rur_amt
    type: DECIMAL
  - name: prc_comission_rur_part
    type: DECIMAL
  - name: internal_org_rk
    type: INTEGER
  - name: internal_org_id
    type: TEXT
  - name: prc_min_comission_rur_amt
    type: DECIMAL
  - name: fix_comission_usd_amt
    type: DECIMAL
  - name: prc_comission_usd_part
    type: DECIMAL
  - name: prc_min_comission_usd_amt
    type: DECIMAL
  - name: fix_comission_eur_amt
    type: DECIMAL
  - name: prc_comission_eur_part
    type: DECIMAL
  - name: prc_min_comission_eur_amt
    type: DECIMAL
  - name: source_system_cd
    type: TEXT
  - name: is_active_flg
    type: TEXT
  - name: effective_from_dttm
    type: TIMESTAMP
  - name: effective_to_dttm
    type: TIMESTAMP
  - name: load_id
    type: INTEGER
  - name: processed_dttm
    type: TIMESTAMP
  - name: layer_id
    type: INTEGER
  - name: contract_segment_cd
    type: TEXT
  - name: free_p2p_transfer_flg
    type: TEXT
  - name: accrued_interest_flg
    type: TEXT
  - name: free_loyalty_program_flg
    type: TEXT
  - name: free_dbo_cash_settlement_flg
    type: TEXT
  - name: valid_from_dttm
    type: TIMESTAMP
  - name: valid_to_dttm
    type: TIMESTAMP
  - name: hdp_processed_dttm
    type: TIMESTAMP
  - name: deleted_flg
    type: BOOLEAN 
    nullable: false
  - name: src_cd
    type: TEXT
  - name: version_id
    type: BIGINT 
    nullable: false
  - name: hash_diff
    type: CHAR
    params:
      length: 32
    nullable: false
  - name: valid_flg
    type: BOOLEAN 
    nullable: false
  - name: invalid_id
    type: BIGINT
    nullable: false
hash_src_fields:
  - contract_id
  - contract_type_cd
  - z_code_cd
  - contract_status_cd
  - open_dt
  - close_dt
  - activate_dt
  - contract_num
  - tax_payer_num
  - migrate_from_info
  - comission_type_cd
  - fix_comission_rur_amt
  - prc_comission_rur_part
  - internal_org_id
  - prc_min_comission_rur_amt
  - fix_comission_usd_amt
  - prc_comission_usd_part
  - prc_min_comission_usd_amt
  - fix_comission_eur_amt
  - prc_comission_eur_part
  - prc_min_comission_eur_amt
  - source_system_cd
  - is_active_flg
  - effective_from_dttm
  - effective_to_dttm
  - load_id
  - processed_dttm
  - layer_id
  - contract_segment_cd
  - free_p2p_transfer_flg
  - accrued_interest_flg
  - free_loyalty_program_flg
  - free_dbo_cash_settlement_flg
  - valid_from_dttm
  - valid_to_dttm
multi_fields: []
ref_fields: []
hub_fields: []