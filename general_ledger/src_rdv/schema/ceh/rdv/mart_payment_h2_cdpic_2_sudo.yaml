schema_version: '1.0'
type: MART
name: mart_payment_h2_cdpic_2_sudo
schema: rdv
columns:
- name: payment_rk
  type: BIGINT
  nullable: false
- name: effective_dttm
  type: TIMESTAMP
  nullable: false
- name: env_id_dk
  type: TEXT
  nullable: false
- name: src_cd
  type: TEXT
  nullable: false
- name: controller_employee_rk
  type: BIGINT
  nullable: true
- name: empbank_employee_rk
  type: BIGINT
  nullable: true
- name: executor_employee_rk
  type: BIGINT
  nullable: true
- name: sig_dosdoc_id_sys
  type: TEXT
  nullable: true
- name: sig_dosdoc_id_updtime
  type: TIMESTAMP
  nullable: true
- name: sig_dosdoc_id_ver
  type: DECIMAL
  nullable: true
- name: sig_signtype
  type: TEXT
  nullable: true
- name: purpose
  type: TEXT
  nullable: true
- name: sig_dosdoc_id_subsys
  type: TEXT
  nullable: true
- name: payment_id
  type: TEXT
  nullable: true
- name: id_cretime
  type: TIMESTAMP
  nullable: true
- name: id_subsys
  type: TEXT
  nullable: true
- name: id_sys
  type: TEXT
  nullable: true
- name: id_updtime
  type: TIMESTAMP
  nullable: true
- name: id_ver
  type: DECIMAL
  nullable: true
- name: controller_employee_id
  type: TEXT
  nullable: true
- name: empbank_employee_id
  type: TEXT
  nullable: true
- name: executor_employee_id
  type: TEXT
  nullable: true
- name: deleted_flg
  type: BOOLEAN
  nullable: false
- name: hash_diff
  type: CHAR
  nullable: false
  params:
    length: 32
- name: invalid_id
  type: BIGINT
  nullable: false
- name: version_id
  type: BIGINT
  nullable: false
- name: valid_flg
  type: BOOLEAN
  nullable: false
hash_src_fields:
- sig_dosdoc_id_sys
- sig_dosdoc_id_updtime
- sig_dosdoc_id_ver
- sig_signtype
- purpose
- sig_dosdoc_id_subsys
- payment_id
- id_cretime
- id_subsys
- id_sys
- id_updtime
- id_ver
- controller_employee_id
- empbank_employee_id
- executor_employee_id
multi_fields:
- name: env_id_dk
  field_data_quality_checks:
  - name: IsEmpty
- name: effective_dttm
  field_data_quality_checks: []

ref_fields: []
# history_field: effective_dttm
hub_fields:
- name: payment_rk
  hub_table: hub_payment
  hub_schema: rdv
  is_bk: true
- name: controller_employee_rk
  hub_table: hub_employee
  hub_schema: rdv
  is_bk: false
- name: empbank_employee_rk
  hub_table: hub_employee
  hub_schema: rdv
  is_bk: false
- name: executor_employee_rk
  hub_table: hub_employee
  hub_schema: rdv
  is_bk: false
