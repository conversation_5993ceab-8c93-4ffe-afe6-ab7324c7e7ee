schema_version: '1.0'
type: MART
name: mart_cash_application_cbbcoa_p_cbcoiir_cpro
schema: rdv
columns:
  - name: cash_application_rk
    type: BIGINT
    nullable: false
  - name: pk_hash_cbbcoa_p_cbcoiir_dk
    type: UUID
    nullable: false
  - name: effective_dttm
    type: TIMESTAMP
    nullable: false
  - name: src_cd
    type: TEXT
    nullable: false
  - name: deleted_flg
    type: BOOLEAN
    nullable: false
  - name: is_active_flg
    type: BOOLEAN
    nullable: true
  - name: hash_diff
    type: CHAR
    nullable: false
    params:
      length: 32
  - name: invalid_id
    type: BIGINT
    nullable: false
  - name: version_id
    type: BIGINT
    nullable: false
  - name: valid_flg
    type: BOOLEAN
    nullable: false
  - name: address
    type: TEXT
    nullable: true
  - name: birdate
    type: TIMESTAMP
    nullable: true
  - name: birpla
    type: TEXT
    nullable: true
  - name: country
    type: TEXT
    nullable: true
  - name: idedoc_dulcode
    type: TEXT
    nullable: true
  - name: idedoc_duldate
    type: TIMESTAMP
    nullable: true
  - name: idedoc_duldepcode
    type: TEXT
    nullable: true
  - name: idedoc_dulgiveby
    type: TEXT
    nullable: true
  - name: idedoc_dulnum
    type: TEXT
    nullable: true
  - name: idedoc_dulser
    type: TEXT
    nullable: true
  - name: name
    type: TEXT
    nullable: true
  - name: resident
    type: TEXT
    nullable: true
  - name: par_id_cretime
    type: TIMESTAMP
    nullable: true
  - name: par_id_id
    type: TEXT
    nullable: true
  - name: par_id_subsys
    type: TEXT
    nullable: true
  - name: par_id_sys
    type: TEXT
    nullable: true
  - name: par_id_updtime
    type: TIMESTAMP
    nullable: true
  - name: par_id_ver
    type: BIGINT
    nullable: true
  - name: counterparty_rk
    type: BIGINT
    nullable: true
  - name: role
    type: TEXT
    nullable: true
  - name: pk_cbbcoa_id_id
    type: TEXT
    nullable: true
hash_src_fields:
  - is_active_flg
  - address
  - birdate
  - birpla
  - country
  - idedoc_dulcode
  - idedoc_duldate
  - idedoc_duldepcode
  - idedoc_dulgiveby
  - idedoc_dulnum
  - idedoc_dulser
  - name
  - resident
  - par_id_cretime
  - par_id_id
  - par_id_subsys
  - par_id_sys
  - par_id_updtime
  - par_id_ver
  - role
  - pk_cbbcoa_id_id
multi_fields:
  - name: pk_hash_cbbcoa_p_cbcoiir_dk
    field_data_quality_checks:
      - name: IsEmpty
ref_fields: []
history_field: effective_dttm
hub_fields:
  - name: cash_application_rk
    hub_table: hub_cash_application
    hub_schema: rdv
    is_bk: true
  - name: counterparty_rk
    hub_table: hub_client
    hub_schema: rdv
    is_bk: false
