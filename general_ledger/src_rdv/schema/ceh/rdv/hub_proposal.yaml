schema_version: '1.2'
name: hub_proposal
schema: rdv
columns:
  - name: proposal_rk
    type: BIGINT
    nullable: false
  - name: proposal_id
    type: TEXT
    nullable: false
  - name: src_cd
    type: TEXT
    nullable: false
  - name: bk_type
    type: TEXT
    nullable: false
  - name: invalid_id
    type: BIGINT
    nullable: false
  - name: version_id
    type: BIGINT
    nullable: false
type: HUB
rk_field: proposal_rk
bk_fields:
  - name: proposal_id
resource_cd: ceh.rdv.hub_proposal
business_key_schemas:
  - name: BK-rdv-proposal-proposal_seq-MOLE
    src_cd: MOLE
    bk_type: proposal_seq
    fields:
      - name: proposal_id
        field_data_quality_checks:
          - name: IsNull
        field_transform_chains:
          - name: Coalesce
            params:
              value: ~null~
    split_resource: true
  - name: BK-rdv-proposal-proposal_uuid-MOLE
    src_cd: MOLE
    bk_type: proposal_uuid
    fields:
      - name: proposal_id
        field_data_quality_checks:
          - name: IsEmpty
        field_transform_chains:
          - name: Cast
            params:
              to_type: VARCHAR
          - name: Coalesce
            params:
              value: ~null~
    split_resource: true
  - name: BK-rdv-proposal-proposal_seq-ASPK
    src_cd: ASPK
    bk_type: proposal_seq
    fields:
      - name: proposal_id
        field_data_quality_checks:
          - name: IsEmpty
        field_transform_chains:
          - name: Cast
            params:
              to_type: VARCHAR
          - name: Coalesce
            params:
              value: ~null~
    split_resource: true