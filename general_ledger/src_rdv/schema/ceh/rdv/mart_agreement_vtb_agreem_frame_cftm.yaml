schema_version: '1.0'
name: mart_agreement_vtb_agreem_frame_cftm
schema: rdv
type: MART
columns:
- name: hash_diff
  nullable: false
  type: CHAR
  params:
    length: 32
- name: src_cd
  nullable: false
  type: TEXT
- name: invalid_id
  nullable: false
  type: BIGINT
- name: version_id
  nullable: false
  type: BIGINT
- name: valid_flg
  nullable: false
  type: BOOLEAN
- name: agreement_rk
  nullable: false
  type: BIGINT
- name: effective_date
  nullable: false
  type: DATE
- name: deleted_flg
  nullable: false
  type: BOOLEAN
- name: ods_processed_dt
  nullable: true
  type: TIMESTAMP
- name: id
  nullable: true
  type: BIGINT
- name: c_calc_from_begin
  nullable: true
  type: TEXT
- name: client_rk
  nullable: true
  type: BIGINT
- name: c_client
  nullable: true
  type: BIGINT
- name: c_cnt_der
  nullable: true
  type: DECIMAL
- name: c_date_time_begin
  nullable: true
  type: TIMESTAMP
- name: c_date_time_end
  nullable: true
  type: TIMESTAMP
- name: c_deals_arr
  nullable: true
  type: BIGINT
- name: c_frame_avtopik
  nullable: true
  type: TEXT
- name: c_frame_type
  nullable: true
  type: BIGINT
- name: c_hold_in_saldo
  nullable: true
  type: TEXT
- name: c_per_der
  nullable: true
  type: BIGINT
- name: c_prc_der
  nullable: true
  type: DECIMAL
- name: rko_deal_rk
  nullable: true
  type: BIGINT
- name: c_rko_ref
  nullable: true
  type: BIGINT
- name: c_term_hist_arr
  nullable: true
  type: BIGINT
- name: product_rk
  nullable: true
  type: BIGINT
- name: c_type_ref
  nullable: true
  type: BIGINT
- name: c_vtb_acc_arr
  nullable: true
  type: BIGINT
- name: c_vtb_ex_bs
  nullable: true
  type: BIGINT
- name: c_vtb_spread_0
  nullable: true
  type: BIGINT
- name: sn
  nullable: true
  type: BIGINT
- name: su
  nullable: true
  type: BIGINT
hash_src_fields:
- id
- c_calc_from_begin
- c_client
- c_cnt_der
- c_date_time_begin
- c_date_time_end
- c_deals_arr
- c_frame_avtopik
- c_frame_type
- c_hold_in_saldo
- c_per_der
- c_prc_der
- c_rko_ref
- c_term_hist_arr
- c_type_ref
- c_vtb_acc_arr
- c_vtb_ex_bs
- c_vtb_spread_0
- sn
- su
multi_fields: []
ref_fields: []
hub_fields:
- name: agreement_rk
  hub_table: hub_agreement
  hub_schema: rdv
  is_bk: true
- name: client_rk
  hub_table: hub_client
  hub_schema: rdv
  is_bk: false
- name: rko_deal_rk
  hub_table: hub_deal
  hub_schema: rdv
  is_bk: false
- name: product_rk
  hub_table: hub_product
  hub_schema: rdv
  is_bk: false
history_field: effective_date