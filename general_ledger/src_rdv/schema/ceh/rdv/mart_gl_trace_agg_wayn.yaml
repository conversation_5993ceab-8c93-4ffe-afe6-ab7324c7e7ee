schema_version: '1.0'
type: MART
name: mart_gl_trace_agg_wayn
schema: rdv

columns:
  - name: hash_diff
    type: CHAR
    nullable: false
    params:
      length: 32
  - name: src_cd
    type: TEXT
    nullable: false
  - name: invalid_id
    type: BIGINT
    nullable: false
  - name: version_id
    type: BIGINT
    nullable: false
  - name: deleted_flg
    type: BOOLEAN
    nullable: false
  - name: valid_flg
    type: BOOLEAN
    nullable: false
  - name: gl_trace_agg_dk
    type: BIGINT
    nullable: false
  - name: id
    type: BIGINT
    nullable: false
  - name: dr_main_account_rk
    type: BIGINT
    nullable: true
  - name: dr_main_account
    type: BIGINT
    nullable: true
  - name: dr_account_rk
    type: BIGINT
    nullable: true
  - name: dr_account
    type: BIGINT
    nullable: true
  - name: cr_account_rk
    type: BIGINT
  - name: deal_operation_rk
    type: BIGINT
    nullable: true
  - name: cr_account
    type: BIGINT
    nullable: true
  - name: dr_service
    type: BIGINT
    nullable: true
  - name: entry_role
    type: TEXT
    nullable: true
  - name: cr_account_number
    type: TEXT
    nullable: true
  - name: cr_main_entry_rk
    type: BIGINT
    nullable: true
  - name: cr_main_entry
    type: BIGINT
    nullable: true
  - name: dr_tariff
    type: BIGINT
    nullable: true
  - name: curr_rk
    type: BIGINT
    nullable: true
  - name: curr
    type: TEXT
    nullable: true
  - name: dr_account_number
    type: TEXT
    nullable: true
  - name: m_transaction_rk
    type: BIGINT
    nullable: true
  - name: m_transaction_id
    type: BIGINT
    nullable: true
  - name: db_date
    type: TIMESTAMP
    nullable: true
  - name: partition_key
    type: TEXT
    nullable: true
  - name: order_date
    type: TIMESTAMP
    nullable: true
  - name: dr_main_entry_rk
    type: BIGINT
    nullable: true
  - name: dr_main_entry_id
    type: BIGINT
    nullable: true
  - name: description
    type: TEXT
    nullable: true
  - name: gl_doc_id
    type: BIGINT
    nullable: true
  - name: cr_main_account_rk
    type: BIGINT
    nullable: true
  - name: cr_main_account
    type: BIGINT
    nullable: true
  - name: gl_transfer_id
    type: BIGINT
    nullable: true
  - name: amount
    type: DECIMAL
    nullable: true
  - name: cr_service
    type: BIGINT
    nullable: true
  - name: cr_tariff
    type: BIGINT
    nullable: true
  - name: trans_role
    type: TEXT
    nullable: true
 

hash_src_fields:
    - id
    - dr_main_account_rk
    - dr_main_account
    - dr_account_rk
    - dr_account
    - cr_account_rk
    - cr_account
    - dr_service
    - entry_role
    - cr_account_number
    - cr_main_entry_rk
    - cr_main_entry
    - dr_tariff
    - curr_rk
    - curr
    - dr_account_number
    - m_transaction_rk
    - m_transaction_id
    - db_date
    - partition_key
    - order_date
    - dr_main_entry_rk
    - dr_main_entry_id
    - description
    - gl_doc_id
    - cr_main_account_rk
    - cr_main_account
    - gl_transfer_id
    - amount
    - cr_service
    - cr_tariff
    - trans_role

hub_fields:
  - name: dr_main_account_rk
    hub_table: hub_account
    hub_schema: rdv
    is_bk: false
  - name: dr_account_rk
    hub_table: hub_account
    hub_schema: rdv
    is_bk: false
  - name: cr_account_rk
    hub_table: hub_account
    hub_schema: rdv
    is_bk: false
  - name: cr_main_entry_rk
    hub_table: hub_record
    hub_schema: rdv
    is_bk: false
  - name: curr_rk
    hub_table: hub_currency
    hub_schema: rdv
    is_bk: false
  - name: m_transaction_rk
    hub_table: hub_card_transactions
    hub_schema: rdv
    is_bk: false
  - name: dr_main_entry_rk
    hub_table: hub_record
    hub_schema: rdv
    is_bk: false
  - name: cr_main_account_rk
    hub_table: hub_account
    hub_schema: rdv
    is_bk: false
  - name: deal_operation_rk
    hub_table: hub_deal_operation
    hub_schema: rdv
    is_bk: false

ref_fields: []

multi_fields:
  - name: gl_trace_agg_dk
    field_data_quality_checks: []