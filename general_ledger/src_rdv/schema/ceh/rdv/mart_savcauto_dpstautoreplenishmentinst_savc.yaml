schema_version: '1.0'
type: MART
name: mart_savcauto_dpstautoreplenishmentinst_savc
schema: rdv

columns:
  - name: guid_dk
    type: TEXT
    nullable: false
  - name: effective_dttm
    type: TIMESTAMP
    nullable: false
  - name: hash_diff
    type: CHAR
    nullable: false
    params:
      length: 32
  - name: src_cd
    type: TEXT
    nullable: false
  - name: invalid_id
    type: BIGINT
    nullable: false
  - name: version_id
    type: BIGINT
    nullable: false
  - name: deleted_flg
    type: BOOLEAN
    nullable: false
  - name: valid_flg
    type: BOOLEAN
    nullable: false
  - name: auto_payment_rk
    type: BIGINT
  - name: auto_payment_id
    type: TEXT
  - name: id_id
    type: TEXT
  - name: amount_currency
    type: TEXT
  - name: id_system
    type: TEXT
  - name: id_version
    type: BIGINT
  - name: id_updatetime
    type: TIMESTAMP
  - name: id_createtime
    type: TIMESTAMP
  - name: accountowner_id_id
    type: TEXT
  - name: accountowner_id_system
    type: TEXT
  - name: accountowner_id_version
    type: BIGINT
  - name: accountowner_id_updatetime
    type: TIMESTAMP
  - name: accountowner_id_createtime
    type: TIMESTAMP
  - name: amount_value
    type: DECIMAL
  - name: creditaccount_id_id
    type: TEXT
  - name: creditaccount_id_system
    type: TEXT
  - name: creditaccount_id_version
    type: BIGINT
  - name: creditaccount_id_updatetime
    type: TIMESTAMP
  - name: creditaccount_id_createtime
    type: TIMESTAMP
  - name: debitaccount_id_id
    type: TEXT
  - name: debitaccount_id_system
    type: TEXT
  - name: debitaccount_id_version
    type: BIGINT
  - name: debitaccount_id_updatetime
    type: TEXT
  - name: debitaccount_id_createtime
    type: TEXT
  - name: paymentpercentvalue
    type: BIGINT
  - name: periodicity_dictionary_id
    type: TEXT
  - name: periodicity_dictionary_system
    type: TEXT
  - name: periodicity_key
    type: TEXT
  - name: periodicity_keyname
    type: TEXT
  - name: rounding
    type: BIGINT
  - name: status_dictionary_id
    type: TEXT
  - name: status_dictionary_system
    type: TEXT
  - name: status_key
    type: TEXT
  - name: status_keyname
    type: TEXT
  - name: type_dictionary_id
    type: TEXT
  - name: type_dictionary_system
    type: TEXT
  - name: type_key
    type: TEXT
  - name: type_keyname
    type: TEXT
  - name: purpose
    type: TEXT
  - name: id_subsystem
    type: TEXT
  - name: accountowner_id_subsystem
    type: TEXT
  - name: creditaccount_id_subsystem
    type: TEXT
  - name: debitaccount_id_subsystem
    type: TEXT
  - name: productinstance_id_id
    type: TEXT
  - name: productinstance_id_system
    type: TEXT
  - name: productinstance_id_subsystem
    type: TEXT
  - name: productinstance_id_version
    type: INTEGER
  - name: productinstance_id_updatetime
    type: TIMESTAMP
  - name: productinstance_id_createtime
    type: TIMESTAMP
  - name: channel_ip
    type: TEXT
  - name: channel_mac
    type: TEXT
  - name: channel_type
    type: TEXT

hash_src_fields:
  - debitaccount_id_id
  - id_system
  - id_createtime
  - status_dictionary_system
  - creditaccount_id_createtime
  - accountowner_id_id
  - type_dictionary_id
  - status_key
  - accountowner_id_version
  - creditaccount_id_system
  - creditaccount_id_updatetime
  - accountowner_id_updatetime
  - debitaccount_id_version
  - amount_value
  - periodicity_keyname
  - periodicity_dictionary_id
  - id_version
  - debitaccount_id_createtime
  - amount_currency
  - status_dictionary_id
  - creditaccount_id_version
  - periodicity_dictionary_system
  - debitaccount_id_system
  - periodicity_key
  - type_key
  - type_dictionary_system
  - rounding
  - debitaccount_id_updatetime
  - accountowner_id_system
  - id_id
  - status_keyname
  - id_updatetime
  - type_keyname
  - purpose
  - paymentpercentvalue
  - creditaccount_id_id
  - accountowner_id_createtime
  - id_subsystem
  - accountowner_id_subsystem
  - creditaccount_id_subsystem
  - debitaccount_id_subsystem
  - productinstance_id_id
  - productinstance_id_system
  - productinstance_id_subsystem
  - productinstance_id_version
  - productinstance_id_updatetime
  - productinstance_id_createtime
  - channel_ip
  - channel_mac
  - channel_type

multi_fields:
  - name: guid_dk
    field_data_quality_checks:
      - name: IsNull
  - name: effective_dttm
    field_data_quality_checks:
      - name: IsNull

ref_fields: []

hub_fields:
  - name: auto_payment_rk
    hub_table: hub_auto_payment
    hub_schema: rdv
    is_bk: false