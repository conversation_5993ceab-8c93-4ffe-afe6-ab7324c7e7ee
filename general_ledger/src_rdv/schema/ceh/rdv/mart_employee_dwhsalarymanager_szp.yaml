schema_version: '1.0'
type: MART
name: mart_employee_dwhsalarymanager_szp
schema: rdv
columns:
  # - name: employee_rk
    # type: BIGINT
    # nullable: true 
  - name: managerid
    type: BIGINT
    nullable: false 
  - name: deal_rk
    type: BIGINT
    nullable: false 
  - name: client_rk
    type: BIGINT
    nullable: false 
  - name: unloadid
    type: DECIMAL
    params:
      precision: 38
      scale: 0
    nullable: false
  - name: managertypecode
    type: TEXT
    nullable: false 
  - name: managerlogin
    type: TEXT
    nullable: true 
  - name: managerdomain
    type: TEXT
    nullable: true 
  - name: manageremployeenumber
    type: TEXT
    nullable: true
  - name: hash_diff
    type: CHAR
    params:
        length: 32
    nullable: false
  - name: src_cd
    type: TEXT
    nullable: false
  - name: invalid_id
    type: BIGINT
    nullable: false
  - name: version_id
    type: BIGINT
    nullable: false
  - name: deleted_flg
    type: BOOLEAN
    nullable: false
  - name: valid_flg
    type: BOOLEAN
    nullable: false
  - name: effective_dttm
    type: TIMESTAMP
    nullable: false

hash_src_fields:
  - managerlogin
  - managerdomain
  - manageremployeenumber
  
multi_fields:
  - name: unloadid
    field_data_quality_checks: []
  - name: managerid
    field_data_quality_checks: []
  - name: managertypecode
    field_data_quality_checks: []


ref_fields: []

hub_fields:
  # - name: employee_rk
    # hub_table: hub_employee
    # hub_schema: rdv
    # is_bk: true
  - name: deal_rk
    hub_table: hub_deal
    hub_schema: rdv
    is_bk: true
  - name: client_rk
    hub_table: hub_client
    hub_schema: rdv
    is_bk: true
history_field: effective_dttm