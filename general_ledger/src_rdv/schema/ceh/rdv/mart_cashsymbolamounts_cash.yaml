schema_version: '1.0'
type: MART
name: mart_cashsymbolamounts_cash
schema: rdv
columns:
  - name: id_id_dk
    type: TEXT
    nullable: false
  - name: effective_dttm
    type: TIMESTAMP
    nullable: false
  - name: deleted_flg
    type: BOOLEAN
    nullable: false
  - name: src_cd
    type: TEXT
    nullable: false
  - name: hash_diff
    type: CHAR
    nullable: false
    params:
      length: 32
  - name: invalid_id
    type: BIGINT
    nullable: false
  - name: version_id
    type: BIGINT
    nullable: false
  - name: valid_flg
    type: BOOLEAN
    nullable: false
  - name: changeid_dk
    type: TEXT
    nullable: false
  - name: changetype
    type: TEXT
    nullable: true
  - name: changetimestamp
    type: TIMESTAMP
    nullable: true
  - name: id_updatetime
    type: TIMESTAMP
    nullable: true
  - name: hdp_processed_dttm
    type: TIMESTAMP
    nullable: true
  - name: cashsymbolamounts_hash
    type: TEXT
    nullable: true
  - name: cashsubsymbolamounts_hash
    type: TEXT
    nullable: true
  - name: cashsymbolamounts_amount_currency
    type: TEXT
    nullable: true
  - name: cashsymbolamounts_amount_value
    type: TEXT
    nullable: true
  - name: cashsymbolamounts_cashsymbol_dictionary_id
    type: TEXT
    nullable: true
  - name: cashsymbolamounts_cashsymbol_dictionary_system
    type: TEXT
    nullable: true
  - name: cashsymbolamounts_cashsymbol_key_dk
    type: BIGINT
    nullable: false
  - name: cashsymbolamounts_cashsymbol_keyname
    type: TEXT
    nullable: true
  - name: id_version
    type: TEXT
    nullable: true
hash_src_fields:
  - changetype
  - hdp_processed_dttm
  - cashsymbolamounts_hash
  - cashsubsymbolamounts_hash
  - cashsymbolamounts_amount_currency
  - cashsymbolamounts_amount_value
  - cashsymbolamounts_cashsymbol_dictionary_id
  - cashsymbolamounts_cashsymbol_dictionary_system
  - cashsymbolamounts_cashsymbol_keyname
  - id_version
multi_fields:
  - name: id_id_dk
    field_data_quality_checks:
      - name: IsEmpty
  - name: cashsymbolamounts_cashsymbol_key_dk
    field_data_quality_checks:
      - name: IsEmpty
  - name: changeid_dk
    field_data_quality_checks: []
ref_fields: []
history_field: effective_dttm
hub_fields: []
