schema_version: '1.0'
name: hsat_address_obj_rtll
schema: rdv
columns:
  - name: address_obj_rk
    type: BIGINT
    nullable: false
  - name: hash_diff
    type: CHAR
    nullable: false
    params:
      length: 32
  - name: src_cd
    type: TEXT
    nullable: false
  - name: version_id
    type: BIGINT
    nullable: false
  - name: deleted_flg
    type: BOOLEAN
    nullable: false
  - name: valid_flg
    type: BOOLEAN
    nullable: false
  - name: effective_date
    type: DATE
    nullable: false
  - name: c_aoid
    type: TEXT
  - name: c_aoguid
    type: TEXT
  - name: c_name
    type: TEXT
  - name: c_off_name
    type: TEXT
  - name: c_fias
    type: TEXT
  - name: people_place_cd
    type: TEXT
  - name: c_kladr
    type: TEXT
  - name: c_zip
    type: TEXT
  - name: c_archaic
    type: TEXT
  - name: c_status
    type: BIGINT
  - name: c_okato_cd
    type: TEXT
  - name: c_oktmo_cd
    type: TEXT
  - name: c_objectid
    type: BIGINT
  - name: c_changeid
    type: BIGINT
  - name: class_id
    type: TEXT
  - name: sn
    type: BIGINT
  - name: su
    type: BIGINT
rk_field: address_obj_rk
hash_src_fields:
  - c_aoid
  - c_aoguid
  - c_name
  - c_off_name
  - c_fias
  - people_place_cd
  - c_kladr
  - c_zip
  - c_archaic
  - c_status
  - c_okato_cd
  - c_oktmo_cd
  - c_objectid
  - c_changeid
  - class_id
  - sn
  - su
type: HUB_SATELLITE
history_field: effective_date
parent_table: hub_address_obj
parent_schema: rdv
