schema_version: '1.0'
name: hsat_department_branch_rtls
schema: rdv
columns:
  - name: department_rk
    type: BIGINT
    nullable: false
  - name: hash_diff
    type: CHAR
    nullable: false
    params:
      length: 32
  - name: src_cd
    type: TEXT
    nullable: false
  - name: version_id
    type: BIGINT
    nullable: false
  - name: deleted_flg
    type: BOOLEAN
    nullable: false
  - name: valid_flg
    type: BOOLEAN
    nullable: false
  - name: effective_date
    type: DATE
    nullable: false
  - name: c_shortlabel
    type: TEXT
  - name: c_date_begin
    type: DATE
  - name: c_date_end
    type: DATE
  - name: c_difference
    type: BIGINT
  - name: c_internal
    type: TEXT
  - name: c_local_code
    type: TEXT
  - name: c_ready_for_finish
    type: TEXT
  - name: c_depo
    type: TEXT
  - name: c_int_r
    type: TEXT
  - name: c_internal_d
    type: TEXT
  - name: c_bank_req
    type: BIGINT
  - name: c_holidays
    type: TEXT
  - name: c_ch_day_begin_pkg
    type: BIGINT
#  - name: c_min_name
#    type: TEXT
  - name: c_eod_time
    type: DATE
  - name: c_cmfr
    type: TEXT
rk_field: department_rk
hash_src_fields:
  - c_shortlabel
  - c_date_begin
  - c_date_end
  - c_difference
  - c_internal
  - c_local_code
  - c_ready_for_finish
  - c_depo
  - c_int_r
  - c_internal_d
  - c_bank_req
  - c_holidays
  - c_ch_day_begin_pkg
#  - c_min_name
  - c_eod_time
  - c_cmfr
type: HUB_SATELLITE
parent_table: hub_department
parent_schema: rdv
history_field: effective_date