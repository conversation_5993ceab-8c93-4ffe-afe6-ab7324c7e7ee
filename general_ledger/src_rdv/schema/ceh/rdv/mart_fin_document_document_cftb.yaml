schema_version: '1.0'
type: MART
name: mart_fin_document_document_cftb
schema: rdv
ref_fields: []
columns:
- name: fin_document_rk
  type: BIGINT
  nullable: false
- name: effective_date
  type: DATE
  nullable: false
- name: src_cd
  type: TEXT
  nullable: false
- name: version_id
  type: BIGINT
  nullable: false
- name: valid_flg
  type: BOOLEAN
  nullable: false
- name: deleted_flg
  type: BOOLEAN
  nullable: false
- name: invalid_id
  type: BIGINT
  nullable: false
- name: hash_diff
  type: CHAR
  params:
    length: 32
  nullable: false
- name: c_array_dog_acc
  type: BIGINT
  nullable: true
- name: c_array_sum_dog
  type: BIGINT
  nullable: true
- name: c_astr_date_prov
  type: TIMESTAMP
  nullable: true
- name: c_category_info
  type: BIGINT
  nullable: true
- name: c_comment
  type: TEXT
  nullable: true
- name: correction_fin_document_rk
  type: BIGINT
  nullable: true
- name: c_correction_doc
  type: BIGINT
  nullable: true
- name: c_date_exec
  type: TIMESTAMP
  nullable: true
- name: department_rk
  type: BIGINT
  nullable: true
- name: c_depart
  type: BIGINT
  nullable: true
- name: c_document_date
  type: TIMESTAMP
  nullable: true
- name: c_document_num
  type: BIGINT
  nullable: true
- name: c_document_uno
  type: TEXT
  nullable: true
- name: employee_rk
  type: BIGINT
  nullable: true
- name: c_document_user
  type: BIGINT
  nullable: true
- name: c_dt_send
  type: BIGINT
  nullable: true
- name: c_edition_uno
  type: TEXT
  nullable: true
- name: filial_department_rk
  type: BIGINT
  nullable: true
- name: c_filial
  type: BIGINT
  nullable: true
- name: c_history_state
  type: BIGINT
  nullable: true
- name: folder_pay_fin_document_rk
  type: BIGINT
  nullable: true
- name: c_in_folder
  type: BIGINT
  nullable: true
- name: c_kt_send
  type: BIGINT
  nullable: true
- name: c_pachka
  type: TEXT
  nullable: true
- name: c_printed
  type: TEXT
  nullable: true
- name: storno_fin_document_rk
  type: BIGINT
  nullable: true
- name: c_storno_doc
  type: BIGINT
  nullable: true
- name: c_subdocuments
  type: BIGINT
  nullable: true
- name: c_text_vozv
  type: TEXT
  nullable: true
- name: user_exec_employee_rk
  type: BIGINT
  nullable: true
- name: c_user_exec
  type: BIGINT
  nullable: true
- name: c_user_type
  type: BIGINT
  nullable: true
- name: class_id
  type: TEXT
  nullable: true
- name: collection_id
  type: BIGINT
  nullable: true
- name: fin_document_id
  type: BIGINT
  nullable: true
- name: sn
  type: BIGINT
  nullable: true
- name: state_id
  type: TEXT
  nullable: true
- name: su
  type: BIGINT
  nullable: true
- name: time
  type: TIMESTAMP
  nullable: true
hash_src_fields:
- c_array_dog_acc
- c_array_sum_dog
- c_astr_date_prov
- c_category_info
- c_comment
- c_correction_doc
- c_date_exec
- c_depart
- c_document_date
- c_document_num
- c_document_uno
- c_document_user
- c_dt_send
- c_edition_uno
- c_filial
- c_history_state
- c_in_folder
- c_kt_send
- c_pachka
- c_printed
- c_storno_doc
- c_subdocuments
- c_text_vozv
- c_user_exec
- c_user_type
- class_id
- collection_id
- fin_document_id
- sn
- state_id
- su
- time
hub_fields:
- name: fin_document_rk
  hub_table: hub_fin_document
  hub_schema: rdv
  is_bk: true
- name: correction_fin_document_rk
  hub_table: hub_fin_document
  hub_schema: rdv
  is_bk: false
- name: department_rk
  hub_table: hub_department
  hub_schema: rdv
  is_bk: false
- name: employee_rk
  hub_table: hub_employee
  hub_schema: rdv
  is_bk: false
- name: filial_department_rk
  hub_table: hub_department
  hub_schema: rdv
  is_bk: false
- name: folder_pay_fin_document_rk
  hub_table: hub_fin_document
  hub_schema: rdv
  is_bk: false
- name: storno_fin_document_rk
  hub_table: hub_fin_document
  hub_schema: rdv
  is_bk: false
- name: user_exec_employee_rk
  hub_table: hub_employee
  hub_schema: rdv
  is_bk: false
history_field: effective_date