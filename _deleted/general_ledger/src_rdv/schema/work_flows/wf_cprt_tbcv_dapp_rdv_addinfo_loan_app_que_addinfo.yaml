schema_version: '1.13'
name: wf_cprt_tbcv_dapp_rdv_addinfo_loan_app_que_addinfo
type: WORK_FLOW
version: 1
tags:
  - ZI19
  - wf_cprt_tbcv_dapp_rdv_addinfo_loan_app_que_addinfo
  - 1.TBCV.ZI19.50
orientation: TB
local_metrics:
  wf_dataset_max_date_to:
    query: MAX(DATE_TRUNC('second', hdp_processed_dttm))
    on_null: .conf.algos."1.TBCV.ZI19.50".by_src."dapp.prod_repl_subo_tbcvcccp.tbcv_ret_cust_loan_app_questionnaire_addinfo".wf_dataset_max_date_to
    target: stage_T_input
targets:
  - short_name: mart_addinfo
    resource_cd: ceh.rdv.mart_addinfo_loan_app_que_addinfo_tbcv
    table: mart_addinfo_loan_app_que_addinfo_tbcv
    schema: rdv
sources:
  - short_name: addinfo
    type: DB_TABLE
    object: tbcv_ret_cust_loan_app_questionnaire_addinfo
    resource_cd: dapp.prod_repl_subo_tbcvcccp.tbcv_ret_cust_loan_app_questionnaire_addinfo
mappings:
  marts:
    - target: mart_addinfo
      source: addinfo
      short_name: mart_addinfo_tbcv
      algorithm_uid: 1.TBCV.ZI19.50
      algorithm_uid_2: '1'
      delta_mode: new
      where_clause:
        engine: jq
        template: hdp_processed_dttm >= '{from_dttm}' and hdp_processed_dttm  < '{to_dttm}'
        vars:
          from_dttm: .conf.algos."1.TBCV.ZI19.50".by_src."dapp.prod_repl_subo_tbcvcccp.tbcv_ret_cust_loan_app_questionnaire_addinfo".wf_dataset_max_date_to
          to_dttm: .conf.algos."1.TBCV.ZI19.50".by_src."dapp.prod_repl_subo_tbcvcccp.tbcv_ret_cust_loan_app_questionnaire_addinfo".dapp_actual_dttm
      field_map:
        addinfo_dk:
          type: column
          value: retcustanapp_que_adin_hash
        retcustanapp_que_adin_hash:
          type: column
          value: retcustanapp_que_adin_hash
        changeid:
          type: column
          value: changeid
        changetype:
          type: column
          value: changetype
        changetimestamp:
          type: column
          value: changetimestamp
        retcustanapp_que_adin_militaryservicestatus:
          type: column
          value: retcustanapp_que_adin_militaryservicestatus
        retcustanapp_que_adin_militaryexemptedcomment:
          type: column
          value: retcustanapp_que_adin_militaryexemptedcomment
        retcustanapp_que_adin_hasfirearmslicense:
          type: column
          value: retcustanapp_que_adin_hasfirearmslicense
        retcustanapp_que_adin_hasoverduedebts:
          type: column
          value: retcustanapp_que_adin_hasoverduedebts
        retcustanapp_que_adin_debtscomment:
          type: column
          value: retcustanapp_que_adin_debtscomment
        retcustanapp_que_adin_maritalstatuscomment:
          type: column
          value: retcustanapp_que_adin_maritalstatuscomment
        retcustanapp_que_adin_subjectcredithistorycode:
          type: column
          value: retcustanapp_que_adin_subjectcredithistorycode
        retcustanapp_que_adin_hasunfulfilledjudgement:
          type: column
          value: retcustanapp_que_adin_hasunfulfilledjudgement
        retcustanapp_que_adin_unfulfilledjudgementcomment:
          type: column
          value: retcustanapp_que_adin_unfulfilledjudgementcomment
        retcustanapp_que_adin_hascurrentjudicialprocess:
          type: column
          value: retcustanapp_que_adin_hascurrentjudicialprocess
        retcustanapp_que_adin_judicialprocesscomment:
          type: column
          value: retcustanapp_que_adin_judicialprocesscomment
        retcustanapp_que_adin_hasjudicialpunishment:
          type: column
          value: retcustanapp_que_adin_hasjudicialpunishment
        retcustanapp_que_adin_judicialpunishmentcomment:
          type: column
          value: retcustanapp_que_adin_judicialpunishmentcomment
        retcustanapp_que_adin_hasdisabilityfirstsecondgroup:
          type: column
          value: retcustanapp_que_adin_hasdisabilityfirstsecondgroup
        retcustanapp_que_adin_disabilitygroupcomment:
          type: column
          value: retcustanapp_que_adin_disabilitygroupcomment
        retcustanapp_que_adin_monthlystatementbypersonalmanager:
          type: column
          value: retcustanapp_que_adin_monthlystatementbypersonalmanager
        retcustanapp_que_adin_monthlystatementbyvtbonline:
          type: column
          value: retcustanapp_que_adin_monthlystatementbyvtbonline
        retcustanapp_que_adin_othercustomerinfo:
          type: column
          value: retcustanapp_que_adin_othercustomerinfo
        hdp_processed_dttm:
          type: column
          value: hdp_processed_dttm
        effective_dttm:
          type: column
          value: changetimestamp
        deleted_flg:
          type: sql_expression
          value: changetype = '0'
          field_type: BOOLEAN
      hub_map: []
      metrics:
        by_src:
          - save_as: wf_dataset_max_date_to
            metric: wf_dataset_max_date_to
      synthetic_history:
        syn_history_function: rdv.syn_bussiness_date_trunc
        func_param:
          - param_source: field
            param_value: changetimestamp
          - param_source: const
            param_value: day
      ref_map: []
  references: []
  hub_satellites: []
  link_satellites: []
