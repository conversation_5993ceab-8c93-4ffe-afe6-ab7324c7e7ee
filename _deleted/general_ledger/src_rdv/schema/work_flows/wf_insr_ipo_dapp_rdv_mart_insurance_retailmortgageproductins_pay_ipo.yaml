name: wf_insr_ipo_dapp_rdv_mart_insurance_retailmortgageproductins_pay_ipo
type: WORK_FLOW
schema_version: '1.14'
version: 1
tags:
  - 'team: zi1'
  - wf
  - 'area: insr'
  - 'src: IPO'
  - 'prv: dapp'
  - 'tgt: rdv'
  - 'cf: cf_insr_ipo_dapp_rdv_mart_insurance_retailmortgageproductins_pay_ipo'
  - 'rls: 81'
  - 'uid: 15.IPO.ZI1.2'
orientation: TB
sources:
  - short_name: ipo_retailmortgagep
    type: DB_TABLE
    object: ipo_retailmortgageproductinsurance_paymentschedule
    resource_cd: dapp.prod_repl_subo_mtgpp.ipo_retailmortgageproductinsurance_paymentschedule
targets:
  - short_name: mart_insurance_reta_1
    resource_cd: ceh.rdv.mart_insurance_retailmortgageproductins_pay_ipo
    table: mart_insurance_retailmortgageproductins_pay_ipo
    schema: rdv
local_metrics:
  wf_dataset_max_date_to:
    target: stage_T_input
    query: MAX(DATE_TRUNC('second', hdp_processed_dttm))
    on_null: .conf.algos[.this_algo_uid].by_src[.this_source_cd].wf_dataset_max_date_to
mappings:
  marts:
    - short_name: mart_insurance_reta_1
      algorithm_uid: 15.IPO.ZI1.2
      algorithm_uid_2: '1'
      target: mart_insurance_reta_1
      source: ipo_retailmortgagep
      delta_mode: new
      where_clause:
        engine: jq
        template: hdp_processed_dttm >= '{from_dttm}' and hdp_processed_dttm  < '{to_dttm}'
        vars:
          from_dttm: .conf.algos[.this_algo_uid].by_src[.this_source_cd].wf_dataset_max_date_to
          to_dttm: .conf.algos[.this_algo_uid].by_src[.this_source_cd].dataset_max_date_to
      metrics:
        by_src:
          - save_as: wf_dataset_max_date_to
            metric: wf_dataset_max_date_to
      field_map:
        paymentschedule_hash_dk:
          type: column
          value: paymentschedule_hash
        paymentschedule_guid_dk:
          type: column
          value: paymentschedule_guid
        deleted_flg:
          type: sql_expression
          value: changetype = 'DELETE'
          field_type: BOOLEAN
        changeid:
          type: column
          value: changeid
        changetype:
          type: column
          value: changetype
        changetimestamp:
          type: sql_expression
          value: etl.try_cast2ts(changetimestamp, null, 'YYYYMMDDHH24:MI:SS')
          field_type: TIMESTAMP
        hdp_processed_dttm:
          type: column
          value: hdp_processed_dttm
        paymentschedule_date:
          type: column
          value: paymentschedule_date
        paymentschedule_paidperiodinyears:
          type: column
          value: paymentschedule_paidperiodinyears
        paymentschedule_insurancepremium:
          type: column
          value: paymentschedule_insurancepremium
      hub_map: []
      ref_map: []
