schema_version: 2.0
metadata:
  author: <PERSON><PERSON><PERSON> (70189790)
  version: "1.0"
  description: Управляющий поток
  tags:
    - 'wf: wf_cprt_csoc_drp_rdv_mart_client_organisation_documents_csoc'
    - 'team: zi25'
    - 'area: Вовлеченная сторона'
    - 'src: csoc'
    - 'prv: drp'
    - 'tgt: rdv'
    - 'uid: 1.CSOC.ZI25.1'
    - 'src-tbl: csoc_organisation'
    - 'tgt-tbl: ceh.rdv.mart_client_organisation_csoc'
    - 'cf: cf_cprt_csoc_drp_rdv_mart_client_organisation_documents_csoc'
  group: general_ledger 
  imports:
    - eod_template.cf_uni_template
  main_flows:
    - cf_cprt_csoc_drp_rdv_mart_client_organisation_documents_csoc
flows:
  - id: cf_cprt_csoc_drp_rdv_mart_client_organisation_documents_csoc
    description: упр<PERSON><PERSON><PERSON><PERSON>ющий поток загрузки
    builder: ceh_core_idl.app.builders.simple_flow_builder
    tasks:
      - id: run_wf_cprt_csoc_drp_rdv_mart_client_organisation_csoc
        builder: ceh_core_idl.app.builders.include_flow_builder   # 2 ???
        properties:
          ref: cf_uni_template
          properties:
            mode: ANY  # возможные значения: ANY, ALL
            actual_dttm_prefix: 'CSOC'   # (src) код системы источника. Для каждой системы источника своя метрика (wain/csrf/..)_actual_dttm
            work_flow_id: wf_cprt_csoc_drp_rdv_mart_client_organisation_documents_csoc
            algos_map:
              1.CSOC.ZI25.4:
                - ceh: ceh.rdv.mart_client_organisation_documents_csoc
                  uni: dapp.prod_repl_subo_csoc.csoc_organisation_documents