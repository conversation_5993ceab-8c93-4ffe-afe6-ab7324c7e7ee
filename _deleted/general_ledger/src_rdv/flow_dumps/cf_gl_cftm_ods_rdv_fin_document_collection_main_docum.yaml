schema_version: 2.0 # номер версии используем 2.0
metadata:
  author: achurkin
  version: "1.0"
  description: Управляющий поток
  tags:
    - "cf: cf_ods"
    - "rdv"
    - "ods"
    - "2.ZI2.9"
  imports:
    - rdv_cf_uni_template.cf_uni_template
  group: general_ledger
  main_flows: 
    - cf_gl_cftm_ods_rdv_fin_document_collection_main_docum
flows:
  - id: cf_gl_cftm_ods_rdv_fin_document_collection_main_docum # название потока, уникальное
    description: управляющий поток cf_gl_cftm_ods_rdv_fin_document_collection_main_docum
    builder: ceh_core_idl.app.builders.simple_flow_builder # ссылка на конструктор потока
    metadata: # параметры потока (Trigger Dag w/config)
    tasks:
      - id: run_wf_gl_cftm_ods_rdv_fin_document_collection_main_docum
        builder: ceh_core_idl.app.builders.include_flow_builder
        properties:
          ref: cf_uni_template
          properties:
            mode: ANY  # возможные значения: ANY, ALL
            actual_dttm_prefix: 'cftm'
            work_flow_id: wf_gl_cftm_ods_rdv_fin_document_collection_main_docum
            algos_map:
              2.ZI2.9:
                - ceh: ceh.rdv.lsat_fin_document_collection_cftm
                  uni: ods.CFT2MAIN_SOH.H2_Z#MAIN_DOCUM
