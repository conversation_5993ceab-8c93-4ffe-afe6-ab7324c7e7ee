schema_version: 1
start_date: &start_date "2020-12-21 00:00:00+03:00"
name: wf_ods_rdv_ref_incom_confirmation
version: 18

default_args:
  owner: pdv2
  depends_on_past: false
  start_date: *start_date

tags:
  - wf
  - ods
  - rdv
  - dict
  - rkk
  - incom_confirmation

catchup: false
orientation: TB
ods_versions_key: ODS_VERSIONS
transaction_id_key: TRANSACTION_ID
version_id_key: VERSION_ID

lock_resources:
  attempt_timeout: 12
  attempts: 150

external_tables:

  - name: e_h2_dict_element_incom_confirmation
    table_name: e_h2_dict_element_incom_confirmation
    ods_delta_mode: new
    resource_cd: ods.RKK2_SOH.H2_DICT_ELEMENT

references:
  - name: ref_incom_confirmation
    table_name: ref_incom_confirmation
    rdv_resource: ceh.rdv_dict.ref_incom_confirmation

links: []
satellites: []
hubs: []