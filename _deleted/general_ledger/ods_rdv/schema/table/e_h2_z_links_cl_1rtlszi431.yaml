schema_version: 2
table_name: e_h2_z_links_cl_1rtlszi431
explicit_columns: true
columns:
  - name: ods$effective_from_dt
    type:
      type_class: TIMESTAMP
  - name: ods$effective_to_dt
    type:
      type_class: TIMESTAMP
  - name: ods$effective_from_csn
    type:
      type_class: BIGINT
  - name: ods$effective_to_csn
    type:
      type_class: BIGINT
  - name: ods$is_active_flg
    type:
      type_class: VARCHAR
      length: 1
  - name: ods$deleted_flg
    type:
      type_class: VARCHAR
      length: 1
  - name: ods$create_id
    type:
      type_class: DECIMAL
      precision: 38
      scale: 0
  - name: ods$update_id
    type:
      type_class: DECIMAL
      precision: 38
      scale: 0
  - name: ods$processed_dt
    type:
      type_class: TIMESTAMP
  - name: id
    type:
      type_class: DECIMAL
    nullable: false
  - name: c_vid_link
    type:
      type_class: DECIMAL
    nullable: false
  - name: c_handle
    type:
      type_class: VARCHAR
    nullable: false
  - name: c_found
    type:
      type_class: VARCHAR
    nullable: false
  - name: c_client_name
    type:
      type_class: VARCHAR
    nullable: false
  - name: c_date_beg
    type:
      type_class: TIMESTA<PERSON>
    nullable: false
  - name: c_date_end
    type:
      type_class: TIMESTAMP
    nullable: false
  - name: c_base_found
    type:
      type_class: DECIMAL
    nullable: false
  - name: sn
    type:
      type_class: DECIMAL
    nullable: false
  - name: su
    type:
      type_class: DECIMAL
    nullable: false
  - name: effective_dt_syn
    type:
      type_class: DATE
additional_columns: []
datastore:
  source_system: RTLS
  data_capture_mode: increment
  key_fields:
    - id
    - effective_dt_syn
  column_groups:
    data: []
  business_keys:
    - business_key: client_relation_num_id
      field_map:
        id: client_relation_id
      satellites:
        - satellite: client_relation_links_cl_rtls
          field_map:
            effective_dt_syn: effective_date
            c_vid_link: c_vid_link_cd
            c_handle: c_handle
            c_found: c_found
            c_client_name: c_client_name
            c_date_beg: c_date_beg
            c_date_end: c_date_end
            c_base_found: c_base_found_cd
            sn: sn
            su: su
  data_quality_checks: []
  links: []
  references: []
  synthetic_history:
    syn_history_function: syn_bussiness_date_trunc
    func_param:
    - param_source: field
      param_value: ods$effective_from_dt
    - param_source: const
      param_value: day
    destination_field: effective_dt_syn
  data_source_type: ODS_SCD2
creation:
  ora_table: h2_z#links_cl
  ora_schema: $ODS_CFT2RS