table_name: hub_application_status

explicit_columns: true

columns:

- name: application_status_rk
  nullable: false
  type:
    type_class: BIGINT

- name: application_status_id
  nullable: false
  type:
    type_class: VARCHAR
    length: 255

- name: src_cd
  nullable: false
  type:
    type_class: VARCHAR
    length: 32

- name: bk_type
  nullable: false
  type:
    type_class: VARCHAR
    length: 32

- name: invalid_id
  nullable: false
  type:
    type_class: BIGINT

- name: version_id
  nullable: false
  type:
    type_class: BIGINT

datastore:

  hub: application_status

  key_fields:
  - application_status_rk

  column_groups:
    data: []
    bk:
    - application_status_id
    rk:
    - application_status_rk
    mgmt:
    - src_cd
    - bk_type
    - invalid_id
    - version_id

