columns:
- name: assets_collection_rk
  nullable: false
  type:
    type_class: BIGINT
- name: assets_rk
  nullable: false
  type:
    type_class: BIGINT
- name: collection_rk
  nullable: false
  type:
    type_class: BIGINT
- name: src_cd
  nullable: false
  type:
    length: 32
    type_class: VARCHAR
- name: version_id
  nullable: false
  type:
    type_class: BIGINT
- name: deleted_flg
  nullable: false
  type:
    type_class: BOOLEAN
- name: collection_role_cd
  nullable: false
  type:
    type_class: VARCHAR
datastore:
  column_groups:
    data: []
    mgmt:
    - src_cd
    - version_id
    - deleted_flg
    rk:
    - assets_collection_rk
    - assets_rk
    - collection_rk
    - collection_role_cd
  delta_mode: new
  key_fields:
  - assets_collection_rk
  - version_id
  link: assets_collection
explicit_columns: true
table_name: lnk_assets_collection
