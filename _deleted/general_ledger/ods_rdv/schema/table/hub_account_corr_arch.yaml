columns:
- name: account_corr_arch_rk
  nullable: false
  type:
    type_class: BIGINT

- name: account_corr_arch_id
  nullable: false
  type:
    type_class: VARCHAR
    length: 255

- name: src_cd
  nullable: false
  type:
    type_class: VARCHAR
    length: 32

- name: bk_type
  nullable: false
  type:
    type_class: VARCHAR
    length: 32

- name: invalid_id
  nullable: false
  type:
    type_class: BIGINT

- name: version_id
  nullable: false
  type:
    type_class: BIGINT

datastore:
  column_groups: 
    data: []
    bk:
    - account_corr_arch_id
    rk:
    - account_corr_arch_rk
    mgmt:
    - src_cd
    - bk_type
    - invalid_id
    - version_id
  hub: account_corr_arch
  key_fields:
  - account_corr_arch_rk    
explicit_columns: true
table_name: hub_account_corr_arch