schema_version: 2
table_name: h2_z_doc_kinds_2zi22351
explicit_columns: True

columns:

- name: ods$effective_from_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_to_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_from_csn
  type:
    type_class: BIGINT

- name: ods$effective_to_csn
  type:
    type_class: BIGINT

- name: ods$is_active_flg
  type:
    type_class: VARCHAR
    length: 1

- name: ods$deleted_flg
  type:
    type_class: VARCHAR
    length: 1

- name: ods$create_id
  type:
    type_class: DECIMAL
    precision: 38
    scale: 0

- name: ods$update_id
  type:
    type_class: DECIMAL
    precision: 38
    scale: 0

- name: ods$processed_dt
  type:
    type_class: TIMESTAMP

- name: id
  type:
    type_class: VARCHAR
    length: 128

- name: sn
  type:
    type_class: BIGINT 

- name: su
  type:
    type_class: BIGINT 

- name: c_name
  type:
    type_class: VARCHAR
    length: 250
    
- name: effective_date
  type:
    type_class: DATE


datastore:
  source_system: CFTM
  data_capture_mode: increment
  data_source_type: ODS_SCD2
  synthetic_history:
    syn_history_function: syn_bussiness_date_trunc
    func_param:
    - param_source: field
      param_value: ods$effective_from_dt
    - param_source: const
      param_value: day
    destination_field: effective_date  
  key_fields:
  - id
  - effective_date
  column_groups:
    data: []
  business_keys: 
  - business_key: doc_kind
    field_map: 
      id: document_kind_id
  data_quality_checks: []
  links: []
  references:
  - reference: ref_document_kind_cftm
    field_map: 
      id: document_kind_id
      ods$effective_from_dt: effective_date
      c_name: c_name

creation:
  ora_table: H2_Z#DOC_KINDS
  ora_schema: $ODS_SINT
  parallelism:
    ora_parallel_amount: 4
    ora_part_field_name: ODS$EFFECTIVE_TO_CSN
    ora_part_field_type: int