schema_version: 2
table_name: e_map_department_uniq_1zi4125
explicit_columns: true
columns:
- name: ods$effective_from_dt
  type:
    type_class: TIMESTAMP
- name: ods$effective_to_dt
  type:
    type_class: TIMESTAMP
- name: ods$effective_from_csn
  type:
    type_class: BIGINT
- name: ods$effective_to_csn
  type:
    type_class: BIGINT
- name: ods$is_active_flg
  type:
    type_class: VARCHAR
    length: 1
- name: ods$deleted_flg
  type:
    type_class: VARCHAR
    length: 1
- name: ods$create_id
  type:
    type_class: DECIMAL
    precision: 38
    scale: 0
- name: ods$update_id
  type:
    type_class: DECIMAL
    precision: 38
    scale: 0
- name: ods$processed_dt
  type:
    type_class: TIMESTAMP
- name: department_rk
  type:
    type_class: VARCHAR
    length: 255
- name: department_uniq_rk
  type:
    type_class: VARCHAR
    length: 255
- name: effective_from_dt
  type:
    type_class: DATE
- name: effective_to_dt
  type:
    type_class: DATE
- name: src_uniq_cd
  type:
    type_class: VARCHAR
- name: effective_dt_syn
  type:
    type_class: DATE
additional_columns:    
# Computed field for CFTM BK Schema
- name: bk_schema_cftm
  type:
    type_class: TEXT
  computed: '''CFTM''::text'
  
datastore:
  source_system: ACPD
  data_capture_mode: increment
  data_source_type: ODS_SCD2
  synthetic_history:
    syn_history_function: syn_bussiness_date_trunc
    func_param:
    - param_source: field
      param_value: effective_from_dt
    - param_source: const
      param_value: day
    destination_field: effective_dt_syn
  key_fields:
  - department_rk
  - department_uniq_rk
  - effective_dt_syn
  column_groups:
    data: []
  data_quality_checks: []
  business_keys:
  - business_key: department_num_id
    alias: department_rk_alias
    required: false
    field_map:
      department_rk: department_id
# Additional BK type      
    src_cd_field: bk_schema_cftm
  - business_key: department_num_id
    alias: department_uniq_rk_alias
    required: false
    field_map:
      department_uniq_rk: department_id
  links:
  - link: department_uniq
    satellites:
    - satellite: department_uniq_acpd
      field_map:
        effective_dt_syn: effective_from_date
        effective_to_dt: effective_to_date
        src_uniq_cd: src_uniq_cd
    rk_bk_map:
      primary_rk:
        bk: department_num_id
        alias: department_uniq_rk_alias
      secondary_rk:
        bk: department_num_id
        alias: department_rk_alias
  references: []
creation:
  ora_table: MAP_DEPARTMENT_UNIQ
  ora_schema: $ODS_UNIQ
