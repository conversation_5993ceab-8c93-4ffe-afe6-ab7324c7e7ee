schema_version: 2
table_name: h2_z_aux_ctrl_type
explicit_columns: true

columns:

- name: ods$effective_from_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_to_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_from_csn
  type:
    type_class: BIGINT

- name: ods$effective_to_csn
  type:
    type_class: BIGINT

- name: ods$is_active_flg
  type:
    type_class: VARCHAR
    length: 1

- name: ods$deleted_flg
  type:
    type_class: VARCHAR
    length: 1
    
- name: ods$create_id
  type:
    type_class: DECIMAL
    precision: 38
    scale: 0

- name: ods$update_id
  type:
    type_class: DECIMAL
    precision: 38
    scale: 0

- name: ods$processed_dt
  type:
    type_class: TIMESTAMP

- name: id
  type:
    type_class: BIGINT

- name: sn
  type:
    type_class: BIGINT

- name: su
  type:
    type_class: BIGINT

- name: c_code
  type:
    type_class: VARCHAR
    length: 32

- name: c_name
  type:
    type_class: VARCHAR
    length: 100

- name: c_accept_count
  type:
    type_class: BIGINT

- name: c_accept_method
  type:
    type_class: VARCHAR
    length: 128

- name: c_reject_message
  type:
    type_class: VARCHAR
    length: 2000

- name: c_diff_controllers
  type:
    type_class: VARCHAR
    length: 1

- name: c_access_param
  type:
    type_class: BIGINT

- name: c_object_class
  type:
    type_class: VARCHAR
    length: 128

- name: c_priority
  type:
    type_class: BIGINT

- name: c_conditions
  type:
    type_class: BIGINT

- name: c_vtb_set_ap
  type:
    type_class: VARCHAR
    length: 1



datastore:
  source_system: CFTM
  data_capture_mode: increment
  data_source_type: ODS_SCD2
  key_fields:
  - id
  column_groups:
    data: []
  business_keys: 
  - business_key: aux_ctrl_type_num_id
    field_map: 
      id: aux_ctrl_type_id 
   
  data_quality_checks: []
  links: []
  references: []

creation:
  ora_table: H2_Z#AUX_CTRL_TYPE
  ora_schema: $ODS_SINT
  parallelism:
    ora_parallel_amount: 4
    ora_part_field_name: ODS$EFFECTIVE_TO_CSN
    ora_part_field_type: int

