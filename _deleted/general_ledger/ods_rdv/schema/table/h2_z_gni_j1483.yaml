schema_version: 2
table_name: h2_z_gni_j1483
explicit_columns: true

columns:

- name: ods$effective_from_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_to_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_from_csn
  type:
    type_class: BIGINT

- name: ods$effective_to_csn
  type:
    type_class: BIGINT

- name: ods$is_active_flg
  type:
    type_class: VARCHAR
    length: 1

- name: ods$deleted_flg
  type:
    type_class: VARCHAR
    length: 1
    
- name: ods$create_id
  type:
    type_class: DECIMAL
    precision: 22
    scale: 0

- name: ods$update_id
  type:
    type_class: DECIMAL
    precision: 22
    scale: 0

- name: ods$processed_dt
  type:
    type_class: TIMESTAMP

- name: id
  type:
    type_class: BIGINT

- name: sn
  type:
    type_class: BIGINT

- name: su
  type:
    type_class: BIGINT

- name: c_working_datetime
  type:
    type_class: TIMESTAMP

- name: c_filename
  type:
    type_class: VARCHAR
    length: 2048

- name: c_description
  type:
    type_class: VARCHAR
    length: 4000

- name: c_create_user
  type:
    type_class: BIGINT

- name: c_file_num
  type:
    type_class: VARCHAR
    length: 200

- name: c_format_num
  type:
    type_class: VARCHAR
    length: 20

- name: c_gni_blank
  type:
    type_class: BIGINT

- name: c_chang_num
  type:
    type_class: VARCHAR
    length: 36

- name: c_chang_date
  type:
    type_class: TIMESTAMP

- name: c_req_file
  type:
    type_class: BIGINT

- name: c_serv_part_out
  type:
    type_class: VARCHAR
    length: 4000

- name: c_info_part_out
  type:
    type_class: VARCHAR
    length: 4000

- name: c_com_status
  type:
    type_class: BIGINT

- name: c_account
  type:
    type_class: BIGINT

- name: c_send_user
  type:
    type_class: BIGINT

- name: c_send_datetime
  type:
    type_class: TIMESTAMP

- name: c_in_flag
  type:
    type_class: VARCHAR
    length: 4

- name: c_letter_date
  type:
    type_class: TIMESTAMP

- name: c_is_letter_error
  type:
    type_class: VARCHAR
    length: 4

- name: c_letter_user
  type:
    type_class: BIGINT

- name: c_in_file_history
  type:
    type_class: BIGINT

- name: c_send_branch
  type:
    type_class: BIGINT

- name: c_mess_text
  type:
    type_class: TEXT

- name: c_vtb_notify_send
  type:
    type_class: DECIMAL
    precision: 22
    scale: 0    
    
- name: effective_dt_syn
  type:
    type_class: DATE  
  
additional_columns:

- name: role_gni_jour_employee_cd
  type:
    type_class: VARCHAR
  computed: 3
  
datastore:
  source_system: CFTM
  data_capture_mode: increment
  data_source_type: ODS_SCD2
  synthetic_history:
    syn_history_function: syn_bussiness_date_trunc
    func_param:
    - param_source: field
      param_value: ods$effective_from_dt
    - param_source: const
      param_value: day
    destination_field: effective_dt_syn
  key_fields: 
  - id
  - effective_dt_syn
  column_groups:
    data: []
  business_keys:
  - business_key: gni_jour_num_id
    required: false
    field_map:
      id: gni_jour_id
  - business_key: employee_num_id
    required: false
    field_map: 
      c_letter_user: employee_id      
      
  data_quality_checks: []

  links:
    - link: gni_jour_employee
      alias: gni_jour_employee1483
      rk_bk_map:
        gni_jour_rk:
          bk: gni_jour_num_id
        employee_rk:
          bk: employee_num_id
      ref_map:
        role_gni_jour_employee_cd:
          bk: role_gni_jour_employee
          field: role_gni_jour_employee_cd          
      satellites:
      - satellite: gni_jour_employee_cftm
        field_map:
          ods$effective_from_dt: effective_date          

  references: []

creation:
  ora_table: H2_Z#GNI_JOUR
  ora_schema: $ODS_SINT
  parallelism:
    ora_parallel_amount: 4
    ora_part_field_name: ODS$EFFECTIVE_TO_CSN
    ora_part_field_type: int

