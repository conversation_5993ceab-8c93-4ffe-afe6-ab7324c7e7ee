schema_version: 2
table_name: h2_z_cl_priv297
explicit_columns: True

columns:

- name: ods$effective_from_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_to_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_from_csn
  type:
    type_class: BIGINT

- name: ods$effective_to_csn
  type:
    type_class: BIGINT

- name: ods$is_active_flg
  type:
    type_class: VARCHAR
    length: 1 

- name: ods$deleted_flg
  type:
    type_class: VARCHAR
    length: 1

- name: ods$create_id
  type:
    type_class: DECIMAL
    precision: 22
    scale: 0

- name: ods$update_id
  type:
    type_class: DECIMAL
    precision: 22
    scale: 0

- name: ods$processed_dt
  type:
    type_class: TIMESTAMP

- name: id
  type:
    type_class: BIGINT

- name: collection_id
  type:
    type_class: BIGINT

- name: c_value
  type:
    type_class: BIGINT

- name: sn
  type:
    type_class: BIGINT

- name: su
  type:
    type_class: BIGINT    

- name: effective_dt_syn
  type:
    type_class: DATE  
 
additional_columns:

- name: collection_role_cd
  type:
    type_class: VARCHAR
  computed:  2027
 
datastore:
  source_system: CFTM
  data_capture_mode: increment
  data_source_type: ODS_SCD2
  synthetic_history:
    syn_history_function: syn_bussiness_date_trunc
    func_param:
    - param_source: field
      param_value: ods$effective_from_dt
    - param_source: const
      param_value: day
    destination_field: effective_dt_syn
  key_fields: 
  - collection_id
  - c_value  
  - effective_dt_syn
  column_groups:
    data: []
  business_keys:
  - business_key: client_num_id
    required: false
    field_map:
      c_value: client_id
  - business_key: collection_num_id
    required: false
    field_map: 
      collection_id: collection_id      
      
  data_quality_checks: []

  links:
    - link: client_collection
      alias: client_collection297
      rk_bk_map:
        client_rk:
          bk: client_num_id
        collection_rk:
          bk: collection_num_id
      ref_map:
        collection_role_cd:
          bk: collection_role
          field: collection_role_cd       
      satellites:
      - satellite: client_collection_cftm
        field_map:
          ods$effective_from_dt: effective_date          

  references: []

creation:
  ora_table: H2_Z#CL_PRIV_REF
  ora_schema: $ODS_SINT
  parallelism:
    ora_parallel_amount: 4
    ora_part_field_name: ODS$EFFECTIVE_TO_CSN
    ora_part_field_type: int
