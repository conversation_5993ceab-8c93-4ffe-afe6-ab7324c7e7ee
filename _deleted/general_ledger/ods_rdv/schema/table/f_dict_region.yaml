schema_version: 1
file_name: f_dict_region
explicit_columns: true

columns:

- name: region_cd
  nullable: false
  type:
    type_class: VARCHAR

- name: region_code
  nullable: false
  type:
    type_class: VARCHAR

- name: region_name
  nullable: false
  type:
    type_class: VARCHAR

- name: region_district_cd
  nullable: false
  type:
    type_class: VARCHAR    

- name: region_eng_name
  nullable: true
  type:
    type_class: VARCHAR

- name: effective_from_date
  nullable: false
  type:
    type_class: DATE

- name: effective_to_date
  nullable: false
  type:
    type_class: DATE

datastore:
  source_system: DTPL
  data_capture_mode: increment
  data_source_type: FLAT_FILE_SCD1
  key_fields:
  - region_cd
  column_groups:
    data: []
  business_keys:
  - business_key: DictRegion
    field_map:
      region_cd: region_cd
  data_quality_checks:
  - dq_check: RowDuplicate
  links: []
  references:
  - reference: dict_region
    field_map:
      region_cd: region_cd
      region_code: region_code
      region_name: region_name
      region_eng_name: region_eng_name
      region_district_cd: region_district_cd
      effective_from_date: effective_from_date
      effective_to_date: effective_to_date

csv_file_params:
  file_name: dict_region.csv
  encoding: utf-8
  delimiter: ';'
  date_format: '%d.%m.%Y'
