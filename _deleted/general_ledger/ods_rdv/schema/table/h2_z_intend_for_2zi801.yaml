schema_version: 2
table_name: h2_z_intend_for_2zi801
explicit_columns: True

columns:

- name: ods$effective_from_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_to_dt
  type:
    type_class: TIMESTAMP

- name: ods$effective_from_csn
  type:
    type_class: DECIMAL

- name: ods$effective_to_csn
  type:
    type_class: DECIMAL

- name: ods$is_active_flg
  type:
    type_class: VARCHAR
    length: 1

- name: ods$deleted_flg
  type:
    type_class: VARCHAR
    length: 1

- name: ods$create_id
  type:
    type_class: DECIMAL
    precision: 38
    scale: 0

- name: ods$update_id
  type:
    type_class: DECIMAL
    precision: 38
    scale: 0

- name: ods$processed_dt
  type:
    type_class: TIMESTAMP

- name: id
  type:
    type_class: BIGINT
    
- name: c_high_level
  type:
    type_class: BIGINT

- name: sn
  type:
    type_class: INTEGER

- name: su
  type:
    type_class: INTEGER

- name: effective_date
  type:
    type_class: DATE

datastore:
  source_system: CFTM
  data_capture_mode: increment
  data_source_type: ODS_SCD2
  synthetic_history:
    syn_history_function: syn_bussiness_date_trunc
    func_param:
    - param_source: field
      param_value: ods$effective_from_dt
    - param_source: const
      param_value: day
    destination_field: effective_date
  key_fields: 
  - id
  - effective_date
  column_groups:
    data: []
  business_keys:
  - business_key: intend_account_num_id
    required: false
    alias: intacc1    
    field_map:
      id: intend_account_id
  - business_key: intend_account_num_id
    required: false
    alias: intacc2    
    field_map:
      c_high_level: intend_account_id
    
  data_quality_checks: []    
    
  links:
    - link: intend_account_parent
      rk_bk_map:
        intend_account_rk:
          bk: intend_account_num_id
          alias: intacc1       
        parent_intend_account_rk:
          bk: intend_account_num_id
          alias: intacc2
      satellites:
      - satellite: intend_account_parent_cftm
        field_map: 
          ods$effective_from_dt: effective_date
          
  references: []

creation:
  ora_table: H2_Z#INTEND_FOR
  ora_schema: $ODS_SINT
  parallelism:
    ora_parallel_amount: 4
    ora_part_field_name: ODS$EFFECTIVE_TO_CSN
    ora_part_field_type: int