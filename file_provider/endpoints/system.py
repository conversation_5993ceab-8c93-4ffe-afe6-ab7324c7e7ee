import importlib
from typing import Any, Dict
from fastapi.responses import JSONResponse
from dwh_services_utils.schemas.system import service as schemas
from fastapi import APIRouter, Depends

from lib.auth.utils import required

router = APIRouter()


@router.get(
    '/status',
    summary='Статус сервиса',
    responses={
        200: {'model': schemas.ServiceStatus},
        503: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
async def service_status():
    try:
        core = importlib.import_module('file_provider.faust_worker.core')
        status = await core.get_command('_service_status_t', 'status')  # noqa
        return schemas.ServiceStatus.from_orm(status)
    except KeyError:
        return JSONResponse(status_code=503, content={'message': 'Сервис недоступен.'})
