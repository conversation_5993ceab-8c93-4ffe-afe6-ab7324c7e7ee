import importlib
from datetime import datetime
from typing import List, Optional

from dwh_services_utils.schemas.file_provider.v1_0 import schemas
from fastapi import APIRouter, Depends
from fastapi.responses import Response, JSONResponse

from lib.auth.utils import required
from lib.kafka.models import file_models
from lib.fastapi_versioning_mod import version
from file_provider.endpoints.utils import check_resource
from file_provider.config import conf
from lib.logging.utils import get_logger


logger = get_logger()
router = APIRouter()


@router.get(
    '/resources',
    response_model=List[schemas.ResourceFileSummary],
    dependencies=[Depends(required(roles=['read_role']))],
)
@version(1, 0)
async def get_resources() -> List[schemas.ResourceFileSummary]:
    """
    Получить список Ресурсов.
    """
    core = importlib.import_module('file_provider.faust_worker.core')
    resources = await core.get_resources_command()  # noqa

    i = 0
    result = []
    for resource in resources:
        if resource['status']['is_deleted']:
            continue
        i = i + 1
        if i >= conf.RESOURCES_PAGE_SIZE:
            break
        result.append(resource)
    return result


@router.get(
    '/resources/{resource_cd}',
    responses={
        200: {'model': schemas.ResourceFileFull},
        404: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['read_role']))]
)
@version(1, 0)
async def get_resource(resource_cd: str):
    """
    Получить информацию о Ресурсе.
    """
    response = await check_resource(resource_cd)
    if isinstance(response, JSONResponse):
        return response
    resource = schemas.ResourceFileFull.from_orm(response)
    return resource


@router.post(
    '/resources',
    response_model=schemas.ResourceFileFull,
    responses={
        201: {'model': schemas.ResourceFileFull},
        409: {'model': schemas.Message},
        400: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
@version(1, 0)
async def create_resource(req: schemas.ResourceFileRequest):
    """
    Зарегистрировать новый Ресурс.
    """
    allowed_conn_types = ['sftp', 'hdfs', 's3api', 'local']
    if req.connections.filelist.type not in allowed_conn_types:
        message = f'Для ресурса {req.resource_cd} передан ' \
                  f'недопустимый тип соединения {req.connections.filelist.type}. ' \
                  f'Допустимые значения: {allowed_conn_types}'
        logger.error(message)
        return JSONResponse(status_code=400, content={'message': message})

    core = importlib.import_module('file_provider.faust_worker.core')
    resource = await core.get_resource_command(req.resource_cd)  # noqa
    if not resource or resource.status.is_deleted:
        model = file_models.ResourceFileFull(**req.dict())
        result = await core.set_resource_command(model.resource_cd, model)  # noqa
        resource = schemas.ResourceFileFull.from_orm(result)
        return resource
    else:
        return JSONResponse(status_code=409, content={'message': 'Ресурс уже существует'})


@router.put(
    '/resources/{resource_cd}',
    responses={
        200: {'model': schemas.ResourceFileFull},
        403: {'model': schemas.Message},
        404: {'model': schemas.Message},
        405: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
@version(1, 0)
async def update_resource(resource_cd: str, req: schemas.ResourceFileRequest):
    """
    Изменить информацию о Ресурсе.
    """
    core = importlib.import_module('file_provider.faust_worker.core')
    response = await check_resource(resource_cd)
    if isinstance(response, JSONResponse):
        return response
    model = file_models.ResourceFileFull(**req.dict())
    result = await core.set_resource_command(model.resource_cd, model)  # noqa

    return result


@router.delete(
    '/resources/{resource_cd}',
    responses={
        200: {'model': schemas.ResourceFileFull},
        403: {'model': schemas.Message},
        404: {'model': schemas.Message},
        405: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
@version(1, 0)
async def delete_resource(resource_cd: str):
    """
    Софт-удаление Ресурса.
    """
    core = importlib.import_module('file_provider.faust_worker.core')
    response = await check_resource(resource_cd)
    if isinstance(response, JSONResponse):
        return response
    response.status.is_deleted = True
    return await core.delete_resource_command(response.resource_cd, response)  # noqa


@router.get(
    '/resources/{resource_cd}/state',
    responses={
        200: {'model': schemas.ResourceFileStateFull},
        403: {'model': schemas.Message},
        404: {'model': schemas.Message},
        424: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['read_role']))]
)
@version(1, 0)
async def get_resource_state(resource_cd: str):
    """
    Получить состояние Ресурса.
    """
    core = importlib.import_module('file_provider.faust_worker.core')
    response = await check_resource(resource_cd)
    if isinstance(response, JSONResponse):
        return response
    state = await core.get_command('_file_metrics_t', resource_cd)  # noqa

    if not state or not hasattr(state, 'queue') or not state.queue:
        return JSONResponse(status_code=424, content={'message': 'У ресурса ещё нет состояния'})
    state = state.queue
    logger.info(state)
    as_of_dttm = max(metric.as_of_dttm for key, metric in state.items())
    resource_state = {
        'as_of_dttm': as_of_dttm,
    }
    for key, metric in state.items():
        # metric.pop('__faust')
        resource_state[key] = metric.value

    resource_state = schemas.ResourceFileStateFull(**resource_state)

    return resource_state


@router.put(
    '/resources/{resource_cd}/state',
    responses={
        405: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
@version(1, 0)
async def update_resource_state(resource_cd: str):
    """
    Изменить состояние Ресурса.
    """
    return JSONResponse(
        status_code=405,
        content={
            'message': 'Провайдер не поддерживает изменение Состояния Ресурсов.'
        }
    )


@router.get(
    '/resources/{resource_cd}/filelist',
    responses={
        200: {'model': schemas.FileListFull},
        403: {'model': schemas.Message},
        404: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['read_role']))]
)
@version(1, 0)
async def get_filelist(resource_cd: str):
    """
    Получить Список файлов.
    """
    core = importlib.import_module('file_provider.faust_worker.core')
    response = await check_resource(resource_cd)
    if isinstance(response, JSONResponse):
        return response
    files = await core.get_command('_file_list_t', resource_cd)  # noqa

    return schemas.FileListFull.from_orm(files)


@router.get(
    '/resources/{resource_cd}/filelist/files',
    responses={
        200: {'model': List[schemas.FileDescription]},
        400: {'model': schemas.Message},
        403: {'model': schemas.Message},
        404: {'model': schemas.Message},
    },
    dependencies=[Depends(required(roles=['read_role']))]
)
@version(1, 0)
async def get_files(
    response: Response,
    resource_cd: str,
    fresh_then_dttm: Optional[datetime] = None,
    limit: int = conf.FILES_LIMIT,
    continuation_token: int = 0
):
    """
    Получить Описания файлов.
    """
    logger.info("get_files!")
    if limit > conf.FILES_MAX_LIMIT:
        return JSONResponse(
            status_code=400,
            content={
                'message': f'Значение параметра limit превышает максимально допустимое значение ({conf.FILES_MAX_LIMIT})!'
            }
        )

    chk_res = await check_resource(resource_cd)
    if isinstance(chk_res, JSONResponse):
        return chk_res

    core = importlib.import_module('file_provider.faust_worker.core')
    files_list_rsrc = await core.get_command('_file_list_t', resource_cd)  # noqa
    if not files_list_rsrc or not files_list_rsrc.files:
        return []

    fresh_files = [
        f for f in files_list_rsrc.files
        if fresh_then_dttm is None or f["last_modified"] >= int(fresh_then_dttm.timestamp())
    ]

    if (continuation_token + limit) < len(fresh_files):
        response.headers["next-continuation-token"] = str(continuation_token + limit)

    return [schemas.FileDescription.parse_obj(f) for f in fresh_files[continuation_token: continuation_token + limit]]
