from file_provider.config import conf
from file_provider.faust_worker import get_faust_app
from lib.kafka.models.common import ServiceStatus

faust_app = get_faust_app()

# # Ресурс
# _file_resources = faust_app.topic(
#     f'srv_file_provider_resources_{conf.TOPIC_CNT}',
#     key_type=str,
#     value_type=ResourceFileFull,
#     internal=True,
# )
#
# # Метрики
# _file_metrics = faust_app.topic(
#     f'srv_file_provider_metrics_{conf.TOPIC_CNT}',
#     key_type=str,
#     value_type=FileMetrics)
#
# # Список файлов
# _file_list = faust_app.topic(
#     f'srv_file_provider_list_{conf.TOPIC_CNT}',
#     key_type=str,
#     value_type=FileListFull)


# Очередь команд - топик на команды для работы с таблицей ресурсов
_resource_command_queue = faust_app.topic(
    f'srv_file_provider_resources_cmdqueue_{conf.ID_SUFFIX}_{conf.TOPIC_CNT}',
    internal=True,
    key_type=str
)

# Очередь команд - топик на команды для работы с таблицей метрик
_metrics_command_queue = faust_app.topic(
    f'srv_file_provider_metrics_cmdqueue_{conf.ID_SUFFIX}_{conf.TOPIC_CNT}',
    internal=True,
    key_type=str
)

# Очередь команд - топик на команды для работы с таблицей списков файлов
_filelist_command_queue = faust_app.topic(
    f'srv_file_provider_list_cmdqueue_{conf.ID_SUFFIX}_{conf.TOPIC_CNT}',
    internal=True,
    key_type=str
)

_service_status = faust_app.topic(
    f'srv_file_provider_service_status_{conf.TOPIC_CNT}',
    key_type=str,
    value_type=ServiceStatus,
    internal=True,
)

# Healthcheck topic
# _kafka_services_healthcheck = faust_app.topic(
#     'srv_provider_kafka_healthcheck',
#     key_type=str,
#     value_type=str
# )
