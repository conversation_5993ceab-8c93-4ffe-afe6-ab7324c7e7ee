from datetime import datetime

from file_provider.utils.scanners import LocalFileScanner, SFTPFileScanner, HDFSFileScanner, S3FileScanner
from file_provider.utils.metric_actualizer import MetricActualizer
from lib.kafka.models.file_models import FileListFull, ResourceFileFull, FileMetricFull
from file_provider.faust_worker.agents import (
    filelist_commands_consumer,
    # health_check_agent,
    metrics_commands_consumer,
    resource_commands_consumer
)
from file_provider.faust_worker.commands import (
    AppendOrUpdateDictParams,
    AppendOrUpdateParams, AppendParams,
    GetParams,
    GetResourceStateParams,
    GetTableParams,
    SetResourceParams,
    UpdateParams,
)
from file_provider.faust_worker.tables import (
    _file_metrics_t,
    _file_resources_t,
    _file_list_t,
)

from file_provider.utils.utils import get_dttm, get_timedelta_refresh, set_metric_ids
from lib.logging.utils import get_logger


logger = get_logger()


async def add_resource_command(resource):
    return await resource_commands_consumer.ask(
        key=resource.resource_cd,
        value=AppendParams('_file_resources_t', resource.resource_cd, resource)
    )


# async def health_check_command(provider_type):
#     try:
#         answer = await health_check_agent.ask(
#             key=provider_type,
#             value='ok'
#         )
#         return answer
#     except Exception as ex:
#         logger.error(f'Kafka is not ok! - {ex}')


async def update_resource_command(resource):
    return await resource_commands_consumer.ask(
        key=resource.resource_cd,
        value=UpdateParams('_file_resources_t', resource.resource_cd, resource)
    )


async def delete_resource_command(resource_cd, resource):
    return await resource_commands_consumer.ask(
        key=resource_cd,
        value=SetResourceParams(resource_cd, resource)
    )


async def get_resource_command(resource_cd):
    return await resource_commands_consumer.ask(
        key=resource_cd,
        value=GetParams('_file_resources_t', resource_cd)
    )


async def get_command(table: str, key: str):
    return await resource_commands_consumer.ask(
        key=key,
        value=GetParams(table, key)
    )


async def get_resources_command():
    return await resource_commands_consumer.ask(
        value=GetTableParams('_file_resources_t')
    )


async def get_resource_state_command(resource_cd):
    return await resource_commands_consumer.ask(
        key=resource_cd,
        value=GetResourceStateParams(resource_cd)
    )


async def set_resource_command(resource_cd, resource):
    logger.debug(f'set_resource_command {resource}')
    return await resource_commands_consumer.ask(
        key=resource_cd,
        value=SetResourceParams(resource_cd, resource)
    )


async def set_resource(resource_cd, value):
    logger.debug(f'set_resource {value} {type(value)}')
    if isinstance(value, ResourceFileFull):
        resource = value
    else:
        resource = ResourceFileFull(**value)
    logger.debug(f'set_resource {resource}')
    resource = await set_metric_ids(resource)
    try:
        res = _file_resources_t[resource_cd]
    except KeyError:
        logger.debug(f'set_resource {1}')
        _file_resources_t[resource_cd] = resource
        logger.debug(f'set_resource {2}')
        await _update_resource_metrics(resource, resource_cd)
        await _update_filelist(resource_cd)
    else:
        _file_resources_t[resource_cd] = resource
        await _update_resource_metrics(resource, resource_cd)
        await _update_filelist(resource_cd)
    return resource


async def _update_filelist(resource_cd):
    filelist = FileListFull(
        resource_cd=resource_cd,
        as_of_dttm=datetime.utcnow(),
    )
    await filelist_commands_consumer.ask(
        key=resource_cd,
        value=AppendOrUpdateParams(
            table='_file_list_t',
            key=resource_cd,
            value=filelist,
        )
    )


async def _update_resource_metrics(resource, resource_cd):
    logger.debug(f'_update_metrics {resource.metrics}')
    for m_key, m_value in resource.metrics.items():
        logger.debug(f'_update_metrics {m_value}')
        metric = FileMetricFull(
            id=m_value.id,
            resource_cd=resource_cd,
            as_of_dttm=datetime.utcnow(),
        )
        await metrics_commands_consumer.ask(
            key=resource_cd,
            value=AppendOrUpdateDictParams(
                dict_key=resource_cd,
                table='_file_metrics_t',
                key=str(m_key),
                value=metric,
                dict_class='FileMetrics',
            )
        )


# async def _update_metrics(metrics, resource_cd):
#     logger.debug(f'_update_metrics {metrics}')
#     for metric in metrics:
#         logger.debug(f'_update_metrics {metric}')
#         await metrics_commands_consumer.ask(
#             key=resource_cd,
#             value=AppendOrUpdateDictParams(
#                 dict_key=resource_cd,
#                 table='_file_resource_metrics_t',
#                 key=str(metric.id.split('.')[-1]),
#                 value=metric,
#                 dict_class='FileResourceMetric',
#             )
#         )


async def get_resource_state(resource_cd):
    try:
        resource = _file_resources_t[resource_cd]
        metrics = _file_metrics_t[resource_cd]
        logger.debug(metrics)
        metrics = metrics.queue
        return metrics
    except KeyError:
        return None


async def file_list_refresh():
    logger.debug(f'Таймер обновления файл листов запущен')
    for file_list in _file_list_t.values():
        if not isinstance(file_list, FileListFull):
            file_list = FileListFull(**file_list)
        resource = _file_resources_t[file_list.resource_cd]
        if resource.status and (resource.status.is_deleted or resource.status.is_maintenance):
            continue
            # raise UnmodifiableException
        await _refresh_file_list(file_list, resource)
        await _actualize_metrics(resource)


async def _refresh_file_list(file_list, resource):
    logger.debug(f'Обновление файл листа для ресурса {resource.resource_cd}')
    if file_list.as_of_dttm:
        file_list.as_of_dttm = get_dttm(file_list.as_of_dttm)
        refresh = get_timedelta_refresh(resource)
        if datetime.utcnow() - file_list.as_of_dttm > refresh:
            await _scan_files(resource)
    else:
        await _scan_files(resource)


async def _scan_files(resource):
    logger.debug(f'_scan_files: type={resource.connections.filelist["type"]} resource_cd={resource.resource_cd}')
    scanners = {
        'local': LocalFileScanner,
        'sftp': SFTPFileScanner,
        'hdfs': HDFSFileScanner,
        's3api': S3FileScanner,
    }
    scanner_type = resource.connections.filelist['type']
    try:
        scanner = scanners[scanner_type](resource)
        scan_file_list = scanner.scan()
    except Exception as e:
        logger.error(f'Не удалось просканировать ресурс {resource.resource_cd}. {e}')
        refresh_dttm = datetime.utcnow()
        scan_file_list = FileListFull(
            resource_cd=resource.resource_cd,
            as_of_dttm=refresh_dttm,
            files=[],
            last_online_dttm=refresh_dttm,
            files_count=0
        )

    logger.debug(f'_scan_files: type={resource.connections.filelist["type"]} resource_cd={resource.resource_cd} '
                        f'files {scan_file_list}')
    await filelist_commands_consumer.ask(
        key=resource.resource_cd,
        value=AppendOrUpdateParams('_file_list_t', resource.resource_cd, scan_file_list)
    )


async def _actualize_metrics(resource: ResourceFileFull):
    logger.debug(f'Актуализация метрик для ресурса {resource.resource_cd}')
    metrics = _file_metrics_t[resource.resource_cd].queue
    file_list = _file_list_t[resource.resource_cd]
    if not isinstance(file_list, FileListFull):
        file_list = FileListFull(**file_list)
    actualizer = MetricActualizer(resource, metrics, file_list)
    actual_metrics = actualizer.actualize()
    for m_key, m_value in actual_metrics.items():
        logger.debug(f'Актуализированные метрики ресурса {resource.resource_cd}, {m_value}')
        await metrics_commands_consumer.ask(
            key=resource.resource_cd,
            value=AppendOrUpdateDictParams(
                dict_key=resource.resource_cd,
                table='_file_metrics_t',
                key=str(m_key),
                value=m_value,
                dict_class='FileMetrics',
            )
        )
