from concurrent.futures import Thr<PERSON><PERSON>ool<PERSON>xecutor
import concurrent.futures
from enum import Enum

import jq
from typing import List
from file_provider.config import conf
from lib.logging.utils import get_logger

logger = get_logger()


class JQType(Enum):
    attrs = 'attrs'
    filter = 'filter'
    metrics = 'metrics'


# TODO change python jq to async-gather-C jq (see ceh-porvider-back)
class JQExecutor:
    """
    Класс выполнения JQ-запросов
    """

    def __init__(self, input_json: List, jq_list: List, jq_type: JQType):
        self.input_json = input_json
        self.jq_list = jq_list
        self.jq_type = jq_type

        if jq_type is JQType.filter:
            self.timeout = conf.JQ_TIMEOUT_FILTER
        elif jq_type is JQType.metrics:
            self.timeout = conf.JQ_TIMEOUT_METRICS
        elif jq_type is JQType.attrs:
            self.timeout = conf.JQ_TIMEOUT_ATTRS
        else:
            self.timeout = conf.JQ_TIMEOUT_DEFAULT

        self.num_workers = len(jq_list)
        self._add_pk()

    def _execute_jq(self, input_json: List, jq_dict: dict):
        if self.jq_type is JQType.metrics:
            return {jq_dict['name']: jq.compile(jq_dict['query']).input(input_json).first()}
        else:
            return jq.compile(jq_dict['query']).input(input_json).all()

    def _stop_process_pool(self, e) -> None:
        for pid, process in e._processes.items():
            process.terminate()
        e.shutdown()

    def _add_pk(self):
        i = 0
        for el in self.input_json:
            el['_pk'] = i
            i += 1

    def _combine_res(self, res_list):
        if len(res_list) == 0:
            return []
        if len(res_list) == 1:
            return res_list[0]
        combine_res = []
        for i in range(len(res_list) - 1):
            for el1 in res_list[i]:
                for el2 in res_list[i + 1]:
                    if el1['_pk'] == el2['_pk']:
                        combine_res.append({**el1, **el2})
        return combine_res

    def run_executor(self) -> List:
        res_list = []

        with concurrent.futures.ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            futures = []
            try:
                for jq_dict in self.jq_list:
                    futures.append(executor.submit(self._execute_jq, self.input_json, jq_dict))
                for future in concurrent.futures.as_completed(futures, timeout=self.timeout):
                    res_list.append(future.result(timeout=self.timeout))
            except concurrent.futures.TimeoutError:
                logger.info(f"Timeout occured during mq executing with {self.jq_list}. Timeout is {self.timeout}")
                self._stop_process_pool(executor)
            except Exception as ex:
                logger.info(f"Error occured during mq executing with {self.jq_list}. Error is {ex}")

        if self.jq_type is JQType.metrics:
            results = res_list
        else:
            results = self._combine_res(res_list)

        # delete technical field if added
        for result in results:
            result.pop('_pk', None)
        # if not clear input_json, _pk push to result-resource
        for item in self.input_json:
            item.pop('_pk', None)

        return results
