from file_provider.utils.scanners.base_scanner import BaseScanner
from lib.kafka.models.file_models import ResourceFileFull
from file_provider.exceptions import InvalidHDFSAuthType
from hdfs.ext.kerberos import KerberosClient
from hdfs.client import InsecureClient
from pathlib import Path
from typing import List, Dict
import os


class HDFSFileScanner(BaseScanner):
    """
    Класс для работы с hdfs - сканирует файлы в директории и возвращает список фалов с атрибутами
    """
    SCANNER_TYPE = 'hdfs'

    def __init__(self, resource: ResourceFileFull):
        super(HDFSFileScanner, self).__init__(resource)

        self.dir_name = resource.connections.filelist['location']['remotedir']

        auth_type = resource.connections.filelist['auth']['type']
        if auth_type == 'insecure':
            self.client = InsecureClient(resource.connections.filelist['location']['server'], root="/")
        elif auth_type == 'kerberos':
            self.client = KerberosClient(resource.connections.filelist['location']['server'], root='/')
        else:
            raise InvalidHDFSAuthType(f'Получен тип авторизации: {auth_type}. Допустимые: kerberos, insecure')

    def get_files_list(self) -> List[Dict]:
        file_list = []
        file_generator = self.client.walk(self.dir_name, status=True)
        for root, dirs, files in file_generator:
            for file_name, file_attrs in files:
                full_file_path = str(Path(root[0]) / file_name)

                attrs = {
                    'uri': full_file_path,
                    'folders': list(filter(None, root[0].split(os.sep))),
                    'filename': file_name,
                    'ext': Path(file_name).suffix.replace('.', ''),
                    'length': file_attrs['length'],
                    'last_modified': file_attrs['modificationTime'],
                }
                file_list.append(attrs)
        return file_list


if __name__ == '__main__':
    res = ResourceFileFull(
        **{
            "resource_cd": "test_res",
            "resource_desc": "test description",
            "tags": [],
            "features": {},
            "status": {
                "is_readonly": False,
                "is_maintenance": False,
                "is_deleted": False
            },
            "connections": {
                "filelist": {
                  "type": "hdfs",
                  "location": {
                    "server": "http://127.0.0.1:50070",
                    "remotedir": "/"
                  },
                  "auth": {
                    "type": "insecure"
                  }
                }
            },
            "filelist": {
                "computed_attributes": [
                    {
                        "name": "test_file_num",
                        "expr": '.[] | . += {"test_file_num": (.filename)[4:5]}'
                    }
                ],
                "filter": '',
                "refresh": "PDT0H1M"
            },
            "metrics": {
                "is_locked": {
                    "id": "test_res.is_locked",
                    "query": "1 != 1",
                    "default": False
                },
                "locked_by": {
                    "id": "test_res.locked_by",
                    "query": "-1",
                    "default": -1
                },
                "version_id": {
                    "id": "test_res.version_id",
                    "query": "1",
                    "default": 1
                }
            }
        }
    )
    scanner = HDFSFileScanner(res)
    lst = scanner.scan()
    print(lst.files)
