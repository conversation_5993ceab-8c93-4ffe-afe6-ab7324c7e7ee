from file_provider.utils.scanners.base_scanner import BaseScanner
from lib.kafka.models.file_models import ResourceFileFull
from botocore.exceptions import ClientError
from configparser import ConfigParser
from lib.logging.utils import get_logger
from pathlib import Path
from typing import List, Dict
import boto3
import os

logger = get_logger()

class S3FileScanner(BaseScanner):
    """
    Класс для работы с S3 - сканирует файлы в директории и возвращает список фалов с атрибутами
    """
    SCANNER_TYPE = 's3api'

    def __init__(self, resource: ResourceFileFull):
        super(S3FileScanner, self).__init__(resource)
        self.filter = resource.connections.filelist.get('filter', dict())

        profile = resource.connections.filelist['auth']['profile']
        if profile == 'root':
            aws_credentials_path = os.path.join(os.sep, profile, '.aws', 'credentials')
        else:
            aws_credentials_path = os.path.join(os.sep, 'home', profile, '.aws', 'credentials')
        config = ConfigParser()
        config.read(aws_credentials_path)

        profile_section = resource.connections.filelist['auth'].get('section', 'default')
        aws_access_key_id = config.get(profile_section, 'aws_access_key_id')
        aws_secret_access_key = config.get(profile_section, 'aws_secret_access_key')
        endpoint = f'{resource.connections.filelist["location"]["endpoint"]}:'\
                   f'{resource.connections.filelist["location"]["port"]}'

        self.bucket = resource.connections.filelist["location"]["bucket"]
        self.client = boto3.client('s3', aws_access_key_id=aws_access_key_id,
                                   aws_secret_access_key=aws_secret_access_key,
                                   endpoint_url=endpoint)

    def get_files_list(self) -> List[Dict]:
        try:
            all_files_attrs = self.iterate_bucket_items()
        except ClientError as ex:
            if ex.response['Error']['Code'] == 'NoSuchBucket':
                logger.error(f'Для ресурса {self.resource.resource_cd} не найден bucket {self.bucket}')
                return []
            else:
                raise

        file_list = []
        for file in all_files_attrs:
            file_name = file['Key']
            splited_path = list(filter(None, file_name.split(os.sep)))  # eq [x for x in file_name.split(os.sep) if x]
            clear_file_name = splited_path[-1]
            folders = splited_path[:-1]
            attrs = {
                'uri': file_name,
                'folders': folders,
                'filename': clear_file_name,
                'ext': Path(clear_file_name).suffix.replace('.', ''),
                'size': file['Size'],
                'last_modified': int(file['LastModified'].timestamp()),
            }
            file_list.append(attrs)
        return file_list

    def iterate_bucket_items(self):
        paginator = self.client.get_paginator('list_objects_v2')
        operation_parameters = {
            'Bucket': self.bucket
        }
        filters = ['Prefix', 'MaxItems', 'Delimiter', 'EncodingType']
        for s3_filter in filters:
            if self.filter.get(s3_filter):
                operation_parameters[s3_filter] = self.filter[s3_filter]
        page_iterator = paginator.paginate(**operation_parameters)

        for page in page_iterator:
            if page['KeyCount'] > 0:
                for item in page['Contents']:
                    yield item


if __name__ == '__main__':
    res = ResourceFileFull(
        **{
            "resource_cd": "test_res",
            "resource_desc": "test description",
            "tags": [],
            "features": {},
            "status": {
                "is_readonly": False,
                "is_maintenance": False,
                "is_deleted": False
            },
            "connections": {
                "filelist": {
                    "type": "s3api",
                    "location": {
                      "endpoint": "http://127.0.0.1",
                      "port": 7777,
                      "bucket": "test-bucket"
                    },
                    "auth": {
                      "profile": "dwhservices"
                    },
                    "filter": {
                        "MaxKeys": 500,
                        "Prefix": "test"
                    }
                }
            },
            "filelist": {
                "computed_attributes": [
                    {
                        "name": "test_file_num",
                        "expr": '.[] | . += {"test_file_num": (.filename)[4:5]}'
                    }
                ],
                "filter": '',
                "refresh": "PDT0H1M"
            },
            "metrics": {
                "is_locked": {
                    "id": "test_res.is_locked",
                    "query": "1 != 1",
                    "default": False
                },
                "locked_by": {
                    "id": "test_res.locked_by",
                    "query": "-1",
                    "default": -1
                },
                "version_id": {
                    "id": "test_res.version_id",
                    "query": "1",
                    "default": 1
                }
            }
        }
    )
    scanner = S3FileScanner(res)
    lst = scanner.scan()
    print(lst.files)
