import logging
from typing import Dict, List

from .base import BaseClient
from ..schemas.base_provider.resource import ResourceCreateRequest, ResourceUpdateRequest, \
    Resource, ResourceState
from ..utils.decorators import log_exceptions


logger = logging.getLogger(__name__)


class ProviderClient(BaseClient):
    """
    Клиент для провайдера ресурсов ODS.
    """
    @log_exceptions(logger)
    def get_resources(self) -> List[Resource]:
        """
        Получить список обслуживаемых ресурсов.
        """
        resp = self.get('api/0.3/resources')
        return [Resource(**i) for i in resp]

    @log_exceptions(logger)
    def get_resource(self, *, resource_cd: str) -> Resource:
        """
        Получить ресурс (в состоянии только версия).
        """
        resp = self.get(f'api/0.3/resources/{resource_cd}')
        return Resource(**resp)

    @log_exceptions(logger)
    def create_resource(self, parameters: Dict) -> Resource:
        """
        Создать ресурс.
        """
        req_data = ResourceCreateRequest(**parameters)
        resp = self.post(
            'api/0.3/resources',
            json=req_data.dict()
        )
        return Resource(**resp)

    @log_exceptions(logger)
    def update_resource(self, parameters: Dict, resource_cd: str) -> Resource:
        """
        Изменить ресурс.
        """
        req_data = ResourceUpdateRequest(**parameters)
        resp = self.put(
            f'api/0.3/resources/{resource_cd}',
            json=req_data.dict()
        )
        return Resource(**resp)

    @log_exceptions(logger)
    def get_resource_state(self, *, resource_cd: str) -> ResourceState:
        """
        Получить состояние ресурса.
        """
        resp = self.get(f'api/0.3/resources/{resource_cd}/state')
        return ResourceState(**resp)
