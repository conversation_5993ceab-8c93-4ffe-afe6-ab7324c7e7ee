import logging
from typing import Dict, List

from .base import BaseClient
from ..schemas.kafka_provider.v1_0.schemas import ResourceKafkaFull, ResourceKafkaSummary, ResourceKafkaStateFull
from ..utils.decorators import log_exceptions

logger = logging.getLogger(__name__)


class KafkaProviderClient(BaseClient):
    """
    Клиент для универсального провайдера БД
    """
    @staticmethod
    def get_version():
        return '1.0'

    @log_exceptions(logger)
    def get_resources(self, limit: int = 100, offset: int = 0) -> List[ResourceKafkaSummary]:
        """
        Получить список обслуживаемых ресурсов.
        """
        resp = self.get('api/1.0/resources', params={'limit': limit, 'offset': offset})
        return [ResourceKafkaSummary(**i) for i in resp]

    @log_exceptions(logger)
    def get_resource(self, *, resource_cd: str) -> ResourceKafkaFull:
        """
        Получить ресурс
        """
        resp = self.get(f'api/1.0/resources/{resource_cd}')
        return ResourceKafkaFull(**resp)

    @log_exceptions(logger)
    def create_resource(self, parameters: Dict) -> ResourceKafkaFull:
        """
        Создать ресурс.
        """
        resp = self.post(
            'api/1.0/resources',
            json=parameters
        )
        return ResourceKafkaFull(**resp)

    @log_exceptions(logger)
    def update_resource(self, parameters: Dict, resource_cd: str) -> ResourceKafkaFull:
        """
        Изменить ресурс.
        """
        resp = self.put(
            f'api/1.0/resources/{resource_cd}',
            json=parameters
        )
        return ResourceKafkaFull(**resp)

    @log_exceptions(logger)
    def delete_resource(self, resource_cd: str) -> ResourceKafkaFull:
        """
        Удалить ресурс.
        """
        resp = self.delete(
            f'api/1.0/resources/{resource_cd}'
        )
        return ResourceKafkaFull(**resp)

    @log_exceptions(logger)
    def get_resource_state(self, resource_cd: str) -> ResourceKafkaStateFull:
        """
        Получить состояние ресурса.
        """
        states = self.get(f'api/1.0/resources/{resource_cd}/state')

        return ResourceKafkaStateFull(**states)

