import logging
from datetime import datetime
from typing import Dict, List, Optional

from .base import BaseClient
from ..schemas.file_provider.v1_0.schemas import (
    FileDescription,
    FileListFull,
    ResourceFileFull,
    ResourceFileStateFull,
    ResourceFileSummary,
)
from ..utils.decorators import log_exceptions

logger = logging.getLogger(__name__)


class FileProviderClient(BaseClient):
    """
    Клиент для файлового провайдера
    """

    @staticmethod
    def get_version():
        return '1.0'

    @log_exceptions(logger)
    def get_resources(self) -> List[ResourceFileSummary]:
        """
        Получить список обслуживаемых ресурсов.
        """
        resp = self.get('api/1.0/resources')
        return [ResourceFileSummary(**i) for i in resp]

    @log_exceptions(logger)
    def get_resource(self, *, resource_cd: str) -> ResourceFileFull:
        """
        Получить ресурс
        """
        resp = self.get(f'api/1.0/resources/{resource_cd}')
        return ResourceFileFull(**resp)

    @log_exceptions(logger)
    def create_resource(self, parameters: Dict) -> ResourceFileFull:
        """
        Создать ресурс.
        """
        _ = ResourceFileFull(**parameters)
        resp = self.post(
            'api/1.0/resources',
            json=parameters
        )
        return ResourceFileFull(**resp)

    @log_exceptions(logger)
    def update_resource(self, parameters: Dict, resource_cd: str) -> ResourceFileFull:
        """
        Изменить ресурс.
        """
        _ = ResourceFileFull(**parameters)
        resp = self.put(
            f'api/1.0/resources/{resource_cd}',
            json=parameters
        )
        return ResourceFileFull(**resp)

    @log_exceptions(logger)
    def delete_resource(self, resource_cd: str) -> ResourceFileFull:
        """
        Удалить ресурс.
        """
        resp = self.delete(
            f'api/1.0/resources/{resource_cd}'
        )
        return ResourceFileFull(**resp)

    @log_exceptions(logger)
    def get_resource_state(self, resource_cd: str) -> ResourceFileStateFull:
        """
        Получить состояние ресурса.
        """
        states = self.get(f'api/1.0/resources/{resource_cd}/state')

        return ResourceFileStateFull(**states)

    @log_exceptions(logger)
    def get_filelist(self, resource_cd: str) -> FileListFull:
        """
        Получить файл лист.
        """
        resp = self.get(f'api/1.0/resources/{resource_cd}/filelist')

        return FileListFull(**resp)

    @log_exceptions(logger)
    def get_files(
            self,
            resource_cd: str,
            fresh_then_dttm: Optional[datetime] = None,
            continuation_token: int = 0
    ) -> List[FileDescription]:
        """
        Получить файлы.
        """
        resp = self.get(
            endpoint=f'api/1.0/resources/{resource_cd}/filelist/files',
            params={
                'fresh_then_dttm': fresh_then_dttm,
                'continuation_token': continuation_token,
            }
        )

        return [FileDescription(**i) for i in resp]
