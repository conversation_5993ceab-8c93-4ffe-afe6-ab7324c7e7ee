import os
from datetime import datetime, timedelta
from typing import List, Optional

from pydantic import UUID4, BaseModel, Field
from pydantic.json import timedelta_isoformat
# from tx_manager.config import conf


commit_timeout = os.environ.get('DEFAULT_COMMIT_TIMEOUT') \
    if not os.environ.get('DEFAULT_COMMIT_TIMEOUT') is None else 3600


class TransactionStatus(BaseModel):
    """Статус Транзакции."""

    start_dttm: Optional[datetime] = Field(
        description="Время начала (создания) Транзакции"
    )
    finish_dttm: Optional[datetime] = Field(
        description="Время конца (принятия или отмены) Транзакции"
    )
    is_active: Optional[bool] = Field(
        description="Признак активной (выполняемой) Транзакции"
    )
    is_committed: Optional[bool] = Field(
        description="Признак принятой Транзакции (false для отмененной, null для активной)"
    )
    is_readonly: Optional[bool] = Field(
        description="Признак наличия изменений в Транзакции"
    )
    consented_to_commit: Optional[bool] = Field(
        description="Признак общего консенсуса на коммит (false если на отмену, null если нет консенсуса)"
    )
    client_committed: Optional[bool] = Field(
        description="Признак команды на коммит от клиента (false если на отмену, null если не было команды)"
    )
    rollback_reasons: Optional[List[str]] = Field(
        None,
        description="Причины отмены транзакции"
    )


class TransactionStage(BaseModel):
    """Текущая фаза транзакции."""

    stage_no: Optional[int] = Field(
        description="Текущая фаза транзакции",
        ge=0,
        le=3
    )


class Transaction(BaseModel):
    """Полное представление Транзакции."""

    tx_uid: UUID4 = Field(
        description="Ид глобальной Транзакции"
    )
    commit_timeout: timedelta = Field(
        description="Таймаут, в течение которого клиент должен либо принять транзакцию, либо продолжить ожидание."
    )
    status: TransactionStatus
    stage: TransactionStage

    class Config:
        orm_mode = True
        json_encoders = {
            timedelta: timedelta_isoformat
        }

    @classmethod
    def from_orm(cls, obj):
        return cls.parse_obj({
            'tx_uid': obj.tx_uid,
            'commit_timeout': obj.commit_timeout,
            'status': {
                'start_dttm': obj.start_dttm,
                'finish_dttm': obj.finish_dttm,
                'is_active': obj.is_active,
                'is_committed': obj.is_committed,
                'is_readonly': obj.is_readonly,
                'consented_to_commit': obj.consented_to_commit,
                'client_committed': obj.client_committed,
                'rollback_reasons': obj.rollback_reasons,
            },
            'stage': {
                'stage_no': obj.stage_no
            }
        })


class TransactionWithToken(Transaction):
    """Транзакция с токеном."""
    tx_token: str = Field(
        description="Токен для операций над транзакцией"
    )

    @classmethod
    def from_orm(cls, obj):
        data = Transaction.from_orm(obj).dict()
        data['tx_token'] = obj.tx_token
        return cls.parse_obj(data)


class TransactionCreate(BaseModel):
    """Схема для создания транзакции."""

    commit_timeout: Optional[timedelta] = Field(commit_timeout, description="Таймаут транзакции в секундах")


class TxToken(BaseModel):
    """Токен для авторизации операций над транзакцией."""

    tx_token: str


class TransactionUpdate(TxToken):
    """Схема для обновления транзакции."""

    commit_timeout: timedelta


class TransactionCommit(TxToken):
    """Коммит."""


class TransactionRollback(TxToken):
    """Откат."""
