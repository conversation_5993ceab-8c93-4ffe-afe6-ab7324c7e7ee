from datetime import datetime
from pydantic import BaseModel, Field


class ShutdownRequest(BaseModel):
    """ Завершение работы сервиса. Формат запроса """
    description: str


class ShutdownResponse(BaseModel):
    """ Завершение работы сервиса """

    class Extra(BaseModel):
        """ Метрики готовности сервиса к завершению работы """
        active_tx: int = Field(None, description='Количество открытых транзакций')

    as_of_dttm: datetime = Field(None, description='Время актуализации статуса')
    service_mode: str = Field(None, description='Режим работы сервиса')
    ready_for_shutdown: bool = Field(None, description='Признак готовности к завершению работы')
    extra: Extra

