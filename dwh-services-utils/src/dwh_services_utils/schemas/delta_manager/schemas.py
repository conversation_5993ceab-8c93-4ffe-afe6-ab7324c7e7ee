from pydantic import BaseModel, Field
import datetime
from enum import Enum
from typing import List, Optional


class ExcludeResource(BaseModel):
    """ Список исключенных из репликации ресурсов """
    resource_cd: str
    comment: str


class ShutdownRequest(BaseModel):
    """ Завершение работы сервиса. Формат запроса """
    description: str


class ShutdownResponse(BaseModel):
    """ Завершение работы сервиса """
    as_of_dttm: datetime.datetime = Field(None, description='Время актуализации статуса')
    service_mode: str = Field(None, description='Режим работы сервиса')
    ready_for_shutdown: bool = Field(None, description='Признак готовности к завершению работы')


class StatusType(Enum):
    success = 'SUCCESS'
    progress = 'PROGRESS'
    idle = 'IDLE'
    failed = 'FAILED'
    rollback = 'ROLLBACK'


class OperationStatus(BaseModel):
    start_dttm: datetime.datetime = Field(None, description='Время старта версии')
    finish_dttm: Optional[datetime.datetime] = Field(None, description='Время завершения версии')
    is_active: Optional[bool] = Field(None, description='Признак активности транзакции')
    is_committed: Optional[bool] = Field(None, description='Признак того, что версия была закоммичена')
    is_undone: Optional[bool] = Field(None, description='Признак отзыва версии')
    is_processed: Optional[bool] = Field(None, description='Признак того, что версия была обработана')
    rollback_reasons: Optional[List[str]] = Field(
        None, description='Причины rollback'
    )


class VersionStatus(BaseModel):
    """
    Статус версии
    """

    class Config:
        orm_mode = True

    tx_uid: str = Field(None, description='Транзакция')
    version_id: int = Field(None, description='Номер версии')
    queue_size: Optional[int] = Field(None, description='Размер очереди')
    queue_number: Optional[int] = Field(None, description='Порядковый номер в очереди')
    current_version_id: Optional[int] = Field(None, description='Текущий номер версии')
    current_version_time: Optional[int] = Field(None, description='Время обработки текущей версии, сек')
    status: OperationStatus = Field(None, description='Статус версии')
    as_of_dttm: datetime.datetime = Field(None, description='Время актуализации Статуса.')


class VersionStatusResponse(BaseModel):
    """Схема ответа."""
    body: Optional[VersionStatus]
    status: StatusType
    message: str


class VersionSummary(BaseModel):
    """
    Краткое представление Версии.
    """
    version_id: int
