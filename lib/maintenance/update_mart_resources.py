from argparse import ArgumentParser
from copy import deepcopy
from pathlib import Path
from os import getenv
from os import walk
import psycopg2
import logging
import json


if getenv('LOG_FILE'):
    with open(getenv('LOG_FILE'), 'w') as f:
        pass

logging.basicConfig(
    filename=getenv('LOG_FILE'),
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

parser = ArgumentParser(
    description='Скрипт для обновления параметров витринных ресурсов',
    epilog='LOG_FILE=update_mart_resources.log python3 update_mart_resources.py'
           ' -r etl-scale/_resources/ceh -H host.corp.dev.vtb -u dev_dwh_etl -d dev_dwh -p passwd'
)

parser.add_argument("-r",  "--resources", dest="resources", required=True,
                    help="Путь до ресурсов etl-scale/_resources/ceh")
parser.add_argument("-H",  "--host", dest="host", required=True, help="Хост БД мониторинга")
parser.add_argument("-u",  "--user", dest="user", required=True, help="Логин юзера для БД мониторинга")
parser.add_argument("-d",  "--dbname", dest="dbname", required=True, help="Название БД мониторинга")
parser.add_argument("-p",  "--password", dest="password", required=True, help="Пароль БД мониторинга")
parser.add_argument("-P",  "--port", dest="port", default=5432, help="Порт БД мониторинга (по умолчанию 5432)")
args = parser.parse_args()


def _load_json(json_file_name, encoding='utf-8'):
    with open(json_file_name, encoding=encoding) as json_file:
        return json.load(json_file)


def read_file(file):
    file = Path(file)
    if file.suffix.lower() == '.json':
        try:
            file_resources = _load_json(file)
        except UnicodeDecodeError:
            file_resources = _load_json(file, encoding='latin-1')
        except json.decoder.JSONDecodeError:
            file_resources = _load_json(file, encoding='utf-8-sig')
        return file_resources
    else:
        return


GP_CONNECT = dict(
    host=args.host,
    port=args.port,
    user=args.user,
    password=args.password,
    dbname=args.dbname,
    application_name='SSUM_MART_RES_ACTUALIZER'
)
sql = """
    SELECT dataset_schema_name, dataset_name, resource_cd
    FROM metamodel.v_bdm_resource_parent
"""
mart_to_res = {}
with psycopg2.connect(**GP_CONNECT) as conn:
    with conn.cursor() as cursor:
        logging.info(f'SQL:{sql}')
        cursor.execute(sql)
        data = cursor.fetchall()
for row in data:
    mart_to_res[f'ceh.{row[0]}.{row[1]}'] = row[2]


def update_res(r):
    if r['resource_cd'] not in mart_to_res:
        return r

    _, sch, tbl = r['resource_cd'].split('.')
    mart_resources_agg = ','.join(mart_to_res.pop(r['resource_cd']))
    if not r.get('features'):
        r['features'] = {}
    r['features']['parent_resources'] = mart_resources_agg

    if not r.get('datasets'):
        r['datasets'] = []
    if (sch, tbl) not in [(d.get('schema_name'), d.get('name')) for d in r['datasets']]:
        r['datasets'].append({
          "name": tbl,
          "schema_name": sch
        })
    return r


logging.info(f'Будет обновлено {len(mart_to_res)} ресурсов')
file_generator = walk(args.resources)
for root, dirs, files in file_generator:
    for file_name in files:
        try:
            resource = read_file(root + '/' + file_name)
            if not resource:
                continue

            old_res = deepcopy(resource)
            resource = update_res(resource)

            if old_res != resource:
                with open(root + '/' + file_name, 'w', encoding='UTF-8') as f:
                    f.write(json.dumps(resource, indent=4, ensure_ascii=False))
        except Exception as e:
            logging.error(f"{root + '/' + file_name}: {e}")


logging.info(f'{len(mart_to_res)} ресурсов не найдено в etl-scale, они будут созданы как новые')
for missed_resource, mart_resources in mart_to_res.items():
    _, schema, table = missed_resource.split('.')
    if not (Path(args.resources) / schema).exists():
        (Path(args.resources) / schema).mkdir(parents=True)
    with open(Path(args.resources) / schema / f'{missed_resource}.json', 'w') as f:
        new_mart_resource = {
          "resource_desc": f"Mart resource {schema}.{table}",
          "tags": [
            "mart_resource"
          ],
          "features": {
            "domain": "BDM",
            "source_system": "DTPL",
            "parent_resources": ','.join(mart_resources),
          },
          "metrics": {},
          "resource_cd": missed_resource,
          "is_readonly": True,
          "is_deleted": False,
          "datasets": [
            {
              "name": table,
              "schema_name": schema
            }
          ]
        }
        f.write(json.dumps(new_mart_resource, indent=4, ensure_ascii=False))
logging.info('Ресурсы успешно обновлены')
