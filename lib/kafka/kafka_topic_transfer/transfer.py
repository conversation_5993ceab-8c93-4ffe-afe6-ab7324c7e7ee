import datetime
import json
import logging
import pathlib
import sys
from enum import Enum
from pathlib import Path

from dwh_services_utils.clients import OdsProviderClient, CehProviderClient, SequenceGeneratorClient
from pydantic import BaseModel


class System(Enum):
    ODS = 'ODS'
    CEH = 'CEH'
    SEQUENCE = 'SEQUENCE'

    def __str__(self):
        return 'ODS, CEH, SEQUENCE'


def _init_logger():
    log_formatter = logging.Formatter('%(asctime)s [%(levelname)-5.5s]  %(message)s')
    root_logger = logging.getLogger()
    root_logger.setLevel('DEBUG')
    file_handler = logging.FileHandler(
        f'{pathlib.Path().resolve()}/{str(datetime.datetime.now())[:19].replace(":","_")}.log')
    file_handler.setFormatter(log_formatter)
    root_logger.addHandler(file_handler)

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_formatter)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(logging.StreamHandler(sys.stdout))
    return root_logger


class Transfer:
    def __init__(self, **kwargs):
        self.logger = _init_logger()
        try:
            self.system = System(kwargs.get('system'))
        except ValueError:
            self.logger.error(f'Неверное значение системы: {kwargs.get("system")}, '
                          f'допустимы следующие значения {[name for name, member in System.__members__.items()]}')
        self.uri = kwargs.get('uri')
        self.client_id = kwargs.get('client_id')
        self.client_secret = kwargs.get('client_secret')
        self.token_url = kwargs.get('token_url')
        self.username = kwargs.get('username')
        self.password = kwargs.get('password')
        self.file_dir = Path(kwargs.get('file_dir'))
        self.clients = {
            System.ODS: OdsProviderClient(self.uri),
            System.CEH: CehProviderClient(self.uri),
            System.SEQUENCE: SequenceGeneratorClient(self.uri)
        }
        self.get_method = {
            System.ODS: 'get_resources',
            System.CEH: 'get_resources',
            System.SEQUENCE: 'get_list'
        }
        self.post_method = {
            System.ODS: 'create_resource',
            System.CEH: 'create_resource',
            System.SEQUENCE: 'increment'
        }
        self.post_params = {
            System.ODS: 'get_resources',
            System.CEH: {'resource_cd', 'resource_desc', 'tags', 'features', 'is_readonly'},
            System.SEQUENCE: ('key', 'increment')
        }

    def to_file(self):
        self.logger.info(f'Инициализация клиента {self.system.value}')
        client = self.clients[self.system]
        self.logger.info(f'Выгрузка в файл: {self.file_dir}')
        with open(self.file_dir, 'w') as f:
            data = getattr(client, self.get_method[self.system])()
            if not data:
                self.logger.error('Получен пустой список!!!')
                return
            if isinstance(data.pop(), BaseModel):
                json.dump([x.dict() for x in getattr(client, self.get_method[self.system])()], f)
            else:
                json.dump(getattr(client, self.get_method[self.system])(), f)
        logging.info(f'Данные выгружены в файл: {self.file_dir}')

    def from_file(self):
        self.logger.info(f'Инициализация клиента {self.system.value}')
        client = self.clients[self.system]
        self.logger.info(f'Загрузка данных из файла: {self.file_dir}')
        with open(self.file_dir, 'r') as f:
            data = json.load(f)

        for item in data:
            try:
                self.client_call_function(client, item)
            except Exception as ex:
                self.logger.error(f'Ошибка при создании элемента: {item}, {ex}')

    def client_call_function(self, client, item):
        if self.system is System.SEQUENCE:
            getattr(client, self.post_method[self.system])(*item.values())
        elif self.system is System.CEH:
            item.pop('state', None)
            getattr(client, self.post_method[self.system])(**item)
        elif self.system is System.ODS:
            getattr(client, self.post_method[self.system])(**item)
