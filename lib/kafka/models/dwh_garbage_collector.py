import datetime
from abc import ABC
from enum import Enum

from typing import Any, Dict, List, Optional, OrderedDict

import faust


class DeleteObject(faust.Record, ABC):
    """Класс для передачи команды удаления в топик"""
    origin: Dict[str, Any]
    delete_object: Dict[str, Any]


class DeleteObjects(faust.Record, ABC):
    """Класс для передачи команды удаления в топик"""
    origin: Dict[str, Any]
    delete_objects: List[Dict[str, Any]]


class DeleteStatus(Enum):
    """Статусы удаления"""
    error = 'error'
    pending = 'pending'
    success = 'success'
    skipped = 'skipped'


class ObjectDeleteStatus(faust.Record, ABC):
    """Статус удаления объекта"""
    object_key: str
    delete_status: DeleteStatus
    delete_object: Optional[Dict[str, Any]]
    details: Optional[str]
