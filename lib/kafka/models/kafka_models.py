from abc import ABC
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum

import faust


class ConnectionUsage(Enum):
    METRICS = 'metrics'
    DATA = 'data'

    def __str__(self):
        return 'data, metrics'


class Status(faust.Record, ABC):
    """
    Статус Ресурса
    """
    is_readonly: Optional[bool]
    is_maintenance: Optional[bool]
    is_deleted: Optional[bool]


class KafkaConnection(faust.Record, ABC):
    """
    Определения соединений
    """
    type: str
    usage: Optional[ConnectionUsage]
    bootstrap: str
    options: Optional[Dict[str, Any]]


class Location(faust.Record, ABC):
    """
    Расположение данных
    """
    profile: str
    server: str
    options: Optional[Dict[str, Any]]


class Format(faust.Record, ABC):
    """
    Представление данных
    """
    type: str
    options: Optional[Dict[str, Any]]


class KafkaConnectionPXF(faust.Record, ABC):
    """
    Определение Соединения по PXF
    """
    type: str
    usage: Optional[ConnectionUsage]
    location: Location
    format: Format


class BindParam(faust.Record, ABC):
    name: str
    value: Any
    sqltype: str


class KafkaMetricDefinition(faust.Record, ABC):
    """
    Определение расчетной Метрики
    """
    id: Optional[Any]
    connection: Optional[str]
    topic: str
    query: Optional[str]
    query_parameters: Optional[List[BindParam]]
    query_exec_timeout: Optional[int]
    default: Any
    refresh: Optional[str]
    alias: Optional[str]


class ResourceKafkaFull(faust.Record, ABC):
    """
    Определения Состояния Ресурса
    """
    resource_cd: str
    resource_desc: Optional[str]
    tags: Optional[List[str]]
    features: Optional[Dict[str, Any]]
    status: Optional[Status]
    connections: Optional[Dict[str, Union[KafkaConnection, KafkaConnectionPXF]]]
    metrics: Optional[Dict[str, KafkaMetricDefinition]]


class KafkaMetricFull(faust.Record, ABC):
    """
    Значение расчетной Метрики
    """
    id: str
    resource_cd: Optional[str]
    as_of_dttm: Optional[datetime]
    value: Optional[Any]
    last_online_dttm: Optional[datetime]
    last_online_value: Optional[Any]


class KafkaResourceMetric(faust.Record, ABC):
    """
    Метрики ресурса
    """
    resource_cd: str
    queue: Dict[str, KafkaMetricFull]

