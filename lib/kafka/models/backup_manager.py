from abc import ABC
from enum import Enum
from typing import List, Optional

import faust


class BackupRequestItem(faust.Record, ABC):
    """Запрос на репликацию 1 объекта/схемы. table_name может быть None"""
    schema_name: str
    table_name: str


class BackupRequest(faust.Record, ABC):
    """Класс для передачи списка BackupRequestItem в топик"""
    backup_items: List[BackupRequestItem]


class BackupStatus(Enum):
    """Статусы бекапа"""
    local_processing = 'local_processing'
    local_success = 'local_success'
    local_failed = 'local_failed'
    export_processing = 'export_processing'
    export_success = 'export_success'
    export_failed = 'export_failed'
    remote_processing = 'remote_processing'
    remote_success = 'remote_success'
    remote_failed = 'remote_failed'
    cancel_status = 'cancel_status'


class ChunkBackupStatus(faust.Record, ABC):
    """Статус бекапа 1 куска данных некоторой таблицы"""
    number: int
    rowcount: Optional[int]
    pxf_location: Optional[str]
    base_full_table_name: str
    backup_status: BackupStatus


class TableBackupStatus(faust.Record, ABC):
    """Статус бекапа некоторой таблицы"""
    chunks: Optional[List[str]]
    backup_status: BackupStatus
    details: Optional[str]
