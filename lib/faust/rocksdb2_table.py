from abc import ABC

import faust
from typing import Union
from faust.types.stores import StoreT
from yarl import URL
from lib.faust.rocksdb2_store import RocksDB2Store


class RocksDB2Table(faust.Table, ABC):
    def _new_store_by_url(self, url: Union[str, URL]) -> StoreT:
        return RocksDB2Store(
            url,
            self.app,
            self,
            table_name=self.name,
            key_type=self.key_type,
            key_serializer=self.key_serializer,
            value_serializer=self.value_serializer,
            value_type=self.value_type,
            loop=self.loop,
            options=self.options,
        )
