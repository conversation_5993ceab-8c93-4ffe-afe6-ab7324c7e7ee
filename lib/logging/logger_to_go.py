import json
import pytz
import socket
from copy import deepcopy
from datetime import datetime
from hashlib import md5
from typing import Union, Optional
from uuid import UUID

from faust.serializers.codecs import dumps

import faust


def get_md5_hash(value: str) -> str:
    return md5(value.encode('UTF-8')).hexdigest().lower()


class LoggerToGo:
    def __init__(self, log_table, data: Union[faust.Record, dict], conf):
        self._service_logger = {
            "version": '1.0'
        }
        self._conf = conf
        self._name = conf.IDENT
        self._type = log_table
        self._data = deepcopy(data)
        self._host = socket.gethostname()
        self._instance = conf.INSTANCE
        self._kid = conf.KID
        self._write = {
            "service_logger": self._service_logger,
            "name": self._name,
            "type": self._type,
            "instance": self._instance,
            "host": self._host,
            "kid": self._kid
        }

    def version(self):
        self._write['data'] = self._data
        return self._write

    def version_history(self):
        self._write['data'] = {
            "version_id": self._data.version_id,
            "processed_dttm": datetime.now().isoformat(),
            "version": self._data
        }
        return self._write

    def version_origin_info_or_version_counts(self):
        self._write['data'] = self._data
        return self._write

    def resource_state(self):
        self._write['data'] = {
            "version_id": self._data.version.version_id,
            "tx_uid": self._data.version.tx_uid,
            "resource_cd": self._data.resource_cd,
            "api_version": getattr(self._data, 'api_version', None),
            "processed_dttm": datetime.now().isoformat(),
            "state": json.loads(dumps('json', self._data).decode('utf-8')),
        }
        return self._write

    def lock(self):
        self._write['data'] = {
            "version_id": self._data.version_id,
            "resource_cd": self._data.resource_cd,
            "status": self._data.status,
            "lock_as_of_dttm": self._data.lock_dttm,
            "processed_dttm": datetime.now().isoformat(),
        }
        return self._write

    def upsert_delta_manager_metric(self):
        name = "srv_delta__upsert_duration"
        result = {
            "content_type": "upsert_delta_manager_metric",
            "name": name,
            "help": "",
            "type_id": 1,
            "type_name": "gauge",
            "value": self._data["value"],
            "labels": {
                "value": {
                    "srv__appname": self._conf.IDENT,
                    "srv__hostname": self._host,
                    "srv__environment": self._conf.ENVIRONMENT,
                    "srv__instance": self._conf.INSTANCE,
                    "tx_uid": self._data["version"].tx_uid,
                    "version_id": self._data["version"].version_id,
                },
            },
            "increment": 0,
            "timestamp": datetime.now().timestamp(),
        }
        key_val_labels = [
            f'{key}:{val}' for key, val in
            sorted(result["labels"]["value"].items(), key=lambda x: x[0])
            if key.startswith("srv__")
        ]
        key_val_lbl_str = '||'.join([str(val) for val in key_val_labels])
        result["labels"]["hash"] = str(UUID(get_md5_hash(key_val_lbl_str)))
        name_lbl_str = f'{name}#{key_val_lbl_str}'
        result["hash"] = str(UUID(get_md5_hash(name_lbl_str)))
        return result

    def datafix_journal(self):
        def get_job_run_uid(
                job_id: str,
                run_id: str,
                start_dttm: datetime,
                instance: str,
        ) -> Optional[str]:
            if not all([job_id, run_id, start_dttm, instance]):
                return None
            dt = start_dttm.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S.%fZ')
            hash_uid = get_md5_hash(f'{job_id}_{run_id}_{dt}_{instance}')
            return str(UUID(hash_uid))

        job_run_uid = get_job_run_uid(
            job_id=self._data['job_id'],
            run_id=self._data['run_id'],
            start_dttm=self._data['start_dttm'],
            instance=self._instance,
        )
        labels = {
            "srv__appname": self._conf.IDENT,
            "srv__hostname": self._host,
            "srv__environment": self._conf.ENVIRONMENT,
            "srv__instance": self._conf.INSTANCE,
        }
        sorted_labels = [
            f'{val}' for key, val in
            sorted(labels.items(), key=lambda x: x[0])
        ]
        service_uid_str = '#'.join([str(val) for val in sorted_labels])
        service_uid = str(UUID(get_md5_hash(service_uid_str)))
        self._write['data'] = {
            "event_dttm": self._data['event_dttm'],
            "job_id": self._data['job_id'],
            "run_id": self._data['run_id'],
            "service_uid": service_uid,
            "version_id": self._data['version_id'],
            "datafix_version_id": self._data['datafix_version_id'],
            "resource_cd": self._data['resource_cd'],
            "table_name": self._data['table_name'],
            "task_code": self._data['task_code'],
            "query_expression": self._data['query_expression'],
            "config_json": self._data['config_json'],
            "status_name": self._data['status_name'],
            "job_run_uid": job_run_uid,
        }
        return self._write
