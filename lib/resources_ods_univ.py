import getopt
import json
import sys
import urllib.request

from dwh_services_utils.clients import UniProviderClient

# client = UniProviderClient(
#     # 'http://zi7-uni.dtpl.corp.dev.vtb/',
#     # 'http://gilev-uni.dtpl.corp.dev.vtb/'
#    'http://0.0.0.0:8807/',
# )

def create_univ_from_ods(ods_data, pxf_data, odbc_data, schema="DEV_ODS_META_SOH"):

    univ_data = {"resource_cd": ods_data["resource_cd"], "resource_desc": ods_data["resource_desc"],
                 "tags": ods_data["tags"], "features": ods_data["features"],
                 "status": {"is_readonly": ods_data["is_readonly"],
                            "is_maintenance": False, "is_deleted": False}, "connections": dict(), "metrics": dict()}

    key_odbc = list(odbc_data.keys())[0]
    univ_data["connections"].update(odbc_data)
    univ_data["connections"].update(pxf_data)

    univ_data["datasets"] = [{'name': ods_data["resource_cd"].split('.')[2], "schema_name": ods_data["resource_cd"].split('.')[1], "filter": "", "physical_options": []}]

    univ_data['metrics']['is_locked'] = {'id': ods_data["resource_cd"] + '.is_locked', 'connection': key_odbc,
                                         'query': "SELECT false from dual", 'default': False, 'refresh': "PDT23H00M"}

    univ_data['metrics']['locked_by'] ={'id': ods_data["resource_cd"] + '.locked_by', 'connection': key_odbc,
                                         'query': "SELECT -1 from dual", 'default': -1, 'refresh': "PDT23H00M"}

    univ_data['metrics']['ods_wf_max_date_to'] ={ 'id': ods_data["resource_cd"] + ".ods_wf_max_date_to",
                                                'connection': key_odbc,

                                    'query': "select case when max(omls.DATE_TO) < TO_DATE('19000201', 'YYYYMMDD') then TO_DATE('19000201', 'YYYYMMDD') " \
                                                          " else max(omls.DATE_TO) end as ods_wf_max_date_to from " \
                                                          "{}.ODS_META_TABLE omt inner join " \
                                                          "{}.ODS_META_LAYER_RELATION omlr on " \
                                                          "omt.TABLE_ID = omlr.DEST_TABLE_ID left join " \
                                                          "{}.ODS_META_LAYER_STAT omls on omls.LAYER_REL_ID = omlr.LAYER_REL_ID " \
                                                          "where omt.SCHEMA_NAME = ? and omt.TYPE_NAME = ? " \
                                                          "and omt.TABLE_NAME = ? group by omt.TABLE_NAME"
                                        .format(schema, schema, schema),
                                    'query_parameters':  list()}
    univ_data['metrics']['ods_wf_max_date_to']['query_parameters'].append(
        {"name": "SCHEMA_NAME", "value": ods_data["resource_cd"].split('.')[1], "sqltype": "varchar"})
    univ_data['metrics']['ods_wf_max_date_to']['query_parameters'].append(
        {"name": "TYPE_NAME", "value": "SOH", "sqltype": "varchar"})
    univ_data['metrics']['ods_wf_max_date_to']['query_parameters'].append(
        {"name": "TABLE_NAME", "value": ods_data["resource_cd"].split('.')[2], "sqltype": "varchar"})

    univ_data['metrics']['ods_wf_max_date_to']['default'] = "1899-01-01"
    univ_data['metrics']['ods_wf_max_date_to']['refresh'] = "PDT23H00M"

    return univ_data


def load_univ_from_ods(client, ods_data, pxf_data, odbc_data, schema="ODS_META"):

    univ_resources = list()

    for ods_d in ods_data:
        univ_data = create_univ_from_ods(ods_d, pxf_data, odbc_data, schema)
        try:
            univ_resources.append(client.create_resource(univ_data))
        except Exception as error:
            print(error)
    return univ_resources


if __name__ == '__main__':
    args = sys.argv[1:]

    if len(args) <= 0:
        print("set parameter url uniprovider: ")
        sys.exit(0)

    client = UniProviderClient(
        # 'http://zi7-uni.dtpl.corp.dev.vtb/',
        # 'http://gilev-uni.dtpl.corp.dev.vtb/'
        args[0],
    )

    pxf = "conf/pxf.json"
    odbc = "conf/odbc.json"
    schema = "DEV_ODS_META_SOH"
    src_json = 'conf/res_for_conv.json'

    # url_ods = "http://0.0.0.0:8803/api/0.3/resources"
    # with urllib.request.urlopen(url_ods) as url:

    # with open(ods) as ods_file:
    #     ods_data = json.load(ods_file)
    with open(pxf) as pxf_file:
        pxf_data = json.load(pxf_file)

    with open(src_json) as src_file:
        ods_data = json.load(src_file)

    with open(odbc) as odbc_file:
        odbc_data = json.load(odbc_file)

    univ_resources = load_univ_from_ods(client, ods_data, pxf_data, odbc_data, schema)

    for res in univ_resources:
        print(res)
        # states = client.get_resource_state(resource_cd=res.resource_cd)
        # print(states)
        ##client.delete_resource(resource_cd=res.resource_cd)

