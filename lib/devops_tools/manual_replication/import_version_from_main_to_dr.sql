CREATE OR REPLACE FUNCTION etl.import_version_from_main_to_dr2(p_version_id integer)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$


-- 20230622

DECLARE
            version_row record;
            ext_version_row record;
            version_history_row record;
            complex_field_row record;
            delta_name text;
            delta_exists int2;
            delta_dr_exists int2;
            version_gap_exists int2;
            target_name text;
            row_cnt int8;
BEGIN
            -- просмотр etl.version на мейне
            SELECT *
            INTO ext_version_row
            FROM etl.ext_gp_etl_version_r v
            WHERE v.version_id = p_version_id;

            -- если active_flg true return 0
            IF ext_version_row.active_flg IS NOT FALSE
            THEN
                RAISE NOTICE 'Версия % на MAIN активна (в обработке) или отсутсвует', p_version_id;
                RETURN 1;
            END IF;

            -- просмотр etl.version на dr
            SELECT *
            INTO version_row
            FROM etl.version v
            WHERE v.version_id=p_version_id;

            -- если active_flg false return 2
            IF version_row.active_flg IS false and version_row.processed_flg IS true and version_row.finish_dttm is not null
            THEN
                RAISE NOTICE 'Версия % на DR уже применена ', p_version_id;
                RETURN 2;
            END IF;
            -- если active_flg false и commited_flg false - upsert etl.version
            IF ext_version_row.active_flg IS FALSE AND ext_version_row.commited_flg IS FALSE
            THEN
                PERFORM etl.upsert_row(p_version_id, ext_version_row.tx_uid::uuid, ext_version_row.dag_id, ext_version_row.start_dttm, now()::timestamp,
                ext_version_row.active_flg, ext_version_row.commited_flg, ext_version_row.undo_flg, ext_version_row.processed_flg);
                RETURN 0;
            END IF;

            -- цикл по etl.version_history если active_flg false и commites_flg true
            FOR version_history_row IN
                        SELECT
                            version::jsonb,
                            jsonb_array_elements(version::jsonb->'operations') as operation
                        FROM etl.ext_gp_etl_version_history_r h
                        WHERE h.version_id=p_version_id
            LOOP
                        -- получение имен дельта таблицы и таргет таблицы
                        SELECT etl.target_table_from_query(version_history_row.operation::json->>'statement') INTO target_name;
                        SELECT etl.delta_table_from_query(version_history_row.operation::json->>'statement') INTO delta_name;
                        delta_name = split_part(delta_name, '.', 1) || '.' || substring(split_part(delta_name, '.', 2) from 0 for 64);

						-- не удалось получить названия таблиц
				        IF delta_name is null or target_name is null
				        THEN
				            RAISE NOTICE 'Не удалось получить названия таблиц target:%, delta:%', target_name, delta_name;
				            RETURN 3;
				        END IF;

				        -- проверка блокировки наката версии
						SELECT 1 AS t FROM etl.repl_journal rj WHERE rj.version_id < p_version_id AND target_name = ANY(rj.err_tables) AND rj.status IN (0,1) LIMIT 1 into version_gap_exists;
			            IF version_gap_exists = 1
			            then
			            	RAISE NOTICE 'Версия % заблокирована для наката. Таблица % ', p_version_id, target_name;
			                RETURN 4;
			            end if;

                        -- проверяем наличие дельта-таблицы на DR
                        SELECT 1 as t FROM etl.v_pg_catalog_columns WHERE nspname = split_part(delta_name, '.', 1) AND relname = split_part(delta_name, '.', 2) LIMIT 1 INTO delta_dr_exists;
                        -- SELECT 1 AS t FROM pg_tables WHERE schemaname = split_part(delta_name, '.', 1) AND tablename = split_part(delta_name, '.', 2) INTO delta_dr_exists;
                        raise notice '1 target=%,  delta=% delta_exists=% delta_dr_exists=%', target_name, delta_name, delta_exists, delta_dr_exists;
                        IF delta_dr_exists = 1
                        THEN
                        		EXECUTE version_history_row.operation::json->>'statement';
                                GET DIAGNOSTICS row_cnt = ROW_COUNT;
                                -- insert в etl.version_counts
                                INSERT INTO etl.version_counts(version_id, table_name, ver_count, event_time) values (p_version_id, target_name, row_cnt, timezone('utc', now())::timestamp );
                        END IF;

                        -- проверяем наличие дельта-таблицы на мейне - если нет локальной
                        SELECT 1 AS t FROM etl.ext_pg_catalog_columns WHERE nspname=split_part(delta_name, '.', 1) AND relname=split_part(delta_name, '.', 2) LIMIT 1 INTO delta_exists;
                        raise notice '2 target=%,  delta=% delta_exists=% delta_dr_exists=%', target_name, delta_name, delta_exists, delta_dr_exists;

                        -- если не найдены дельты ни на одном контуре - то выходим с ошибкой return 2
				        IF delta_exists is null and delta_dr_exists is null
				        THEN
				            RAISE NOTICE 'Дельта таблица не найдена ни на одном контуре %', delta_name;
				            RETURN 3;
				        END IF;

                        IF delta_exists = 1 and delta_dr_exists IS NULL
                        THEN
                           -- создание внешней таблицы
                           PERFORM etl.create_external_table_for_import_from_gp(split_part(delta_name, '.', 1), split_part(delta_name, '.', 2));
                           -- импорт из внешней таблицы в локальную дельта-таблицу
                           EXECUTE 'CREATE TABLE ' || delta_name || ' AS SELECT * FROM '|| split_part(delta_name, '.', 1) || '.ext_gp_' || replace(delta_name, '.', '_') || '_r';

		                   	FOR complex_field_row IN
						    SELECT
						        attname, format_type
						    FROM
						        public.replication_tables
						    WHERE
						        nspname = split_part(delta_name, '.', 1)
						        AND relname = split_part(delta_name, '.', 2)
						        AND format_type IN ('json', 'jsonb', 'uuid', 'time with time zone', 'time without time zone', 'timestamp(0) with time zone', 'timestamp with time zone')
						    LOOP
					        	EXECUTE 'ALTER TABLE ' || delta_name || ' ALTER COLUMN ' || complex_field_row.attname || ' TYPE ' || complex_field_row.format_type || ' USING ' || complex_field_row.attname || '::' || complex_field_row.format_type;
					        END LOOP;

                           -- исполнение запроса statement
                           EXECUTE version_history_row.operation::json->>'statement';
                           GET DIAGNOSTICS row_cnt = ROW_COUNT;
                           -- insert в etl.version_counts
                           INSERT INTO etl.version_counts(version_id, table_name, ver_count, event_time) values (p_version_id, target_name, row_cnt, timezone('utc', now())::timestamp );
                           -- удаление дельта-таблицы в внешней таблицы
                           EXECUTE 'DROP TABLE ' || delta_name;
                           EXECUTE 'DROP EXTERNAL TABLE ' || split_part(delta_name, '.', 1) || '.ext_gp_' || replace(delta_name, '.', '_') || '_r';
                        END IF;
                        raise notice '3 target=%,  delta=% delta_exists=% delta_dr_exists=%', target_name, delta_name, delta_exists, delta_dr_exists;
            END LOOP;

            -- insert в etl.version_history
            INSERT INTO etl.version_history (version_id,processed_dttm,version) VALUES (p_version_id, now()::timestamp -  interval '3 hour', version_history_row.version);
            -- upsert etl.version
            PERFORM etl.upsert_row(p_version_id, ext_version_row.tx_uid::uuid, ext_version_row.dag_id, ext_version_row.start_dttm, now()::timestamp - interval '3 hour' ,
                ext_version_row.active_flg, ext_version_row.commited_flg, ext_version_row.undo_flg, ext_version_row.processed_flg);
            RETURN 0;
END



$function$
;
