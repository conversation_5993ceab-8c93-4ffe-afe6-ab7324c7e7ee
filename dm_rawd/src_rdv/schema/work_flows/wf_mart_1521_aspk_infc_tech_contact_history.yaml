name: wf_mart_1521_aspk_infc_tech_contact_history
type: WORK_FLOW
schema_version: '1.13'
version: 1
tags:
  - wf
  - wf_mart_1521_aspk_infc_tech_contact_history
  - 'tech_contact_history'
  - v.2
  - rd44
  - <PERSON><PERSON> (4067775)
orientation: TB

sources:
  - short_name: tech_contact_history
    type: DB_TABLE
    resource_cd: aspk.aspk_infc.tech_contact_history
    object: tech_contact_history
targets:
  - short_name: mart
    schema: rd_rawd
    table: mart_1521_aspk_infc_tech_contact_history
    resource_cd: ceh.rd_rawd.mart_1521_aspk_infc_tech_contact_history
local_metrics:
  wf_max_date_to:
    target: stage_T_input
    query: |
      coalesce(
        max(prs_processed_dttm), (SELECT max(prs_processed_dttm)::timestamp as metric from rd_rawd.mart_1521_aspk_infc_tech_contact_history),
        '1900-01-01 00:00:00' :: TIMESTAMP)
mappings:
  marts:
    - short_name: mart
      algorithm_uid: 'mart_1521_aspk_infc_tech_contact_history'
      algorithm_uid_2: '1'
      target: mart
      source: tech_contact_history
      delta_mode: 'new'
      where_clause: |
         prs_processed_dttm::timestamp  > '${conf.algos["mart_1521_aspk_infc_tech_contact_history"].by_src["aspk.aspk_infc.tech_contact_history"].wf_max_date_from}'::timestamp
         and
         prs_processed_dttm::timestamp <= '${conf.algos["mart_1521_aspk_infc_tech_contact_history"].by_src["aspk.aspk_infc.tech_contact_history"].wf_max_date_to}'::timestamp
      metrics:
        by_src:
          - wf_max_date_to

      field_map: 
        contact_id:
          type: column
          value: contact_id
        value_dttm:
          type: column
          value: value_dttm
        contact_status_cd:
          type: column
          value: contact_status_cd
        processed_dttm:
          type: column
          value: processed_dttm
        prs_processed_dttm:
          type: column
          value: prs_processed_dttm
        deleted_flg_from_aspk:
          type: column
          value: deleted_flg 
        deleted_flg:
          type: literal
          value: false           


      ref_map: []

      hub_map: []