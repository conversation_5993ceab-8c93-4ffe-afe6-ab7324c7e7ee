name: wf_raw_data_mrod_mref_kis_collateral_type
type: WORK_FLOW
schema_version: '1.11'
version: 1
tags:
  - get_raw_data
orientation: TB
sources:
  - short_name: fct_i_p
    type: DB_TABLE
    resource_cd: tseh.public.mrod_mref_kis_collateral_type
    object: mrod_get_raw_data
targets:
  - short_name: mart_fct_i_p
    schema: rd_rawd
    table: mart_mrod_get_raw_data
    resource_cd: ceh.rd_rawd.mart_mrod_mref_kis_collateral_type
local_metrics:
  wf_max_date_to:
    target: stage_T_input
    query: |
      coalesce(
        max(upload_date),
        '1999-12-12' :: TIMESTAMP
      )    
mappings:
  marts:
    - short_name: mart_fct_i_p
      algorithm_uid: 'mrod_mref_kis_collateral_type'
      algorithm_uid_2: '1'
      target: mart_fct_i_p
      source: fct_i_p
      delta_mode: new
      where_clause: | 
        upload_date > '${conf.algos["mrod_mref_kis_collateral_type"].by_src["tseh.public.mrod_mref_kis_collateral_type"].wf_max_date_from}'
        and
        upload_date <= '${conf.algos["mrod_mref_kis_collateral_type"].by_src["tseh.public.mrod_mref_kis_collateral_type"].wf_max_date_to}'
        and 
        lower(table_name)='mref_kis_collateral_type'
      field_map:
        file_name:
          type: column
          value: file_name
        row_num:
          type: column
          value: row_num
        row_data:
          type: column
          value: row_data
        upload_date:
          type: column
          value: upload_date
        table_name:
          type: column
          value: table_name
        uniq_num:
          type: column
          value: uniq_num
        deleted_flg:
          type: sql_expression
          value: >
            case when json_extract_path(lower(row_data)::json, 'upload_mode')::text='"delete"' then true else false end
          field_type: BOOLEAN
      metrics:
        by_src:
          - wf_max_date_to
      ref_map: []
      hub_map: []