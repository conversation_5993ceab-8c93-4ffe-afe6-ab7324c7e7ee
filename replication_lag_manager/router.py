from fastapi import APIRouter
from fastapi.openapi.docs import (
    get_swagger_ui_html,
)


from replication_lag_manager.config import conf
from replication_lag_manager.endpoints import dags

api_router = APIRouter()
api_router.include_router(dags.router, prefix='/api/1.0/dags', tags=['dags'])


@api_router.get('/health')
async def health():
    return {'status': 'ok'}


@api_router.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url="/api/1.0/openapi.json",
        title="Reload Manager",
        swagger_js_url="/lib/static/swagger-ui-bundle.js",
        swagger_css_url="/lib/static/swagger-ui.css",
        init_oauth={
            "clientId": conf.OAUTH_CLIENT_ID,
            "clientSecret": conf.OAUTH_CLIENT_SECRET,
        },
    )
