from abc import ABC
from typing import Any
import faust
from prometheus_client import generate_latest, REGISTRY
import lib.kafka.models.uni_models as models
from lib.logging.utils import get_logger


logger = get_logger()


class CommandException(Exception):
    pass


class ClassNotFoundException(CommandException):
    pass


class CommandParams(faust.Record, ABC, abstract=True):
    """Параметры команды (добавления, удаления ...),
       которые передаются агентам через топик.
    """


class AppendParams(CommandParams, ABC):
    """Параметры добавления ключа."""
    table: str
    key: str
    value: Any


class AppendOrUpdateParams(AppendParams, ABC):
    """Параметры добавления или обновления ключа."""


class UpdateParams(AppendParams, ABC):
    """Параметры обновления ключа."""


class GetMetricsParams(CommandParams, ABC):
    """ Параметры получения метрик мониторинга """
    table: str = ''


class GetParams(CommandParams, ABC):
    """Параметры получения ключа."""
    table: str
    key: str


class DeleteParams(CommandParams, ABC):
    """Параметры удаления ключа."""
    table: str
    key: str


class GetTableParams(CommandParams, ABC):
    """Параметры получения таблицы."""
    table: str
    limit: int
    offset: int


class GetKeysParams(CommandParams, ABC):
    """Запрос всех ключей фауст таблицы"""
    table: str


class DictParams(CommandParams, ABC):
    """Параметры работы с словарями"""
    dict_key: str
    table: str
    key: str
    value: Any
    dict_class: str


class AppendDictParams(DictParams, ABC):
    """Параметры добавления в словарь"""


class UpdateDictParams(DictParams, ABC):
    """Параметры обновления в словаре"""


class AppendOrUpdateDictParams(DictParams):
    """Параметры обновления в словаре"""


class PopDictParams(DictParams, ABC):
    """Параметры исключения из словаре"""


class GetByFieldParams(CommandParams, ABC):
    """Параметры получения значения по полю, а не ключу."""
    table: str
    field: str
    value: Any


class SetResourceParams(CommandParams, ABC):
    """Параметры установки ресурса"""
    resource_cd: str
    value: Any


class GetResourceStateParams(CommandParams, ABC):
    """Параметры получения состояния ресурса"""
    resource_cd: str


class GetTableItemsParams(CommandParams, ABC):
    """Параметры получения таблицы."""
    table: str


class GetValidationErrorsParams(CommandParams, ABC):
    value: Any


class Command:
    """ Все операции над ключом в таблице реализованы в виде команд.
        В агент передаются параметры команды (экземпляр CommandParams), в зависимости от параметров
        создается экземпляр Command и вызывается метод __call__ с этими параметрами.
    """
    def __init__(self, key: str = None, table: faust.Table = None):
        self.key = key
        self.table = table

    def __call__(self, command_params: CommandParams):
        raise NotImplementedError

    def _get_faust_table(self, command_params):
        if self.table is not None:
            return self.table
        from uni_provider.faust_worker.tables import FAUST_TABLES
        logger.debug(f'Command_params: {command_params}')
        faust_table = FAUST_TABLES[command_params.table]
        logger.debug(f'faust_table: {faust_table}')
        return faust_table

    def _prepare_queue_command(self, command_params):
        faust_table = self._get_faust_table(command_params)
        dict_class = getattr(models, command_params.dict_class, None)
        if not dict_class:
            logger.error(f'Не найден класс словаря {command_params.dict_class}')
            raise ClassNotFoundException(f'Не найден класс словаря {command_params.dict_class}')
        return faust_table, dict_class


class AsyncCommand:
    """ Все операции над ключом в таблице реализованы в виде команд (сейчас это только инкремент и удаление).
        В агент передаются параметры команды (экземпляр CommandParams), в зависимости от параметров
        создается экземпляр Command и вызывается метод __call__ с этими параметрами.
    """
    def __init__(self, key: str = None, table: faust.Table = None):
        self.key = key
        self.table = table

    async def async_call(self, command_params: CommandParams):
        raise NotImplementedError


class AppendDict(Command):
    """Добавление или обновление в словаре."""
    def __call__(self, command_params: AppendDictParams):
        faust_table, dict_class = self._prepare_queue_command(command_params)
        try:
            logger.info(f'Добавление в словарь: {command_params.value}')
            queue = faust_table[command_params.dict_key]
            logger.info(f'Словарь: {queue}')
        except KeyError:
            logger.info(f'Первое значение - Добавление в словарь: {command_params.value}')
            faust_table[command_params.dict_key] = dict_class(
                command_params.dict_key,
                {command_params.key: command_params.value})
        else:
            if command_params.key in queue.queue.keys():
                logger.warning(
                    f'Значение уже существует: {command_params.value}. '
                    f'Для обновления используйте UpdateQueue или AppendOrUpdateQueue'
                )
                return
            else:
                logger.info(f'Не первое значение - Добавление в словарь: {command_params.value}')
                queue.queue[command_params.key] = command_params.value
                faust_table[command_params.dict_key] = queue
        logger.info(f'Словарь результат: {faust_table[command_params.dict_key]}')


class UpdateDict(Command):
    """Добавление или обновление в словаре."""
    def __call__(self, command_params: UpdateDictParams):
        faust_table, dict_class = self._prepare_queue_command(command_params)
        try:
            logger.info(f'Обновление в словаре: {command_params.value}')
            queue = faust_table[command_params.dict_key]
            logger.info(f'Словарь: {queue}')
        except KeyError:
            logger.warning(f'Словарь {command_params.dict_class} не инициализирован')
            return
        else:
            if command_params.key in queue.queue.keys():
                logger.info(f'Ключ найден - обновление словаря: {command_params.value}')
                queue.queue[command_params.key] = command_params.value
                faust_table[command_params.dict_key] = queue
        logger.info(f'Словарь результат: {faust_table[command_params.dict_key]}')


class AppendOrUpdateDict(Command):
    """Добавление или обновление в словаре."""
    def __call__(self, command_params: UpdateDictParams):
        faust_table, dict_class = self._prepare_queue_command(command_params)
        try:
            logger.info(f'Добавление в словарь: {command_params.value}')
            queue = faust_table[command_params.dict_key]
            logger.info(f'Словарь: {queue}')
        except KeyError:
            logger.info(f'Первое значение - Добавление в словарь: {command_params.value}')
            faust_table[command_params.dict_key] = dict_class(
                command_params.dict_key,
                {command_params.key: command_params.value})
        else:
            if command_params.key in queue.queue.keys():
                logger.info(f'Ключ найден - обновление словаря: {command_params.value}')
            else:
                logger.info(f'Не первое значение - добавление в словарь: {command_params.value}')
            queue.queue[command_params.key] = command_params.value
            faust_table[command_params.dict_key] = queue
        logger.info(f'Словарь результат: {faust_table[command_params.dict_key]}')


class PopDict(Command):
    """Удаление из словаря."""
    def __call__(self, command_params: PopDictParams):
        faust_table, dict_class = self._prepare_queue_command(command_params)
        try:
            logger.info(f'Очистка из словаря ключа: {command_params.key}')
            queue = faust_table[command_params.dict_key]
            logger.info(f'Словарь: {queue}')
        except KeyError:
            logger.warning(f'Словарь {command_params.dict_class} не инициализирован')
            return
        else:
            if command_params.key not in queue.queue.keys():
                logger.warning(f'Не найден ключ: {command_params.key}')
            else:
                logger.info(f'Очистка ключа: {command_params.key}')
                queue.queue.pop(command_params.key, None)
                faust_table[command_params.dict_key] = queue
        logger.info(f'Словарь результат: {faust_table[command_params.dict_key]}')


class Append(Command):
    """Добавление."""
    def __call__(self, command_params: AppendParams):
        faust_table = self._get_faust_table(command_params)
        try:
            logger.info(f'Получение ключа: {command_params.key}')
            res = faust_table[command_params.key]
        except KeyError:
            logger.info(f'Ключ {command_params.key} не найден - добавление')
            faust_table[command_params.key] = command_params.value
            return faust_table[command_params.key]
        else:
            logger.warning(f'Ключ уже существует: {command_params.key}')
            return None


class AppendOrUpdate(Command):
    """Добавление."""
    def __call__(self, command_params: AppendOrUpdateParams):
        faust_table = self._get_faust_table(command_params)
        try:
            logger.info(f'Получение ключа: {command_params.key}')
            # res = faust_table[command_params.key]
            _ = faust_table[command_params.key]
        except KeyError:
            logger.info(f'Ключ {command_params.key} не найден - добавление')
            faust_table[command_params.key] = command_params.value
            return faust_table[command_params.key]
        else:
            logger.info(f'Ключ {command_params.key} найден - обновление: {command_params.value}')
            faust_table[command_params.key] = command_params.value
            return faust_table[command_params.key]


class GetKeys(Command):
    """Получение ключей таблицы."""
    def __call__(self, command_params: GetKeysParams):
        logger.info(f'Запрос ключей таблицы {command_params.table}')
        table = self._get_faust_table(command_params)
        if not table:
            return []
        return list(table.keys())


class Update(Command):
    """Обновление по ключу."""
    def __call__(self, command_params: UpdateParams):
        faust_table = self._get_faust_table(command_params)
        try:
            logger.info(f'Получение ключа: {command_params.key}')
            res = faust_table[command_params.key]
        except KeyError:
            logger.warning(f'Ключ {command_params.key} не найден')
            return None
        else:
            logger.info(f'Ключ {command_params.key} найден - обновление: {command_params.value}')
            faust_table[command_params.key] = command_params.value
            return faust_table[command_params.key]


class Get(Command):
    """Получение по ключу."""
    def __call__(self, command_params: GetParams):
        faust_table = self._get_faust_table(command_params)
        try:
            logger.info(f'Получение ключа: {command_params.key}')
            return faust_table[command_params.key]
        except KeyError:
            logger.info(f'Ключ {command_params.key} не найден')
            return None


class Delete(Command):
    """Удаление."""
    def __call__(self, command_params: DeleteParams):
        faust_table = self._get_faust_table(command_params)
        try:
            logger.info(f'Удаление ключа: {command_params.key}')
            del faust_table[command_params.key]
            return command_params.key
        except KeyError:
            logger.info(f'Ключ {command_params.key} не найден')
            return None


class GetTable(Command):
    """Получение таблицы."""
    def __call__(self, command_params: GetParams):
        faust_table = self._get_faust_table(command_params)
        faust_table_keys_all = list(faust_table.keys())

        left_bound = 0
        offset = 0
        for key in faust_table_keys_all:
            if offset >= command_params.offset:
                break
            left_bound += 1
            if not faust_table[key].status or not faust_table[key].status.is_deleted:
                offset += 1

        right_bound = left_bound
        limit = 0
        for key in faust_table_keys_all[left_bound:]:
            if limit >= command_params.limit:
                break
            right_bound += 1
            if not faust_table[key].status or not faust_table[key].status.is_deleted:
                limit += 1

        res = []
        for key in faust_table_keys_all[left_bound:right_bound]:
            if not faust_table[key].status or not faust_table[key].status.is_deleted:
                res.append(faust_table[key])
        return res


class GetByField(Command):
    """Получение значения по полю"""
    def __call__(self, command_params: GetByFieldParams):
        faust_table = self._get_faust_table(command_params)
        logger.info(f'Получение значения по полю: {command_params.field} = {command_params.value}')
        for res in (r for r in faust_table.values() if getattr(r, command_params.field, None) == command_params.value):
            logger.info(f'Получение значения по полю: {command_params.field}, значение: {res}')
            return res
        return None


class GetResourceState(AsyncCommand):
    """Асинхронная установка состояния ресурса"""
    async def async_call(self, command_params: GetResourceStateParams):
        from uni_provider.faust_worker import core
        return await core.get_resource_state(command_params.resource_cd)


class SetResource(AsyncCommand):
    """Асинхронное создание/обновление ресурса"""
    async def async_call(self, command_params: SetResourceParams):
        from uni_provider.faust_worker import core
        return await core.set_resource(command_params.resource_cd, command_params.value)


class GetValidationErrors(AsyncCommand):
    async def async_call(self, command_params: SetResourceParams):
        from uni_provider.faust_worker import core
        return await core.get_validation_errors(command_params.value)


class GetTableItems(Command):
    """Получение таблицы."""
    def __call__(self, command_params: GetTableItemsParams):
        faust_table = self._get_faust_table(command_params)
        res = [item for item in faust_table.items()]
        logger.info(f'Получение строк таблицы: {command_params.table}')
        return res


class GetMetrics(Command):
    """ """

    def __call__(self, params: Get):
        # return generate_latest(REGISTRY).decode('utf-8')
        return ''


COMMAND_PARAMS_HANDLERS = {
    AppendParams: Append,
    AppendOrUpdateParams: AppendOrUpdate,
    GetParams: Get,
    GetByFieldParams: GetByField,
    UpdateParams: Update,
    GetTableParams: GetTable,
    AppendDictParams: AppendDict,
    UpdateDictParams: UpdateDict,
    AppendOrUpdateDictParams: AppendOrUpdateDict,
    PopDictParams: PopDict,
    DeleteParams: Delete,
    GetResourceStateParams: GetResourceState,
    SetResourceParams: SetResource,
    GetValidationErrorsParams: GetValidationErrors,
    GetTableItemsParams: GetTableItems,
    GetMetricsParams: GetMetrics,
    GetKeysParams: GetKeys,
}
