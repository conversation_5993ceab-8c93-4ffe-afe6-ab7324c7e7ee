import importlib
from typing import Union
from fastapi.responses import JSONResponse
from fastapi import HTTPException
from lib.consts import ServiceMode, ServiceParam
from lib.kafka.models.uni_models import ResourceUniFull


async def check_resource(resource_cd) -> Union[ResourceUniFull, JSONResponse]:
    """Проверка состояния ресурса"""
    core = importlib.import_module('uni_provider.faust_worker.core')
    resource = await core.get_resource_command(resource_cd) # noqa
    if not resource:
        return JSONResponse(
            status_code=404,
            content={'message': 'Ресурс не зарегистрирован'}
        )
    if hasattr(resource, 'status') and hasattr(resource.status, 'is_deleted') and resource.status.is_deleted:
        return JSONResponse(
            status_code=410,
            content={'message': 'Ресурс удален'}
        )
    return resource


async def check_service_mode(required: ServiceMode = ServiceMode.ONLINE):
    """ Проверка режима работы сервиса """

    core = importlib.import_module('uni_provider.faust_worker.core')
    service_mode = await core.get_command('_settings_t', ServiceParam.SERVICE_MODE.value)

    if not service_mode:
        await core.add_command_single('_settings_t', ServiceParam.SERVICE_MODE.value, ServiceMode.ONLINE.value)  # noqa
    else:
        if service_mode != required.value:
            raise HTTPException(
                status_code=503,
                detail=f'Система в режиме {service_mode} - функция недоступна',
            )


def get_test_resource():
    return {
        "resource_cd": "service.uni.test_resource",
        "resource_desc": "Тестовый ресурс для проверки работоспособности сервисов. Проверяет через метрику к ADB",
        "tags": [
            "test",
            "services"
        ],
        "features": {},
        "status": {
            "is_readonly": True,
            "is_maintenance": True,
            "is_deleted": False
        },
        "connections": "{{ jdbc_conn | for_instance('ods_cft2main_soh') }}",  # тут может быть любой jdbc_conn
        "datasets": [],
        "metrics": {
            "test_metric": {
                "id": f'service.uni.test_resource.test_metric',
                "connection": "arena_conn",
                "query": "SELECT 1;",
                "query_parameters": None,
                "default": "-1",
                "refresh": "PDT00H01M"
            }
        }
    }
