import datetime
import json
import uuid
from typing import Any, List

from fastapi import APIRouter, BackgroundTasks, status, Depends
from fastapi.responses import JSONResponse
from fastapi.exceptions import HTTPException
from pydantic import UUID4
from dwh_services_utils.schemas import tx_manager as schemas

from lib.auth.utils import required
from lib.kafka.models.ceh_models import FaustTransaction
from lib.logging.custom_sink import create_log_message
from lib.logging.utils import get_logger
from tx_manager import app
from tx_manager.api.custom_api_route import CustomAPIRoute
from tx_manager.api.endpoints.service import check_service_mode
from tx_manager.api.endpoints.utils import merge_rollback_reasons
from tx_manager.core.models import Phase, Transaction, TransactionParty
from tx_manager.config import conf
from tx_manager.exception import PartyAlreadyAdded


logger = get_logger()
router = APIRouter(route_class=CustomAPIRoute)


@router.get(
    "",
    summary="Получить список активных Транзакций.",
    response_model=List[schemas.Transaction],
    dependencies=[Depends(required(roles=['read_role']))]
)
async def get_transactions() -> Any:
    """Список активных транзакций."""
    return await app.get_transactions()


@router.post(
    "",
    summary="Создать новую Транзакцию.",
    response_model=schemas.TransactionWithToken,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(required(roles=['modify_role']))]
)
async def create_transaction(
        tx_data: schemas.TransactionCreate,
        background_tasks: BackgroundTasks
) -> Any:
    """Создать новую Транзакцию."""

    # Проверяем что сервис работает в штатном режиме
    await check_service_mode()

    tx, tx_fsm = await app.add_transaction(
        FaustTransaction(
            tx_uid=uuid.uuid4().hex,
            tx_token=uuid.uuid4().hex,
            commit_timeout=(tx_data.commit_timeout.total_seconds() if tx_data.commit_timeout else None)
                           or conf.DEFAULT_COMMIT_TIMEOUT,
            start_dttm=datetime.datetime.utcnow(),
            parties=[],
            stage_no=0
        )
    )
    background_tasks.add_task(tx_fsm.run)

    message = create_log_message("Transaction created, tx_uid={}".format(tx.tx_uid), tx_uid=tx.tx_uid)
    logger.info(message)
    # logger.info('Transaction created, tx_uid={}', tx.tx_uid)

    return tx


@router.get(
    "/{tx_uid}",
    summary="Получить информацию о Транзакции.",
    response_model=schemas.Transaction,
    dependencies=[Depends(required(roles=['read_role']))]
)
async def get_transaction(tx_uid: UUID4) -> Any:
    """Получить информацию о Транзакции."""
    try:
        tx = await app.get_transaction(tx_uid.hex)
        rollback_reasons = await merge_rollback_reasons(tx)
        tx.rollback_reasons = rollback_reasons
        return tx
    except KeyError:
        return JSONResponse(status_code=status.HTTP_404_NOT_FOUND)


@router.put(
    "/{tx_uid}",
    summary="Изменить таймаут Транзакции.",
    response_model=schemas.Transaction,
    responses={
        401: {"description": "Неверный токен транзакции."},
        403: {"description": "Изменение запрещено, уже не фаза подготовки."}
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
async def update_transaction(
        tx_uid: UUID4,
        tx_data: schemas.TransactionUpdate
) -> Any:
    """Изменить таймаут Транзакции."""

    # Проверяем что сервис работает в штатном режиме
    await check_service_mode()

    try:
        tx = await app.get_transaction(tx_uid.hex)
    except KeyError:
        return JSONResponse(status_code=status.HTTP_404_NOT_FOUND)

    tx_tx = Transaction(**tx.asdict())
    if tx_tx.tx_token != tx_data.tx_token and conf.AUTH_ENABLE and conf.CHECK_TX_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid transaction token",
        )

    if tx_tx.current_phase is not Phase.PREPARE:
        return JSONResponse(status_code=status.HTTP_403_FORBIDDEN)
    tx_tx.commit_timeout = (
                               tx_data.commit_timeout.total_seconds() if tx_data.commit_timeout else None) or conf.DEFAULT_COMMIT_TIMEOUT
    tx = await app.update_transaction(tx_tx)
    return tx


#    if tx.tx_token != tx_data.tx_token and conf.AUTH_ENABLE:
#        raise HTTPException(
#            status_code=status.HTTP_401_UNAUTHORIZED,
#            detail="Invalid transaction token",
#        )
#
#    if tx.current_phase is not Phase.PREPARE:
#        return JSONResponse(status_code=status.HTTP_403_FORBIDDEN)
#    tx.commit_timeout = tx_data.commit_timeout.total_seconds()
#    tx = await app.update_transaction(tx)
#    return tx


@router.post(
    "/{tx_uid}/party",
    summary="Добавить участника Транзакции.",
    response_model=schemas.TransactionParty,
    responses={
        208: {"description": "Такой участник уже добавлен."},
        401: {"description": "Неверный токен транзакции."},
        403: {"description": "Изменение запрещено, уже не фаза подготовки."}
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
async def add_party(
        tx_uid: UUID4,
        party_data: schemas.TransactionPartyCreate
) -> Any:
    """Добавить участника Транзакции."""
    try:
        tx = await app.get_transaction(tx_uid.hex)
    except KeyError:
        return JSONResponse(status_code=status.HTTP_404_NOT_FOUND)

    # if tx.tx_token != party_data.tx_token and conf.AUTH_ENABLE:
    #     raise HTTPException(
    #         status_code=status.HTTP_401_UNAUTHORIZED,
    #         detail="Invalid transaction token",
    #     )
    tx_tx = Transaction(**tx.asdict())
    if tx_tx.current_phase is not Phase.PREPARE:
        return JSONResponse(status_code=status.HTTP_403_FORBIDDEN)

    try:
        tx_party = await app.add_party(tx, party_data.url)
    # участник уже добавлен
    except PartyAlreadyAdded:
        return JSONResponse(status_code=208)

    message = create_log_message("Add party to transaction, tx_uid={}, party_url={}".format(tx_uid, tx_party.url),
                                 tx_uid=tx_uid, tx_party_url=tx_party.url)
    logger.info(message)
    # logger.info("Add party to transaction, tx_uid={}, party_url={}", tx_uid, tx_party.url)

    return TransactionParty(**tx_party.asdict())


@router.post(
    "/{tx_uid}/commit",
    summary="Инициировать коммит транзакции.",
    responses={
        202: {"description": "Успех операции. Коммит начнется, как только все участники дадут согласие."},
        208: {"description": "Команда на коммит уже была. Коммит начнется, как только все участники дадут согласие."},
        401: {"description": "Неверный токен транзакции."},
        403: {"description": "Операция запрещена, уже не фаза подготовки."},
        409: {"description": "Операция не может быть выполнена, так как уже решено, что транзакция отменяется."}
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
async def commit(tx_uid: UUID4, data: schemas.TransactionCommit) -> Any:
    """Инициировать коммит транзакции."""
    try:
        tx = await app.get_transaction(tx_uid.hex)
    except KeyError:
        return JSONResponse(status_code=status.HTTP_404_NOT_FOUND)

    if tx.tx_token != data.tx_token and conf.AUTH_ENABLE and conf.CHECK_TX_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid transaction token",
        )
    tx_tx = Transaction(**tx.asdict())

    if tx.consented_to_commit is False:
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content=f'Не получен консенсус на коммит. Причины: {tx_tx.rollback_reasons}'
        )

    if tx_tx.current_phase is not Phase.PREPARE:
        reasons = f'Причины: {tx_tx.rollback_reasons}' if tx_tx.rollback_reasons else ''
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=f'Транзакция уже не в фазе подготовки. {reasons}'
        )

    if tx.consented_to_commit is False:
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content=f'Не получен консенсус на коммит. Причины: {tx_tx.rollback_reasons}'
        )

    if tx.client_committed:
        return JSONResponse(status_code=208)

    tx.client_committed = True
    tx = await app.update_transaction(tx)
    return JSONResponse(status_code=status.HTTP_202_ACCEPTED)


@router.post(
    "/{tx_uid}/rollback",
    summary="Инициировать отмену транзакции.",
    responses={
        200: {"description": "Успех операции."},
        208: {"description": "Успех операции. Команда на отмену уже была или уже решено, что транзакция отменяется."},
        401: {"description": "Неверный токен транзакции."},
        403: {"description": "Операция запрещена, уже не фаза подготовки."},
        409: {"description": "Операция не может быть выполнена, так как уже была команда на коммит."}
    },
    dependencies=[Depends(required(roles=['modify_role']))]
)
async def rollback(tx_uid: UUID4, data: schemas.TransactionRollback) -> Any:
    """Инициировать отмену транзакции."""
    try:
        tx = await app.get_transaction(tx_uid.hex)
    except KeyError:
        return JSONResponse(status_code=status.HTTP_404_NOT_FOUND)

    if tx.tx_token != data.tx_token and conf.AUTH_ENABLE and conf.CHECK_TX_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid transaction token",
        )
    tx_tx = Transaction(**tx.asdict())
    if tx_tx.current_phase is not Phase.PREPARE:
        return JSONResponse(status_code=status.HTTP_403_FORBIDDEN)

    if tx_tx.client_committed:
        return JSONResponse(status_code=status.HTTP_409_CONFLICT)

    status_code = status.HTTP_200_OK
    if tx.client_committed is False or tx.consented_to_commit is False:
        status_code = 208

    tx.client_committed = False
    tx = await app.update_transaction(tx)
    return JSONResponse(status_code=status_code)
