from replication_manager.enums import ServiceMode


class ReplicationManagerException(Exception):
    """Базовый класс для ошибок в провайере цех"""


class ServiceModeException(ReplicationManagerException):
    def __init__(self, service_mode: ServiceMode):
        self.service_mode = service_mode

    def __str__(self) -> str:
        return f'Система в режиме {self.service_mode} - функция недоступна'


class ReplicationManagerInInvalidState(ReplicationManagerException):
    """Сервис репликации в инвалидном состоянии"""
    def __init__(self, description: str):
        self._description = description

    def __str__(self) -> str:
        return (f'Сервис репликации в инвалидном состоянии. {self._description}.')


class ReplicationManagerOperationFailed(ReplicationManagerException):
    """Ошибка репликации операции в S3"""
    def __init__(
            self,
            tx_uid: str,
            full_table_name: str,
            bucket_name: str,
            description: str,
    ):
        self._tx_uid = tx_uid
        self._full_table_name = full_table_name
        self._bucket_name = bucket_name
        self._description = description

    def __str__(self) -> str:
        return (
            f'Ошибка репликации операции по версии tx_uid: {self._tx_uid}, '
            f'{self._bucket_name}, table_name: {self._full_table_name}. '
            f'{self._description}'
        )
