from pydantic import <PERSON>g<PERSON>Dsn, validator
from typing import Any, Dict, Optional
from lib.config import Settings


class ReplManagerSettings(Settings):
    IDENT: str = 'replication-manager'
    TOPIC_CNT: str = '0'
    ID_SUFFIX: str = f'id-suffix-{TOPIC_CNT}'
    NODE_ID: str = 'REPL_1'
    KID: str = 'K1'

    SELF_BASE_URL: str = 'http://0.0.0.0:8013/'

    DB_DRIVER: str = 'postgresql'
    DB_HOST: str = 'pg_ceh'
    DB_PORT: int = 5432
    DB_USER: str = 'default'
    DB_PASSWORD: str = 'default'
    DB_DATABASE: str = 'ceh'
    DB_CONN_STR: Optional[PostgresDsn] = None
    DB_APP_NAME: Optional[str] = None
    DB_POOL_MIN_SIZE: int = 1
    DB_POOL_MAX_SIZE: int = 50
    DB_ECHO: bool = False
    DB_USE_CONNECTION_FOR_REQUEST: bool = True
    DB_RETRY_LIMIT: int = 1
    DB_RETRY_INTERVAL: int = 1

    S3_BUCKET: str = 'd5-dtpl-ssum'
    S3_REPL_CONCURRENCY: int = 1

    IS_DATASETS_FROM_RES: int = 0

    EXPORT_TO_S3_MAX_ATTEMPTS_COUNT: int = 3
    EXPORT_TO_S3_ATTEMPT_TIMEOUT: int = 30
    IMPORT_FROM_S3_MAX_ATTEMPTS_COUNT: int = 3
    IMPORT_FROM_S3_ATTEMPT_TIMEOUT: int = 30
    S3_MAX_ERROR_COUNT: int = 5

    @validator('DB_CONN_STR', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme='postgresql',
            host=values['DB_HOST'],
            port=str(values['DB_PORT']),
            user=values['DB_USER'],
            password=values['DB_PASSWORD'],
            path=f'/{values["DB_DATABASE"]}',
        )

    @validator('DB_APP_NAME', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_app_name(cls, _: Optional[str], values: Dict[str, Any]) -> str:
        ident = values['IDENT']
        version = values['SERVICE_VERSION']
        instance = values['INSTANCE']
        return f'SSUM {ident} {version} {instance}'

    class Config:
        case_sensitive = True


conf = ReplManagerSettings()
