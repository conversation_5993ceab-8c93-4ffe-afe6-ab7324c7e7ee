FROM centos/python-38-centos7

ENV PIP_DISABLE_PIP_VERSION_CHECK=1

USER root
WORKDIR /opt/app

RUN echo "sslverify=false" >> /etc/yum.conf

RUN \
    sed -i s/mirror.centos.org/vault.centos.org/g /etc/yum.repos.d/*.repo && \
    sed -i s/^#.*baseurl=http/baseurl=https/g /etc/yum.repos.d/*.repo && \
    sed -i s/^mirrorlist=http/#mirrorlist=https/g /etc/yum.repos.d/*.repo && \
    yum install -y epel-release  && \
    yum install -y libbsd

COPY requirements/model_manager.txt ./requirements.txt

RUN pip install --upgrade pip==24.0
RUN pip install -U setuptools wheel
RUN pip install Cython==0.29.28

RUN pip install --no-cache-dir --no-input -r requirements.txt

COPY dwh-services-utils dwh-services-utils/
COPY model_manager model_manager/
COPY lib lib/

RUN pip install --no-cache-dir --no-input --upgrade --no-deps --force-reinstall ./dwh-services-utils

COPY run.* ./
RUN chmod a+x ./run*

ENV PYTHONPATH=${PYTHONPATH}:.

EXPOSE 8000

CMD ./run.sh model_manager
