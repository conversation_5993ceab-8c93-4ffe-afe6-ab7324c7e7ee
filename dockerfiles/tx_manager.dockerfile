#FROM registry.redhat.io/rhel8/python-38:latest
FROM centos/python-38-centos7

ENV PIP_DISABLE_PIP_VERSION_CHECK=1

USER root

WORKDIR /opt

#RUN yum-config-manager --save --setopt=sslverify=false
RUN echo "sslverify=false" >> /etc/yum.conf

RUN \
    sed -i s/mirror.centos.org/vault.centos.org/g /etc/yum.repos.d/*.repo && \
    sed -i s/^#.*baseurl=http/baseurl=https/g /etc/yum.repos.d/*.repo && \
    sed -i s/^mirrorlist=http/#mirrorlist=https/g /etc/yum.repos.d/*.repo && \
    yum install -y epel-release && \
    yum repolist && \
    yum install -y \
      gflags \
      snappy snappy-devel \
      zlib zlib-devel \
      bzip2 bzip2-devel \
      lz4-devel \
      libasan \
      zstd \
      jq
#    yum localinstall -y ./lib/rocksdb/rocksdb-6.17.3-1.el7.x86_64.rpm

RUN pip install Cython==0.29.28
COPY lib lib/
RUN yum localinstall -y ./lib/rocksdb/rocksdb-6.17.3-1.el7.x86_64.rpm

ENV CPLUS_INCLUDE_PATH=${CPLUS_INCLUDE_PATH}:/opt/rocksdb/include
ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/opt/rocksdb
ENV LIBRARY_PATH=${LIBRARY_PATH}:/opt/rocksdb
ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/usr/local/lib
ENV CPATH=${CPATH}:/usr/local/include


WORKDIR /opt/app

COPY requirements/tx_manager.txt ./requirements.txt

RUN pip install --upgrade pip==24.0
RUN pip install -U setuptools wheel
RUN pip install Cython==0.29.28

RUN pip install --no-cache-dir --no-input -r requirements.txt

# COPY faust faust/
# RUN pip install --no-cache-dir --no-input -r faust/requirements/requirements.txt
# RUN pip install --no-cache-dir --no-input --upgrade --no-deps --force-reinstall ./faust

COPY dwh-services-utils dwh-services-utils/
RUN pip install --no-cache-dir --no-input --upgrade --no-deps --force-reinstall ./dwh-services-utils

COPY tx_manager tx_manager/
COPY sequence_generator sequence_generator/
COPY ceh_provider ceh_provider/
COPY lib lib/
COPY tests/tx_manager tests/
COPY run.sh ./run.sh
COPY .prospector.yaml .prospector.yaml
COPY scripts/wait-for-it.sh ./wait-for-it.sh

USER root
RUN chmod a+x ./run* ./wait-for-it.sh

ENV PYTHONPATH=${PYTHONPATH}:.

EXPOSE 8000

CMD ./run.sh tx_manager
