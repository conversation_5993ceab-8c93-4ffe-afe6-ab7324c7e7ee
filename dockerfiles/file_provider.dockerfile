#FROM registry.redhat.io/rhel8/python-38:latest
FROM centos/python-38-centos7

ENV PIP_DISABLE_PIP_VERSION_CHECK=1

USER root

WORKDIR /opt

RUN echo "sslverify=false" >> /etc/yum.conf

RUN \
    sed -i s/mirror.centos.org/vault.centos.org/g /etc/yum.repos.d/*.repo && \
    sed -i s/^#.*baseurl=http/baseurl=https/g /etc/yum.repos.d/*.repo && \
    sed -i s/^mirrorlist=http/#mirrorlist=https/g /etc/yum.repos.d/*.repo && \
    yum install -y epel-release && \
    yum repolist && \
    yum install -y \
      gflags \
      snappy snappy-devel \
      zlib zlib-devel \
      bzip2 bzip2-devel \
      lz4-devel \
      libasan \
      zstd \
      jq

RUN pip install Cython==0.29.28
COPY lib lib/
RUN yum localinstall -y ./lib/rocksdb/rocksdb-6.17.3-1.el7.x86_64.rpm

ENV CPLUS_INCLUDE_PATH=${CPLUS_INCLUDE_PATH}:/opt/rocksdb/include
ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/opt/rocksdb
ENV LIBRARY_PATH=${LIBRARY_PATH}:/opt/rocksdb
ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/usr/local/lib
ENV CPATH=${CPATH}:/usr/local/include

WORKDIR /opt/app

RUN pip install --upgrade pip==24.0
RUN pip install -U setuptools wheel
RUN pip install Cython==0.29.28

COPY requirements/file_provider.txt ./requirements.txt

RUN yum -y install krb5-workstation
RUN sed -i -e 's/#//' -e 's/default_ccache_name/# default_ccache_name/' /etc/krb5.conf

RUN pip install --no-cache-dir --no-input -r requirements.txt

COPY dwh-services-utils dwh-services-utils/

RUN pip install --no-cache-dir --no-input -e ./dwh-services-utils


COPY file_provider file_provider/
COPY conf conf/
COPY lib lib/
COPY openapi openapi/
COPY run.* ./
COPY tests/file_provider tests/
COPY scripts/wait-for-it.sh ./wait-for-it.sh

RUN chmod a+x ./run* ./wait-for-it.sh

ENV PYTHONPATH=${PYTHONPATH}:.

EXPOSE 8000

#CMD sleep 1000
