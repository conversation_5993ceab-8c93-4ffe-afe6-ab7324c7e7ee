#FROM registry.redhat.io/rhel8/python-38:latest
FROM centos/python-38-centos7

ENV PIP_DISABLE_PIP_VERSION_CHECK=1

USER root

WORKDIR /opt

RUN echo "sslverify=false" >> /etc/yum.conf

RUN \
    sed -i s/mirror.centos.org/vault.centos.org/g /etc/yum.repos.d/*.repo && \
    sed -i s/^#.*baseurl=http/baseurl=https/g /etc/yum.repos.d/*.repo && \
    sed -i s/^mirrorlist=http/#mirrorlist=https/g /etc/yum.repos.d/*.repo && \
    yum install -y epel-release && \
    yum repolist && \
    yum install -y \
      gflags \
      snappy snappy-devel \
      zlib zlib-devel \
      bzip2 bzip2-devel \
      lz4-devel \
      libasan \
      zstd \
      jq \
      bind-utils

RUN pip install Cython==0.29.28
COPY lib lib/
RUN yum localinstall -y ./lib/rocksdb/rocksdb-6.17.3-1.el7.x86_64.rpm

ENV CPLUS_INCLUDE_PATH=${CPLUS_INCLUDE_PATH}:/opt/rocksdb/include
ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/opt/rocksdb
ENV LIBRARY_PATH=${LIBRARY_PATH}:/opt/rocksdb
ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/usr/
ENV LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/usr/local/lib
ENV CPATH=${CPATH}:/usr/local/include


WORKDIR /opt/app

RUN yum install -y unixODBC
RUN yum install -y unixODBC-devel
RUN yum install -y mysql-connector-odbc.x86_64
RUN yum install -y postgresql-odbc.x86_64

RUN yum localinstall -y https://download.oracle.com/otn_software/linux/instantclient/215000/oracle-instantclient-basic-********.0-1.x86_64.rpm
RUN yum localinstall -y https://download.oracle.com/otn_software/linux/instantclient/215000/oracle-instantclient-odbc-********.0-1.x86_64.rpm
RUN yum localinstall -y http://package.mapr.com/tools/MapR-ODBC/MapR_Hive/MapRHive_odbc_2.6.11.1011/MapRHiveODBC-2.6.11.1011-1.x86_64.rpm
RUN export LD_LIBRARY_PATH=/usr/local/lib:/opt/mapr/hiveodbc/lib/64
# RUN curl https://packages.microsoft.com/config/rhel/7/prod.repo > /etc/yum.repos.d/mssql-release.repo
# RUN ACCEPT_EULA=Y yum install -y msodbcsql18

RUN echo -e "\n[OracleODBC-21.1] \nDescription = Oracle ODBC driver for Oracle \nDriver = /usr/lib/oracle/21/client64/lib/libsqora.so.21.1 \nFileUsage = 1 \nDriver Logging = 7" >> /etc/odbcinst.ini
RUN echo -e "\n[MapR Hive ODBC Connector 64-bit] \nDescription=MapR Hive ODBC Connector (64-bit) \nDriver=/opt/mapr/hiveodbc/lib/64/libmaprhiveodbc64.so " >> /etc/odbcinst.ini

RUN pip install --upgrade pip==24.0
RUN pip install -U setuptools wheel
RUN pip install Cython==0.29.28

COPY requirements/uni_provider.txt ./requirements.txt
RUN pip install --no-cache-dir --no-input -r requirements.txt

# COPY faust faust/
# RUN pip install --no-cache-dir --no-input -r faust/requirements/requirements.txt
# RUN pip install --no-cache-dir --no-input --upgrade --no-deps --force-reinstall ./faust

COPY dwh-services-utils dwh-services-utils/
RUN pip install --no-cache-dir --no-input -e ./dwh-services-utils

COPY uni_provider uni_provider/
COPY conf conf/
COPY lib lib/
COPY openapi openapi/
COPY run.* ./
COPY tests/uni_provider tests/
COPY scripts/wait-for-it.sh ./wait-for-it.sh

RUN chmod a+x ./run* ./wait-for-it.sh

ENV PYTHONPATH=${PYTHONPATH}:.

EXPOSE 8000

#CMD sleep 1000
