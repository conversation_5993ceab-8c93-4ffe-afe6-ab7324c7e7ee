FROM docker.repo-ci.sfera.inno.local/dtpl-docker/keycloak/keycloak:23.0.6-0

USER root

ADD ./_certs/ /etc/pki/ca-trust/source/anchors/

# Генерируем бандл сертификатов банка    
RUN mkdir -p /opt/keycloak/data/certs \
    && find /etc/pki/ca-trust/source/anchors/ -mindepth 1 | while read CERT; \
        do keytool -importcert -keystore /opt/keycloak/data/certs/trustedstore.jks \
            -file $CERT -alias $(basename $CERT) -storetype JKS \
            -storepass changeit -noprompt; \
        done

ENV JAVA_OPTS_APPEND="-Djavax.net.ssl.trustStore=/opt/keycloak/data/certs/trustedstore.jks -Djavax.net.ssl.trustStorePassword=changeit"

USER keycloak

