schema_version: 2.0
metadata:
  author: ILeonov
  version: "1.0"
  description: выгрузка из dm_t_dkkr_pflmb.dm_agreement_deliquency_mb
  tags:
    - "cf: cf_asrr_load_dm_agreement_deliquency_mb"
    - "wrk: wrk_asrr_load_dm_agreement_deliquency_mb"
  group: test
flows:
  - id: wrk_asrr_load_dm_agreement_deliquency_mb
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: поток
    metadata:
      - name: algorithm_uid
        type: in
        default: wrk_asrr_load_dm_agreement_deliquency_mb
      - name: instance_id
        datatype: int
        type: in
        default: 5000
      - name: target_resource_name
        type: in
        default: ceh.dm_t_dkkr_pflmb_out.agreement_deliquency_mb
      - name: source_resource_names
        type: in
        default:
          - ceh.dm_t_dkkr_pflmb_tech.vitr_mb_deal_creadit_overdue
      - name: actual_dttm
        type: in
        default: '1900-01-01'
      - name: prev_load_date
        type: in
        default: '1900-01-01'
      - name: target_schema_name
        type: in
        default: dm_t_dkkr_pflmb_out
      - name: target_table_name
        type: in
        default: dm_agreement_deliquency_mb
      - name: delta_schema_name
        type: in
        default: dm_t_dkkr_pflmb_dlt
      - name: common_version_id
        datatype: int
        type: in
        default: 100
        
      - name: max_loaded_version
        datatype: int
        type: in
        default: 0

    tasks:
      - id: check_dag_running
        description: Проверка состояние текущего дага
        type: ceh_core_idl.app.operators.core.check_dag_running
        
      - id: get_load_id # Шаг выделения уникального load_id. Шаг обязателен.
        description: Генерация уникального ключа потока
        type: ceh_core_idl.app.operators.etl.generate_load_id_operator
        metadata:
          - name: load_id
            datatype: str
            type: out
        properties:
          instance_id: ${instance_id}
          table_name: ${target_table_name}
        sequencer:
          dependencies:
            - ref: check_dag_running
        
      - id: open_transactions # Шаг открытия транзакции. Шаг обязателен
        description: открытие транзакции
        type: ceh_core_idl.app.operators.services.create_tx_operator
        metadata:
          - name: tx_uid
            datatype: str
            type: out
          - name: tx_token
            datatype: str
            type: out
        properties:
          tx_timeout: 15000
        sequencer:
          dependencies:
            - ref: get_load_id
        
      - id: block_resources # Шаг блокировки ресурса или группы ресурсов. Шаг обязателен
        description: блокировка ресурса
        type: ceh_core_idl.app.operators.services.locked_resources_operator
        metadata:
          - name: version_id
            datatype: int
            type: out
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          target_resource_names:
            - ${target_resource_name}
        sequencer:
          dependencies:
            - ref: open_transactions

      - id: insert_flow_data
        type: ceh_core_idl.app.operators.core.sql_operator
        properties:
          query: INSERT INTO dm_t_dkkr_pflmb_tech.fill_sources_stat
                        ( table_name, row_cnt, version_id, effective_date, load_start_dt, load_end_dt, src_cd, hash_diff)
                                values(
                                    '${target_table_name}', 0, '${block_resources.version_id}'::int8,
                                     decode('${actual_dttm}', '1900-01-01', current_date - 3::int, date '${actual_dttm}'),
                                     now(), null::timestamp, 'KIH', null::bpchar(32))
          result_flg: true
        sequencer:
          dependencies:
            - ref: block_resources

      - id: get_init_data #выгрузим наши данные
        description: выгрузка из источника acc_account
        type: ceh_core_idl.app.operators.etl.create_sql_proc_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          sql_select_statement: 
                            SELECT 
                                report_dt,
                                agreement_rk,
                                root_agreement_rk,
                                deal_id,
                                contract_id,
                                customer_tin,
                                customer_global_id,
                                customer_global_rk,
                                slx,
                                start_dt,
                                stop_dt,
                                overdue_pri_balance_rur_amt,
                                overdue_int_balance_rur_amt,
                                com_fine_pen_agg,
                                vitr_date,
                                load_datetime,
                                effective_from_date,
                                effective_to_date,
                                version_id,
                                deleted_flg,
                                'A' as record_mode                        
                            FROM dm_t_dkkr_pflmb.v_iv_all_dm_agreement_deliquency_mb(${max_loaded_version} + 1, 9223372036854775807)
          analyze_flg: true
          retry_qnt: 10
          result_dataset:
            schema: ${delta_schema_name}
            name: ${target_table_name}_${get_load_id.load_id}_dlt
            physical_options:
              distribute_type: column
              distribute_keys:
                - agreement_rk
                - customer_tin
                - deleted_flg
              materialize_flg: true
        sequencer:
          dependencies:
            - ref: insert_flow_data

      - id: get_dummy_data #выгрузим наши данные
        description: выгрузка из источника acc_account
        type: ceh_core_idl.app.operators.etl.create_sql_proc_operator
        metadata:
          - name: result_dataset
            datatype: dict
            type: out
        properties:
          sql_select_statement: 
                            SELECT *
                            FROM ${delta_schema_name}.${target_table_name}_${get_load_id.load_id}_dlt
                            WHERE 1 != 1
          analyze_flg: true
          retry_qnt: 10
          result_dataset:
            schema: ${delta_schema_name}
            name: ${target_table_name}_${get_load_id.load_id}
            physical_options:
              distribute_type: column
              distribute_keys:
                - agreement_rk
                - customer_tin
                - deleted_flg
              materialize_flg: true
        sequencer:
          dependencies:
            - ref: get_init_data

      - id: insert_flow_data_asrr
        type: ceh_core_idl.app.operators.core.sql_operator
        properties:
          query:        
                INSERT INTO ${target_schema_name}.${target_table_name} (
                            report_date,
                            agreement_rk,
                            root_agreement_rk,
                            deal_id,
                            contract_id,
                            customer_tin,
                            customer_global_id,
                            customer_global_rk,
                            slx,
                            start_dt,
                            stop_dt,
                            overdue_pri_balance_rur_amt,
                            overdue_int_balance_rur_amt,
                            com_fine_pen_agg,
                            vitr_date,
                            load_datetime,
                            effective_from_date,
                            effective_to_date,
                            version_id,
                            deleted_flg)
                        SELECT 
                            report_dt,
                            agreement_rk,
                            root_agreement_rk,
                            deal_id,
                            contract_id,
                            customer_tin,
                            customer_global_id,
                            customer_global_rk,
                            slx,
                            start_dt,
                            stop_dt,
                            overdue_pri_balance_rur_amt,
                            overdue_int_balance_rur_amt,
                            com_fine_pen_agg,
                            vitr_date,
                            load_datetime,
                            effective_from_date,
                            effective_to_date,
                            version_id,
                            deleted_flg
                        FROM ${delta_schema_name}.${target_table_name}_${get_load_id.load_id}_dlt
          result_flg: true
        sequencer:
          dependencies:
            - ref: get_dummy_data
            
      - id: update_resource_state # Шаг обновления состояния у целевого ресурса. Шаг обязателен. Закрытие транакци.
        description: Обновление состояния ресурса
        type: ceh_core_idl.app.operators.services.reg_delta_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          stable_version_id: ${common_version_id}
          target_resource_name: ${target_resource_name}
          target_table_names:
            - ${delta_schema_name}.${target_table_name}_${get_load_id.load_id}
          source_resource_names: ${source_resource_names}
          max_src_version_id: ${max_loaded_version}
          load_id: ${get_load_id.load_id}
          algorithm_uid: ${algorithm_uid}
          txi_table_name: ${delta_schema_name}.${target_table_name}_${get_load_id.load_id}
        sequencer:
          dependencies:
            - ref: insert_flow_data_asrr

      - id: update_flow_data
        type: ceh_core_idl.app.operators.core.sql_operator
        properties:
          query: UPDATE dm_t_dkkr_pflmb_tech.fill_sources_stat 
                       set row_cnt = (select count(*) from ${delta_schema_name}.${target_table_name}_${get_load_id.load_id}),
                           load_end_dt = now(),
                           hash_diff = md5(table_name||effective_date||load_start_dt||now())
                       where table_name = '${target_table_name}_${get_load_id.load_id}'
                             AND version_id = '${block_resources.version_id}'
                             AND effective_date = decode('${actual_dttm}', '1900-01-01', current_date - 3::int, date '${actual_dttm}')
                             AND load_end_dt is null
          result_flg: true
        sequencer:
          dependencies:
            - ref: update_resource_state
        
      - id: commit # Коммит целевого ресурса. Шаг обязателен
        description: Коммит операция
        type: ceh_core_idl.app.operators.services.commit_transaction_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          transaction_token: '${open_transactions.tx_token}'
        sequencer:
          dependencies:
            - ref: update_flow_data
        
      - id: error_occurred # Выявление ошибок выполнения дага. Шаг обязателен
        description: Любой успех
        type: airflow.operators.dummy.DummyOperator
        sequencer:
          dependencies:
            - ref: update_flow_data
              condition: ${not update_flow_data.status}
          condition:
            any
        
      - id: rollback # Выполнение роллбека транзакции. Шаг обязателен
        description: Ролбэк операция
        type: ceh_core_idl.app.operators.services.rollback_transaction_operator
        properties:
          transaction_uid: ${open_transactions.tx_uid}
          transaction_token: '${open_transactions.tx_token}'
        sequencer:
          dependencies:
            - ref: error_occurred
            - ref: open_transactions
          condition:
            all
