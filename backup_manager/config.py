from pydantic import <PERSON>gresDsn, validator
from typing import Any, Dict, Optional
from lib.config import Settings


class BackupManagerSettings(Settings):
    IDENT: str = 'backup-manager'
    TOPIC_CNT: str = '0'
    ID_SUFFIX: str = f'id-suffix-{TOPIC_CNT}'
    NODE_ID: str = 'BACKUP_1'
    ID_APP: str = f'id-app-{TOPIC_CNT}'
    KID: str = 'K1'

    SELF_BASE_URL: str = 'http://0.0.0.0:8014/'

    DB_DRIVER: str = 'postgresql'
    DB_HOST: str = 'pg_ceh'
    DB_PORT: int = 5432
    DB_USER: str = 'default'
    DB_PASSWORD: str = 'default'
    DB_DATABASE: str = 'ceh'
    DB_CONN_STR: Optional[PostgresDsn] = None
    DB_APP_NAME: Optional[str] = None
    DB_POOL_MIN_SIZE: int = 1
    DB_POOL_MAX_SIZE: int = 50
    DB_ECHO: bool = False
    DB_USE_CONNECTION_FOR_REQUEST: bool = True
    DB_RETRY_LIMIT: int = 1
    DB_RETRY_INTERVAL: int = 1

    DB_REMOTE_DRIVER: str = 'postgresql'
    DB_REMOTE_HOST: str = 'pg_ceh'
    DB_REMOTE_PORT: int = 5432
    DB_REMOTE_USER: str = 'default'
    DB_REMOTE_PASSWORD: str = 'default'
    DB_REMOTE_DATABASE: str = 'ceh'
    DB_REMOTE_CONN_STR: Optional[PostgresDsn] = None
    DB_REMOTE_APP_NAME: Optional[str] = None
    DB_REMOTE_POOL_MIN_SIZE: int = 1
    DB_REMOTE_POOL_MAX_SIZE: int = 50
    DB_REMOTE_ECHO: bool = False
    DB_REMOTE_USE_CONNECTION_FOR_REQUEST: bool = True
    DB_REMOTE_RETRY_LIMIT: int = 1
    DB_REMOTE_RETRY_INTERVAL: int = 1

    MAX_EXPORT_LAG: int = 30
    EXPORT_LAG_WAIT_TIMER: int = 120
    MAX_IMPORT_LAG: int = 30
    IMPORT_LAG_WAIT_TIMER: int = 120
    MAX_COPY_TO_TARGET_LAG: int = 30
    COPY_TO_TARGET_TIMER: int = 120

    S3_BUCKET: str = 'd5-dtpl-ssum'
    S3_HOST: str = 'https://k37-dv-ceph01.corp.dev.vtb'
    ACCESS_KEY: str = 'IPEYELPHEWZYAFJHSG4T7YZRCGWVM0'
    S3_SECRET_FILE_PATH: str = 'backup_manager/aws_secret.txt'
    S3_SECRET_KEY: str = None
    S3_SSL_VERIFY: int = 0

    PXF_SERVER_WRITABLE: str = 'k37-dv-ceph01_corp_dev_vtb'
    PXF_PROFILE_WRITABLE: str = 's3:parquet'
    PXF_FORMAT_WRITABLE: str = "'CUSTOM' (FORMATTER='pxfwritable_export')"
    PXF_OPTIONS_WRITABLE: str = 'COMPRESSION_CODEC=gzip&PARQUET_VERSION=v1'

    PXF_SERVER_READABLE: str = 'k37-dv-ceph01_corp_dev_vtb'
    PXF_FORMAT_READABLE: str = "'CUSTOM' (FORMATTER='pxfwritable_import')"

    COMPLEX_FIELD_TYPES: str = "'json', 'jsonb', 'uuid', 'time', 'time with time zone', 'time without time zone', " \
                               "'timestamp with time zone', 'timestamp(0) with time zone'"

    MATERIALIZE_CONCURRENCY: int = 2
    EXPORT_CONCURRENCY: int = 2
    IMPORT_CONCURRENCY: int = 2
    COPY_TO_TARGET_CONCURRENCY: int = 2

    IS_DATASETS_FROM_RES: int = 0

    SEQUENCE_FIELD_NAME: str = 'um$row_id'
    CACHE_SEQ_VALUE: int = 1000
    DEFAULT_STORAGE_MODE: str = 'appendonly=true,blocksize=32768,compresstype=zstd,compresslevel=1,orientation=column'
    RND_SUFFIX: int = 0
    RND_SUFFIX_LEN: int = 6
    BACKUP_SUFFIX: str = 'backup'
    BACKUP_SCHEMA: str = 'dbg'

    CHUNK_SIZE: int = 1_000_000_000

    @validator('DB_CONN_STR', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme='postgresql',
            host=values['DB_HOST'],
            port=str(values['DB_PORT']),
            user=values['DB_USER'],
            password=values['DB_PASSWORD'],
            path=f'/{values["DB_DATABASE"]}',
        )

    @validator('DB_REMOTE_CONN_STR', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_connection_remote(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme='postgresql',
            host=values['DB_REMOTE_HOST'],
            port=str(values['DB_REMOTE_PORT']),
            user=values['DB_REMOTE_USER'],
            password=values['DB_REMOTE_PASSWORD'],
            path=f'/{values["DB_REMOTE_DATABASE"]}',
        )

    @validator('DB_APP_NAME', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_app_name(cls, _: Optional[str], values: Dict[str, Any]) -> str:
        ident = values['IDENT']
        version = values['SERVICE_VERSION']
        instance = values['INSTANCE']
        return f'SSUM {ident} {version} {instance}'

    @validator('DB_REMOTE_APP_NAME', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_app_name_remote(cls, _: Optional[str], values: Dict[str, Any]) -> str:
        ident = values['IDENT']
        version = values['SERVICE_VERSION']
        instance = values['INSTANCE']
        return f'SSUM {ident} {version} {instance} remote'

    @validator('BACKUP_SUFFIX', pre=True)
    # pylint: disable=no-self-argument
    def assemble_db_suffix(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if values['RND_SUFFIX']:
            import random
            import string
            return ''.join(
                [random.choice(string.ascii_lowercase + string.digits) for _ in range(values['RND_SUFFIX_LEN'])]
            )
        return v

    @validator('S3_SECRET_KEY', pre=True)
    # pylint: disable=no-self-argument
    def assemble_s3_secret_key(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        if values['S3_SECRET_FILE_PATH']:
            try:
                with open(values['S3_SECRET_FILE_PATH']) as f:
                    return f.read()
            except FileNotFoundError:
                pass
        return ''

    class Config:
        case_sensitive = True


conf = BackupManagerSettings()
