from backup_manager.faust_worker.backup.s3worker import <PERSON><PERSON><PERSON><PERSON><PERSON>
from backup_manager.utils.gp_meta_data import GPMetaData
from lib.kafka.models.backup_manager import (
    ChunkBackupStatus,
    BackupStatus,
)
from lib.logging.utils import get_logger
from backup_manager.config import conf
from sqlalchemy import table, column
from pathlib import Path


logger = get_logger()


class S3Importer(S3Worker):

    def __init__(
            self, db_pool, base_schema_name, base_table_name, chunk_schema_name, chunk_table_name, chunk,
            backup_schema=conf.BACKUP_SCHEMA,
            pxf_server_readable=conf.PXF_SERVER_READABLE, pxf_format_readable=conf.PXF_FORMAT_READABLE,
            pxf_server_writable=conf.PXF_SERVER_WRITABLE,
            storage_params=conf.DEFAULT_STORAGE_MODE,
            *args, **kwargs
    ):
        super().__init__(db_pool, *args, **kwargs)
        self.backup_schema_name = backup_schema
        self.base_schema_name = base_schema_name
        self.base_table_name = base_table_name

        self.chunk_schema_name = chunk_schema_name
        self.chunk_table_name = chunk_table_name
        self.chunk = chunk

        self.storage_params = storage_params

        self.pxf_server_readable = pxf_server_readable
        self.pxf_format_readable = pxf_format_readable
        self.pxf_server_writable = pxf_server_writable

    async def import_iterator(self):
        async with self.db_pool.acquire() as conn:
            chunk_full_table_name = f'"{self.chunk_schema_name}"."{self.chunk_table_name}"'

            logger.info(
                f'Начало импорта из S3 очередного куска {self.base_schema_name}.{self.base_table_name} '
                f'под номером {self.chunk.number}'
            )

            base_s3_path = Path(self.base_schema_name) / self.base_table_name / self.chunk_table_name
            ext_schema_name = self.backup_schema_name
            ext_table_name = f'ext_{self.chunk_table_name}'
            location = self.chunk.pxf_location.replace(self.pxf_server_writable, self.pxf_server_readable)

            yield ChunkBackupStatus(
                number=self.chunk.number,
                rowcount=self.chunk.rowcount,
                pxf_location=self.chunk.pxf_location,
                base_full_table_name=f'{self.base_schema_name}.{self.base_table_name}',
                backup_status=BackupStatus.remote_processing
            )

            sql = f'DROP EXTERNAL TABLE IF EXISTS "{ext_schema_name}"."{ext_table_name}";'
            await self._execute_sql(conn, sql)

            sql = (
                f"CREATE EXTERNAL TABLE \"{ext_schema_name}\".\"{ext_table_name}\" (\n"
                f"  LIKE \"{self.base_schema_name}\".\"{self.base_table_name}\"\n"
                f")\n"
                f"LOCATION {location} ON ALL\n"
                f"FORMAT {self.pxf_format_readable}\n"
                "ENCODING 'utf-8';"
            )
            await self._execute_sql(conn, sql)

            sql = f'DROP TABLE IF EXISTS {chunk_full_table_name};'
            await self._execute_sql(conn, sql)
            sql = (
                f'CREATE TABLE {chunk_full_table_name} (LIKE "{self.base_schema_name}"."{self.base_table_name}")\n'
                f'WITH ({self.storage_params})'
            )
            await self._execute_sql(conn, sql)

            metadata = GPMetaData(gino_engine=self.db_pool)
            columns = await metadata.get_columns(self.base_schema_name, self.base_table_name)
            sa_columns_chunk = [column(c) for c in columns]
            sa_columns_ext = [column(c) for c in columns]

            chunk_table = table(self.chunk_table_name, *sa_columns_chunk, schema=self.chunk_schema_name)
            ext_table = table(ext_table_name, *sa_columns_ext, schema=ext_schema_name)
            statement = chunk_table.insert().from_select(columns, ext_table.select())

            status = await self._execute_sql(conn, str(statement))
            ver_count_list = status[0].split()
            rowcount = int(ver_count_list[2])

            logger.info(
                f'Из S3 успешно импортирован кусок {chunk_full_table_name} из таблицы '
                f'{self.base_schema_name}.{self.base_table_name} '
                f'под номером {self.chunk.number}. Кол-во строк: {rowcount}'
            )
            sql = f'DROP EXTERNAL TABLE "{ext_schema_name}"."{ext_table_name}";'
            await self._execute_sql(conn, sql)

            self._clear_s3_path(self.s3_bucket, base_s3_path)

            chunk_status = ChunkBackupStatus(
                number=self.chunk.number,
                rowcount=rowcount,
                base_full_table_name=f'{self.base_schema_name}.{self.base_table_name}',
                backup_status=BackupStatus.remote_success
            )
            yield chunk_status
