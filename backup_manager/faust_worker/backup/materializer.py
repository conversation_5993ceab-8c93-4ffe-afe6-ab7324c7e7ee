from backup_manager.utils.gp_meta_data import GPMetaData
from lib.kafka.models.backup_manager import (
    ChunkBackupStatus,
    BackupStatus
)
from sqlalchemy import table, column, and_
from lib.logging.utils import get_logger
from backup_manager.config import conf
from hashlib import md5
from math import ceil


logger = get_logger()


class MaterializeProcessor:
    """
    Класс для материализации таблицы перед ее загрузкой в S3

    Данная операция нужна, чтобы избежать ошибки по типу SocketTimeOutError, которые иногда выдает pxf
    при долгом вычислении записываемого датасета

    Сначала таблица пишется в такую же таблицу с доп полем (sequence)
    Потом большая таблица бробится на мелкие по доп полю (в раздробленные таблицы seq-поле уже не пишется)
    """

    def __init__(
            self, db_pool, base_schema_name, base_table_name,
            backup_schema=conf.BACKUP_SCHEMA, tmp_suffix=conf.BACKUP_SUFFIX,
            seq_field_name=conf.SEQUENCE_FIELD_NAME, cache_seq_value=conf.CACHE_SEQ_VALUE,
            storage_params=conf.DEFAULT_STORAGE_MODE, chunk_size=conf.CHUNK_SIZE,
            unsupported_types=conf.COMPLEX_FIELD_TYPES
    ):
        self.base_schema_name = base_schema_name
        self.base_table_name = base_table_name

        self.db_pool = db_pool
        self.metadata = GPMetaData(gino_engine=self.db_pool)

        self.columns = []

        self.backup_schema_name = backup_schema
        table_hash = md5(f'{base_schema_name}_{base_table_name}_{tmp_suffix}'.encode('UTF-8')).hexdigest()
        self.backup_table_name = f"backup_{table_hash}"

        self.storage_params = storage_params
        self.chunk_size = chunk_size
        self.seq_field_name = seq_field_name
        self.cache_seq_value = cache_seq_value
        self.sequence_name = f'{self.backup_table_name}_seq'

        self.unsupported_types = tuple([
            t.strip().replace("'", '').replace('"', '')
            for t in unsupported_types.split(',')
        ])

    async def alter_unsupported_pxf_types(self, conn):
        columns = await self.metadata.get_complex_field(
            self.backup_schema_name, self.backup_table_name, self.unsupported_types
        )
        columns = [c[0] for c in columns]
        for col in columns:
            await self._execute_sql(
                conn,
                f'ALTER TABLE "{self.backup_schema_name}"."{self.backup_table_name}" '
                f'ALTER COLUMN {col} TYPE text USING {col}::text;'
            )

    async def materialize(self):
        """Дробит таблицу на примерно равные части по CHUNK_SIZE строк.

        В качестве результата возвращает словарь {chunk_full_table_name: ChunkBackupStatus ...}
        """
        async with self.db_pool.acquire() as conn:
            logger.info(f'Начало материализации {self.base_schema_name}.{self.base_table_name}')
            await self._get_columns()

            rowcount = await self._materialize_big_table(conn)
            logger.info(
                f'1 этап материализации закончен {self.base_schema_name}.{self.base_table_name}. '
                f'Записано: {rowcount} строк'
            )

            status = await self._execute_sql(
                conn, f'SELECT last_value FROM {self.backup_schema_name}.{self.sequence_name};'
            )
            # Пример возвращаемой структуры: ('SELECT 1', [(123,)])
            last_seq_value = status[1][0][0]

            chunks_stat = await self._divide_big_table(conn, last_seq_value)
            logger.info(f'2 этап материализации закончен {self.base_schema_name}.{self.base_table_name}')
            await self._execute_sql(
                conn,
                f'DROP TABLE IF EXISTS "{self.backup_schema_name}"."{self.backup_table_name}";'
            )

        chunks_objects = {
            f'{backup_schema}.{chunk_table_name}': ChunkBackupStatus(
                number=ind,
                rowcount=rowcount,
                backup_status=BackupStatus.local_success,
                base_full_table_name=f'{self.base_schema_name}.{self.base_table_name}',
            ) for ind, rowcount, backup_schema, chunk_table_name in chunks_stat
        }
        return chunks_objects

    async def _get_columns(self):
        """Заполняет поле self.columns для дальнейшего переиспользования"""
        self.columns = await self.metadata.get_columns(self.base_schema_name, self.base_table_name)

    async def _execute_sql(self, conn, sql):
        """Выполняет sql и возвращает результат"""
        return await conn.status(self.db_pool.text(sql))

    async def _materialize_big_table(self, conn):
        """1 этап материализации - запись всей таблицы в другую таблицу с sequence полем"""
        await self._execute_sql(conn, f'DROP TABLE IF EXISTS "{self.backup_schema_name}"."{self.backup_table_name}";')
        await self._execute_sql(
            conn,
            f'CREATE TABLE "{self.backup_schema_name}"."{self.backup_table_name}" '
            f'(LIKE "{self.base_schema_name}"."{self.base_table_name}")'
            f'WITH ({self.storage_params});'
        )
        await self._execute_sql(
            conn,
            f'ALTER TABLE "{self.backup_schema_name}"."{self.backup_table_name}" '
            f'ADD COLUMN "{self.seq_field_name}" int8;'
        )
        await self._execute_sql(
            conn,
            f'CREATE SEQUENCE "{self.backup_schema_name}"."{self.sequence_name}" '
            f'CACHE {self.cache_seq_value} '
            f'OWNED BY "{self.backup_schema_name}"."{self.backup_table_name}"."{self.seq_field_name}";'
        )
        await self._execute_sql(
            conn,
            f'ALTER TABLE "{self.backup_schema_name}"."{self.backup_table_name}" ALTER COLUMN "{self.seq_field_name}" '
            f'SET DEFAULT nextval(\'{self.backup_schema_name}.{self.sequence_name}\');'
        )

        src_table = table(self.base_table_name, *[column(c) for c in self.columns], schema=self.base_schema_name)
        bak_table = table(self.backup_table_name, *[column(c) for c in self.columns], schema=self.backup_schema_name)
        statement = bak_table.insert().from_select(self.columns, src_table.select())
        statement = statement.compile(compile_kwargs={"literal_binds": True})
        status = await self._execute_sql(conn, str(statement))

        ver_count_list = status[0].split()
        rowcount = int(ver_count_list[2])

        await self.alter_unsupported_pxf_types(conn)

        return rowcount

    async def _divide_big_table(self, conn, last_seq_value):
        """2 этап материализации - дробление большой таблицы на мелкие"""
        # Генерируем наборы для границ по типу (0-1_000_000_000, 1_000_000_000-2_000_000_000, ...)
        intervals = [
            (self.chunk_size * i, self.chunk_size * (i + 1)) for i in range(ceil(last_seq_value / self.chunk_size))
        ]
        logger.info(f'{self.base_schema_name}.{self.base_table_name} будет разделена на {len(intervals)} кусков')

        seq_column_sa = column(self.seq_field_name)
        chunks = []
        for ind, (start_interval, end_interval) in enumerate(intervals):
            logger.info(f'Начало {self.base_schema_name}.{self.base_table_name} записи {ind} куска')
            chunk_table_name = f'{self.backup_table_name}_{ind}'
            await self._execute_sql(conn, f'DROP TABLE IF EXISTS "{self.backup_schema_name}"."{chunk_table_name}";')
            await self._execute_sql(
                conn,
                f'CREATE TABLE "{self.backup_schema_name}"."{chunk_table_name}" '
                f'(LIKE "{self.backup_schema_name}"."{self.backup_table_name}");'
            )
            await self._execute_sql(
                conn,
                f'ALTER TABLE "{self.backup_schema_name}"."{chunk_table_name}" DROP COLUMN "{self.seq_field_name}";'
            )

            src_table = table(
                self.backup_table_name, *[column(c) for c in self.columns], schema=self.backup_schema_name
            )
            chunk_table = table(
                chunk_table_name, *[column(c) for c in self.columns], schema=self.backup_schema_name
            )
            statement = chunk_table.insert().from_select(
                self.columns,
                src_table.select().where(
                    and_(seq_column_sa > start_interval, seq_column_sa <= end_interval)
                )
            )
            statement = statement.compile(compile_kwargs={"literal_binds": True})
            status = await conn.status(self.db_pool.text(str(statement)))
            ver_count_list = status[0].split()
            rowcount = int(ver_count_list[2])
            if rowcount == 0:
                logger.info(f'Кусок данных №{ind} объекта {self.base_schema_name}.{self.base_table_name} '
                            f'не содержит данных')
                continue

            chunks.append((ind, rowcount, self.backup_schema_name, chunk_table_name))
            logger.info(f'Успешно записан {ind} кусок объекта {self.base_schema_name}.{self.base_table_name}')
        return chunks
