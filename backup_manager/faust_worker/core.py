from backup_manager.faust_worker import get_faust_app, concurrency_dict
from backup_manager.faust_worker.commands import (
    StopReplAgentParams,
    AppendOrUpdateParams,
    GetTableItemsParams,
    GetMetricsParams,
    GetParams,
)
from backup_manager.faust_worker.agents import (
    single_partition_commands_consumer,
    commands_consumer,
    push_backup_request_to_queue_agent,
    update_backup_status_agent,
    get_topic_queue_length,
)
from backup_manager.faust_worker.topics import (
    _export_queue,
    _import_queue,
    _materialize_request_queue,
)
from lib.kafka.models.backup_manager import (
    BackupRequest,
    BackupRequestItem,
    TableBackupStatus,
    BackupStatus,
    ChunkBackupStatus,
)
from lib.tracing.utils import get_session_headers
from lib.logging.utils import get_logger
from typing import List


logger = get_logger()
faust_app = get_faust_app()


async def get_metrics_command():
    """ Возвращает метрики сервиса """
    return await commands_consumer.ask(
        headers=get_session_headers(),
        key='status',
        value=GetMetricsParams(),
    )


async def stop_repl_agent_command():
    return await commands_consumer.ask(
        headers=get_session_headers(),
        key='stop_agent',
        value=StopReplAgentParams(),
    )


async def get_command(table, key):
    return await commands_consumer.ask(
        headers=get_session_headers(),
        key=key,
        value=GetParams(
            table=table,
            key=key,
        )
    )


async def get_table_items_command(table_name: str):
    return await commands_consumer.ask(
        headers=get_session_headers(),
        value=GetTableItemsParams(
            table=table_name,
        )
    )


async def add_command_single(table_name, key, value):
    return await single_partition_commands_consumer.ask(
        headers=get_session_headers(),
        key=key,
        value=AppendOrUpdateParams(
            table=table_name,
            key=key,
            value=value,
        )
    )


async def push_request_to_queue(backup_request):
    value = BackupRequest(
        backup_items=[BackupRequestItem(schema_name=r.schema_name, table_name=r.table_name) for r in backup_request]
    )
    return await push_backup_request_to_queue_agent.ask(value=value)


async def get_metrics():
    """Возвращает метрики сервиса"""
    logger.info('Запрос метрик сервиса бэкапа')
    instance_states = {}
    for topic_name, events in concurrency_dict.items():
        instance_states[topic_name] = {}
        for concurrency_id, event in events.items():
            instance_states[topic_name][concurrency_id] = {}
            if event['final']:
                instance_states[topic_name][concurrency_id] = 'final'
            elif event['stream'].state == 'running':
                instance_states[topic_name][concurrency_id] = 'running'
            else:
                instance_states[topic_name][concurrency_id] = 'stopping'
    return {
        'export_queue_length': await get_topic_queue_length(_export_queue),
        'import_queue_length': await get_topic_queue_length(_import_queue),
        'materialize_request_queue_length': await get_topic_queue_length(_materialize_request_queue),
        'instance_states': instance_states
    }


async def stop_repl_agent():
    """Останавливает все прослушивания топиков, зарегистрированных в concurrency_dict"""
    for topic_name, events in concurrency_dict.items():
        logger.warning(f'Остановка прослушивания {topic_name}')
        for concurrency_id, event in events.items():
            await event['stream'].stop()


async def cancel_backup():
    backup_status = await get_table_items_command('_backup_status_t')
    for table_name, status in backup_status.items():
        await update_backup_status_agent.cast(
            key=table_name,
            value=TableBackupStatus(
                backup_status=BackupStatus.cancel_status
            )
        )


async def get_backup_status():
    """ Возвращает статус бэкапа """
    backup_status: List = await get_table_items_command('_backup_status_t')
    backup_response_status = dict()
    backup_response_status['total_items'] = len(backup_status)
    backup_response_status['errors'] = dict()
    backup_response_status['backup_status'] = dict()

    for table_name, status in backup_status:
        if isinstance(status, dict):
            status = TableBackupStatus(**status)

        if status.details:
            backup_response_status['errors'][table_name] = status.details

        backup_response_status['backup_status'].setdefault(str(status.backup_status), 0)
        backup_response_status['backup_status'][str(status.backup_status)] += 1

    return backup_response_status


async def get_backup_object_status(schema_name, table_name):
    """ Возвращает статус бэкапа объекта """

    backup_status: TableBackupStatus = await get_command('_backup_status_t', f'{schema_name}.{table_name}')
    if not backup_status:
        return

    chunk_statuses = []
    if backup_status.chunks:
        for chunk_name in backup_status.chunks:
            backup_chunk: ChunkBackupStatus = await get_command('_chunk_backup_status_t', chunk_name)
            chunk_statuses.append(backup_chunk.asdict())

    return {
        'backup_status': backup_status.backup_status,
        'backup_chunks': chunk_statuses,
        'details': backup_status.details if backup_status.details else ''
    }

