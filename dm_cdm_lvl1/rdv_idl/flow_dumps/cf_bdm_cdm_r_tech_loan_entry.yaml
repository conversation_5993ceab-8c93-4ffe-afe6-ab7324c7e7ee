# autogenerated yaml
schema_version: 2.0
metadata:
  author: rromashov
  version: '1.0'
  group: general_ledger
  description: Управляющий поток cf_bdm_cdm_r_tech_loan_entry
  tags:

    - 'team: zi12'
    - 'cf'
    - 'area: dm_cdm'
    - 'src: ceh'
    - 'prv: bdm'
    - 'tgt: dm_cdm_tech'
    - 'rls: 26'
    - 'cf: cf_bdm_cdm_r_tech_loan_entry'
    - 'wrk: wrk_bdm_cdm_r_tech_loan_entry'
    - 'tgt-tbl: r_tech_loan_entry'
    - 'PIONRR6-334'
    - 'update_dttm: 2023-11-15 19:00'
  imports:
    - ceh_frm_idl_utils.source_resourse_transfer
flows:
  - id: cf_bdm_cdm_r_tech_loan_entry
    builder: ceh_core_idl.app.builders.simple_flow_builder
    description: Управляющий поток cf_bdm_cdm_r_tech_loan_entry
    metadata:
      - name: target_resource_name_r_tech_loan_entry
        default: ceh.dm_cdm_tech.r_tech_loan_entry
      - name: source_resource_names
        default:

          - ceh.idl.bbridge_entry.cftm
          - ceh.idl.bbridge_entry
          - ceh.idl.bbridge_entry.kih
          - ceh.idl.bbridge_entry.rtll
          - ceh.idl.bbridge_entry.rtls
          - ceh.idl.bbridge_entry.wayn
          - ceh.idl.bbridge_entry.wfbm
          - ceh.idl.blink_deal_operation_x_entry.cftm
          - ceh.idl.blink_deal_operation_x_entry
          - ceh.idl.loan_deal
          - ceh.idl.bbridge_loan_deal
          - ceh.idl.bbridge_loan_deal.cftm
          - ceh.idl.bbridge_loan_deal.dm_cftm
          - ceh.idl.bbridge_loan_deal.dm_rtll
          - ceh.idl.bbridge_loan_deal.rtll
          - ceh.idl.bbridge_deal_operation.cftm
          - ceh.idl.bbridge_deal_operation.dm_cftm
          - ceh.idl.bbridge_deal_operation
          - ceh.idl.bbridge_deal_operation.rtls
          - ceh.idl.bbridge_deal_operation.zfnt
          - ceh.idl.account.acc_bal_wayn
          - ceh.idl.account.acc_bal_wayn_wvtb
          - ceh.idl.account.acc_x_acc_wayn
          - ceh.idl.account.acc_x_cntrp_wayn
          - ceh.idl.account.acc_x_dprt_wayn
          - ceh.idl.account.cftm
          - ceh.idl.account.dm_cftm
          - ceh.idl.account.dm_rtll
          - ceh.idl.account.dm_rtls
          - ceh.idl.account.dm_wayn
          - ceh.idl.account.dm_wayn_s44
          - ceh.idl.account.dm_wayn_wvtb
          - ceh.idl.account
          - ceh.idl.account.rtll
          - ceh.idl.account.rtll_dm
          - ceh.idl.account.rtls
          - ceh.idl.account.wayn
          - ceh.idl.account.wayn_s44
          - ceh.idl.account.wayn_wvtb
          - ceh.idl.account.wayn_wvtb_dm
          - ceh.idl.account.wfbm
          - ceh.idl.bbridge_document.cftm
          - ceh.idl.bbridge_document.dm_cftm
          - ceh.idl.bbridge_document.dm_rtll
          - ceh.idl.bbridge_document.dm_rtls
          - ceh.idl.bbridge_document.rtll
          - ceh.idl.bbridge_document.rtls
          - ceh.idl.document
          - ceh.idl.currency
          - ceh.idl.bbridge_card_transactions.wayn          
          
          - ceh.dm_common_tech.r_tech_account_role

    tasks:
      - id: check_available_resources_in_ceh_provider_r_tech_loan_entry
        type: ceh_core_idl.app.operators.services.get_resources_service_operator
        description: Проверка доступности ресурсов (источников и приемник) в провайдере
          ресурсов
        properties:
          target_resource_name: ${target_resource_name_r_tech_loan_entry}
          source_resource_names: ${source_resource_names}
      - id: get_common_version
        type: ceh_core_idl.app.operators.services.common_version_operator
        description: Получение общей стабильной версии
        properties:
          resources: ${source_resource_names}
        sequencer:
          dependencies:
            - ref: check_available_resources_in_ceh_provider_r_tech_loan_entry
      - id: get_resources_versions_r_tech_loan_entry
        type: ceh_core_idl.app.operators.services.get_max_loaded_version_operator
        description: Получение максимальной загруженной версии в целевой ресурс r_tech_loan_entry
        properties:
          target_resource_name: ${target_resource_name_r_tech_loan_entry}
          source_resource_names: ${source_resource_names}
          stable_version: ${get_common_version.common_version}
          algo_name: default_algo
          debug_mode: true
          condition: any
        sequencer:
          dependencies:
            - ref: get_common_version
      - id: get_workflow_parameters
        type: ceh_core_idl.app.operators.basic.workflow_parameters_operator
        description: Определение основных параметров рабочего потока
        properties:
          version_parameters:
            - ${get_resources_versions_r_tech_loan_entry}
          stable_version: ${get_common_version.common_version}
        sequencer:
          dependencies:
            - ref: get_resources_versions_r_tech_loan_entry
      - id: get_metrics
        builder: ceh_core_idl.app.builders.include_flow_builder
        description: Приведение ресурсов к заданному виду для переноса в последующий
          слой
        properties:
          ref: source_resourse_transfer
          properties:
            source_resource_names: ${source_resource_names}
        sequencer:
          dependencies:
            - ref: get_workflow_parameters
              condition: $(get_resources_versions_r_tech_loan_entry.status and get_resources_versions_r_tech_loan_entry.src_version_check_status)
      - id: trigger_wrk
        type: ceh_core_idl.app.operators.core.trigger_dag_operator
        description: Запуск рабочего потока 
        properties:
          trigger_dag_id: wrk_bdm_cdm_r_tech_loan_entry
          conf:
            instance_id: ${get_workflow_parameters.instance_id}
            src_version_id: ${get_workflow_parameters.src_version_id}
            stable_version_id: ${get_workflow_parameters.stable_version_id}
            init_load_flg: ${get_workflow_parameters.init_load_flg}
            algorithm_name: default_algo
            by_src: ${get_metrics.result_data}
        sequencer:
          dependencies:
            - ref: get_metrics
